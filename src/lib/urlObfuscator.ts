/**
 * Utilitário para ocultar URLs sensíveis
 * 
 * Este módulo fornece funções para ocultar URLs sensíveis
 * sem afetar a funcionalidade do site.
 */

// Lista de domínios sensíveis
const SENSITIVE_DOMAINS = [
  'animefire.plus',
  'animefire.net',
  '15.229.117.80',
  'api-animefire',
  'anime-fire',
  'animefire'
];

/**
 * Verifica se uma URL contém domínios sensíveis
 * @param url URL a ser verificada
 * @returns true se a URL contém domínios sensíveis
 */
export function containsSensitiveDomain(url: string): boolean {
  if (!url) return false;
  
  return SENSITIVE_DOMAINS.some(domain => 
    url.toLowerCase().includes(domain.toLowerCase())
  );
}

/**
 * Oculta uma URL sensível para exibição
 * @param url URL a ser ocultada
 * @returns URL ocultada
 */
export function obfuscateUrl(url: string): string {
  if (!url) return url;
  
  let result = url;
  
  // Substituir domínios sensíveis
  SENSITIVE_DOMAINS.forEach(domain => {
    const regex = new RegExp(`https?://[^/]*${domain}[^/]*`, 'gi');
    result = result.replace(regex, 'https://source.domain');
  });
  
  // Ocultar caminhos específicos
  result = result.replace(/\/animes\/|\/video\/|\/api\?/gi, '/****/');
  
  return result;
}

/**
 * Oculta uma chave de API sensível
 * @param apiKey Chave de API a ser ocultada
 * @returns Chave de API ocultada
 */
export function obfuscateApiKey(apiKey: string): string {
  if (!apiKey) return apiKey;
  
  // Mostrar apenas os primeiros e últimos 4 caracteres
  const firstFour = apiKey.substring(0, 4);
  const lastFour = apiKey.substring(apiKey.length - 4);
  
  return `${firstFour}...${lastFour}`;
}

/**
 * Criptografa uma URL sensível para uso interno
 * Esta função não altera a URL, apenas a codifica para dificultar a leitura
 * @param url URL a ser criptografada
 * @returns URL criptografada
 */
export function encryptSensitiveUrl(url: string): string {
  if (!url) return url;
  
  // Codificar a URL em base64
  if (typeof window !== 'undefined') {
    return btoa(url);
  }
  
  // No servidor, usar Buffer
  return Buffer.from(url).toString('base64');
}

/**
 * Descriptografa uma URL sensível
 * @param encryptedUrl URL criptografada
 * @returns URL original
 */
export function decryptSensitiveUrl(encryptedUrl: string): string {
  if (!encryptedUrl) return encryptedUrl;
  
  try {
    // Decodificar a URL de base64
    if (typeof window !== 'undefined') {
      return atob(encryptedUrl);
    }
    
    // No servidor, usar Buffer
    return Buffer.from(encryptedUrl, 'base64').toString();
  } catch (error) {
    // Se não for uma string base64 válida, retornar a original
    return encryptedUrl;
  }
}
