'use client'

import { useRef, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import dynamic from 'next/dynamic'
import '@/styles/plyr-custom.css'

// Importar Plyr dinamicamente apenas no lado do cliente
const Plyr = dynamic(() => import('plyr'), {
  ssr: false
})

interface VideoQuality {
  label: string
  url: string
  resolution: string
}

interface ModernVideoPlayerProps {
  videoUrl: string
  poster?: string
  title?: string
  className?: string
  qualities?: VideoQuality[]
  onNextEpisode?: () => void
  episodeId?: string
  animeSlug?: string
}

export default function ModernVideoPlayer({
  videoUrl,
  poster,
  title = 'Episódio de Anime',
  className = '',
  qualities = [],
  onNextEpisode,
  episodeId,
  animeSlug
}: ModernVideoPlayerProps) {
  const { data: session } = useSession()
  const videoRef = useRef<HTMLVideoElement>(null)
  const playerRef = useRef<Plyr | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [videoError, setVideoError] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [currentVideoUrl, setCurrentVideoUrl] = useState(videoUrl)
  const [hasMarkedAsWatched, setHasMarkedAsWatched] = useState(false)
  const [savedTime, setSavedTime] = useState(0)
  const [lastSaveTime, setLastSaveTime] = useState(0)
  const saveInterval = 15000 // Salvar a cada 15 segundos

  // Determina o tipo de vídeo com base na URL
  const isBloggerVideo = videoUrl.includes('blogger.com') || videoUrl.includes('blogspot.com')
  const isDirectMp4 = videoUrl.endsWith('.mp4') ||
                     videoUrl.includes('lightspeedst.net') ||
                     videoUrl.includes('/mp4/') ||
                     videoUrl.includes('/mp4_temp/')

  // Inicializar o player Plyr
  useEffect(() => {
    if (!videoRef.current || typeof window === 'undefined') return

    // Configurações do Plyr
    const plyrOptions = {
      controls: [
        'play-large', // Botão de play grande no centro
        'play', // Botão de play/pause
        'progress', // Barra de progresso
        'current-time', // Tempo atual
        'duration', // Duração total
        'mute', // Botão de mudo
        'volume', // Controle de volume
        'captions', // Legendas (se disponíveis)
        'settings', // Configurações (qualidade, velocidade)
        'pip', // Picture-in-picture
        'airplay', // Suporte para AirPlay
        'fullscreen', // Tela cheia
      ],
      i18n: {
        restart: 'Reiniciar',
        rewind: 'Voltar {seektime}s',
        play: 'Reproduzir',
        pause: 'Pausar',
        fastForward: 'Avançar {seektime}s',
        seek: 'Buscar',
        seekLabel: '{currentTime} de {duration}',
        played: 'Reproduzido',
        buffered: 'Carregado',
        currentTime: 'Tempo atual',
        duration: 'Duração',
        volume: 'Volume',
        mute: 'Silenciar',
        unmute: 'Ativar som',
        enableCaptions: 'Ativar legendas',
        disableCaptions: 'Desativar legendas',
        download: 'Baixar',
        enterFullscreen: 'Entrar em tela cheia',
        exitFullscreen: 'Sair da tela cheia',
        frameTitle: 'Player para {title}',
        captions: 'Legendas',
        settings: 'Configurações',
        menuBack: 'Voltar ao menu anterior',
        speed: 'Velocidade',
        normal: 'Normal',
        quality: 'Qualidade',
        loop: 'Repetir',
        start: 'Início',
        end: 'Fim',
        all: 'Todos',
        reset: 'Redefinir',
        disabled: 'Desativado',
        enabled: 'Ativado',
        advertisement: 'Anúncio',
        qualityBadge: {
          2160: '4K',
          1440: 'HD',
          1080: 'HD',
          720: 'HD',
          576: 'SD',
          480: 'SD',
        },
      },
      quality: {
        default: 'auto',
        options: qualities.length > 0 ? qualities.map(q => q.resolution) : ['auto'],
      },
      tooltips: { controls: true, seek: true },
      keyboard: { focused: true, global: true },
      blankVideo: '',
      autoplay: false,
      clickToPlay: true,
      disableContextMenu: false,
      hideControls: true,
      resetOnEnd: false,
      invertTime: false,
      toggleInvert: false,
      fullscreen: { enabled: true, fallback: true, iosNative: true },
      speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2] },
      seekTime: 10,
      volume: 1,
      muted: false,
      storage: { enabled: true, key: 'plyr' },
    }

    // Inicializar o player
    if (!playerRef.current) {
      try {
        // Verificar se o Plyr está disponível
        if (typeof Plyr === 'function') {
          // Inicializar o player com um pequeno atraso para garantir que o DOM esteja pronto
          setTimeout(() => {
            try {
              playerRef.current = new Plyr(videoRef.current, plyrOptions)

              // Eventos do player
              playerRef.current.on('ready', () => {
                setIsLoading(false)
                loadWatchProgress()
              })

              playerRef.current.on('play', () => {
                if (videoRef.current && videoRef.current.currentTime > 0) {
                  setIsLoading(false)
                }
              })

              playerRef.current.on('timeupdate', () => {
                handleTimeUpdate()
              })

              playerRef.current.on('ended', () => {
                handleEnded()
              })

              playerRef.current.on('error', () => {
                handleVideoError()
              })
            } catch (innerError) {
              console.error('Erro ao inicializar o player Plyr (tentativa interna):', innerError)
              setIsLoading(false)

              // Fallback para player nativo em caso de erro
              if (videoRef.current) {
                videoRef.current.controls = true
                videoRef.current.addEventListener('loadeddata', () => setIsLoading(false))
                videoRef.current.addEventListener('timeupdate', handleTimeUpdate)
                videoRef.current.addEventListener('ended', handleEnded)
                videoRef.current.addEventListener('error', handleVideoError)
              }
            }
          }, 100)
        } else {
          console.warn('Plyr não está disponível como uma função')
          setIsLoading(false)

          // Fallback para player nativo
          if (videoRef.current) {
            videoRef.current.controls = true
            videoRef.current.addEventListener('loadeddata', () => setIsLoading(false))
            videoRef.current.addEventListener('timeupdate', handleTimeUpdate)
            videoRef.current.addEventListener('ended', handleEnded)
            videoRef.current.addEventListener('error', handleVideoError)
          }
        }
      } catch (error) {
        console.error('Erro ao inicializar o player Plyr:', error)
        setIsLoading(false)

        // Fallback para player nativo em caso de erro
        if (videoRef.current) {
          videoRef.current.controls = true
          videoRef.current.addEventListener('loadeddata', () => setIsLoading(false))
          videoRef.current.addEventListener('timeupdate', handleTimeUpdate)
          videoRef.current.addEventListener('ended', handleEnded)
          videoRef.current.addEventListener('error', handleVideoError)
        }
      }
    }

    // Limpar o player quando o componente for desmontado
    return () => {
      // Remover event listeners do player nativo (fallback)
      if (videoRef.current) {
        videoRef.current.removeEventListener('loadeddata', () => setIsLoading(false))
        videoRef.current.removeEventListener('timeupdate', handleTimeUpdate)
        videoRef.current.removeEventListener('ended', handleEnded)
        videoRef.current.removeEventListener('error', handleVideoError)
      }

      // Destruir o player Plyr se existir
      if (playerRef.current) {
        try {
          playerRef.current.destroy()
        } catch (error) {
          console.error('Erro ao destruir o player Plyr:', error)
        }
        playerRef.current = null
      }
    }
  }, [videoRef.current])

  // Atualizar a URL do vídeo quando ela mudar
  useEffect(() => {
    setCurrentVideoUrl(videoUrl)
    setIsLoading(true)
    setVideoError(false)
    setErrorMessage('')
    setHasMarkedAsWatched(false)

    // Se o player já estiver inicializado, atualizar a fonte
    if (playerRef.current && videoRef.current) {
      playerRef.current.source = {
        type: 'video',
        sources: [
          {
            src: videoUrl,
            type: 'video/mp4',
          },
        ],
        poster: poster,
      }
    }
  }, [videoUrl, poster])

  // Carregar o progresso de visualização
  const loadWatchProgress = async () => {
    if (!session?.user || !episodeId || !playerRef.current) return

    try {
      const response = await fetch(`/api/watch-progress?episodeId=${episodeId}`)

      if (response.ok) {
        const data = await response.json()

        if (data.currentTime && playerRef.current) {
          // Só definir o tempo se for maior que 0 e menor que 98% da duração
          if (data.currentTime > 0 && (!data.duration || data.percentage < 98)) {
            playerRef.current.currentTime = data.currentTime
            setSavedTime(data.currentTime)
          }
        }
      }
    } catch (error) {
      console.error('Erro ao carregar progresso:', error)
    }
  }

  // Salvar o progresso de visualização
  const saveWatchProgress = async () => {
    if (!session?.user || !episodeId || !playerRef.current) return

    const currentTime = playerRef.current.currentTime
    const duration = playerRef.current.duration
    const now = Date.now()

    // Só salvar se passou o intervalo definido desde o último salvamento
    if (now - lastSaveTime < saveInterval) return
    setLastSaveTime(now)

    try {
      await fetch('/api/watch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          currentTime,
          duration,
        }),
      })
    } catch (error) {
      console.error('Erro ao salvar progresso:', error)
    }
  }

  // Atualizar o tempo atual e salvar o progresso
  const handleTimeUpdate = () => {
    if (!playerRef.current) return

    const currentTime = playerRef.current.currentTime
    const duration = playerRef.current.duration

    // Marcar como assistido quando atingir 80% do vídeo
    if (!hasMarkedAsWatched && currentTime > duration * 0.8 && animeSlug && episodeId) {
      setHasMarkedAsWatched(true)
      updateWatchedStatus(episodeId, true)
    }

    // Salvar o progresso a cada intervalo
    saveWatchProgress()
  }

  // Atualizar o status de assistido
  const updateWatchedStatus = async (episodeId: string, watched: boolean) => {
    if (!animeSlug || !session?.user) return

    try {
      const response = await fetch(`/api/animes/${animeSlug}/watched-episodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          watched,
        }),
      })

      if (!response.ok) {
        throw new Error('Falha ao atualizar status de assistido')
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error)
    }
  }

  // Lidar com o fim do vídeo
  const handleEnded = () => {
    if (onNextEpisode) {
      // Pequeno atraso antes de ir para o próximo episódio
      setTimeout(() => {
        onNextEpisode()
      }, 1000)
    }
  }

  // Lidar com erros no vídeo
  const handleVideoError = () => {
    setVideoError(true)
    setIsLoading(false)
    setErrorMessage('Não foi possível reproduzir o vídeo. Tente novamente mais tarde.')
  }

  // Renderizar o player para vídeos do Blogger
  const renderBloggerPlayer = () => {
    return (
      <div className="w-full h-full">
        <iframe
          src={currentVideoUrl}
          className="w-full h-full"
          allowFullScreen
          frameBorder="0"
          scrolling="no"
          title={title}
          onLoad={() => setIsLoading(false)}
        />
      </div>
    )
  }

  // Renderizar a tela de erro
  const renderErrorScreen = () => {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-900 flex-col p-4">
        <svg className="w-16 h-16 text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 className="text-xl font-bold text-white mb-2">Erro ao reproduzir vídeo</h3>
        <p className="text-gray-400 text-center mb-4">{errorMessage}</p>
        <div className="flex flex-wrap gap-2 justify-center">
          <button
            onClick={() => {
              setVideoError(false)
              setIsLoading(true)

              // Tentar recarregar o vídeo
              if (playerRef.current) {
                playerRef.current.restart()
              }
            }}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Tentar novamente
          </button>

          {episodeId && (
            <button
              onClick={async () => {
                setVideoError(false)
                setIsLoading(true)
                setErrorMessage('')

                try {
                  // Tentar carregar o vídeo através da API de proxy
                  const proxyUrl = `/api/video/${episodeId}`
                  setCurrentVideoUrl(proxyUrl)

                  // Atualizar a fonte do player
                  if (playerRef.current) {
                    playerRef.current.source = {
                      type: 'video',
                      sources: [
                        {
                          src: proxyUrl,
                          type: 'video/mp4',
                        },
                      ],
                      poster: poster,
                    }
                  }
                } catch (error) {
                  console.error('Erro ao tentar usar proxy alternativo:', error)
                  setVideoError(true)
                  setErrorMessage('Não foi possível carregar o vídeo através do proxy alternativo.')
                }
              }}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Usar servidor alternativo
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className={`relative aspect-video bg-black ${className}`}>
      {/* Tela de carregamento */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      )}

      {/* Player de vídeo */}
      {videoError ? (
        renderErrorScreen()
      ) : isBloggerVideo ? (
        renderBloggerPlayer()
      ) : (
        <video
          ref={videoRef}
          className="w-full h-full"
          poster={poster}
          playsInline
          crossOrigin="anonymous"
        >
          <source src={currentVideoUrl} type="video/mp4" />
          Seu navegador não suporta o elemento de vídeo.
        </video>
      )}
    </div>
  )
}
