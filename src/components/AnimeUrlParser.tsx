'use client'

import { useState, useEffect } from 'react'

interface AnimeUrlParserProps {
  onCreateAnime: (animeData: any) => void
  onUpdateEpisodes: (animeSlug: string, episodesData: any[]) => void
}

interface ParsedAnime {
  title: string
  server: string
  format: string
  quality: string
  urlPattern: string
  episodes: number[]
}

export default function AnimeUrlParser({ onCreateAnime, onUpdateEpisodes }: AnimeUrlParserProps) {
  const [urls, setUrls] = useState<string>('')
  const [parsedAnime, setParsedAnime] = useState<ParsedAnime | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedAnimeSlug, setSelectedAnimeSlug] = useState<string>('')
  const [animes, setAnimes] = useState<any[]>([])
  const [isCreatingAnime, setIsCreatingAnime] = useState(false)
  const [isUpdatingEpisodes, setIsUpdatingEpisodes] = useState(false)
  const [malSearchQuery, setMalSearchQuery] = useState('')
  const [malSearchResults, setMalSearchResults] = useState<any[]>([])
  const [selectedMalAnime, setSelectedMalAnime] = useState<any | null>(null)
  const [isSearchingMal, setIsSearchingMal] = useState(false)

  // Fetch animes on component mount
  useEffect(() => {
    fetchAnimes()
  }, [])

  const fetchAnimes = async () => {
    try {
      const response = await fetch('/api/animes')
      if (!response.ok) {
        throw new Error('Failed to fetch animes')
      }
      const data = await response.json()
      setAnimes(data)
    } catch (error) {
      console.error('Error fetching animes:', error)
      setError('Erro ao carregar animes')
    }
  }

  const parseUrls = () => {
    setError(null)
    
    if (!urls.trim()) {
      setError('Por favor, insira pelo menos uma URL')
      return
    }

    try {
      // Split by new lines to handle multiple URLs
      const urlList = urls.split('\n').filter(url => url.trim())
      
      if (urlList.length === 0) {
        setError('Nenhuma URL válida encontrada')
        return
      }

      // Parse the first URL to extract pattern
      const firstUrl = urlList[0]
      const parsedUrl = new URL(firstUrl)
      
      // Extract server and format from path segments
      const pathSegments = parsedUrl.pathname.split('/').filter(segment => segment)
      const server = pathSegments[0] // e.g., 's5', 's7'
      const format = pathSegments[1] // e.g., 'mp4', 'mp4_temp'
      
      // Extract anime name from path
      const animePath = pathSegments[2] // e.g., 'wind-breaker-season-2', 'bleach-sennen-kessen-hen---soukoku-tan'
      
      // Convert to title case for display
      const title = animePath
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
        .replace(/---/g, ': ')
      
      // Extract quality and episode number
      const quality = pathSegments[3] === 'hd' ? 'hd' : pathSegments[4] // 'hd' or '720p'
      
      // Determine episode numbers from all URLs
      const episodes = urlList.map(url => {
        const urlObj = new URL(url)
        const segments = urlObj.pathname.split('/').filter(segment => segment)
        
        // Handle different URL patterns
        if (segments[3] === 'hd') {
          // Pattern: /s5/mp4/anime-name/hd/1.mp4
          return parseInt(segments[4].split('.')[0])
        } else if (segments[3] && segments[3].includes('.mp4')) {
          // Pattern: /s5/mp4/anime-name/1.mp4
          return parseInt(segments[3].split('.')[0])
        } else {
          // Pattern: /s5/mp4_temp/anime-name/1/720p.mp4
          return parseInt(segments[3])
        }
      }).filter(ep => !isNaN(ep)).sort((a, b) => a - b)

      // Determine URL pattern for future episodes
      let urlPattern = ''
      if (pathSegments[3] === 'hd') {
        // Pattern: https://lightspeedst.net/s5/mp4/wind-breaker-season-2/hd/{episode}.mp4
        urlPattern = `https://${parsedUrl.host}/${server}/${format}/${animePath}/hd/{episode}.mp4`
      } else if (pathSegments[4] && pathSegments[4].includes('p.mp4')) {
        // Pattern: https://lightspeedst.net/s5/mp4_temp/teogonia/{episode}/720p.mp4
        urlPattern = `https://${parsedUrl.host}/${server}/${format}/${animePath}/{episode}/${quality}`
      } else {
        // Fallback pattern
        urlPattern = `https://${parsedUrl.host}/${server}/${format}/${animePath}/{episode}.mp4`
      }

      setParsedAnime({
        title,
        server,
        format,
        quality,
        urlPattern,
        episodes
      })
    } catch (error) {
      console.error('Error parsing URLs:', error)
      setError('Erro ao analisar URLs. Verifique se as URLs estão no formato correto.')
    }
  }

  const searchMalAnime = async () => {
    if (!malSearchQuery.trim()) return
    
    try {
      setIsSearchingMal(true)
      const response = await fetch(`/api/mal/search?q=${encodeURIComponent(malSearchQuery)}`)
      
      if (!response.ok) {
        throw new Error('Failed to search anime')
      }
      
      const data = await response.json()
      setMalSearchResults(data)
    } catch (error) {
      console.error('Error searching MAL:', error)
      setError('Erro ao buscar anime no MyAnimeList')
    } finally {
      setIsSearchingMal(false)
    }
  }

  const handleSelectMalAnime = (anime: any) => {
    setSelectedMalAnime(anime)
    setMalSearchResults([])
  }

  const createAnimeFromParsed = async () => {
    if (!parsedAnime || !selectedMalAnime) return
    
    try {
      setIsCreatingAnime(true)
      
      // Traduz o status
      const statusMap: { [key: string]: string } = {
        'currently_airing': 'Em Andamento',
        'finished_airing': 'Concluído',
        'not_yet_aired': 'Não Lançado'
      }

      // Map genres from MAL to Portuguese
      const genreMap: { [key: string]: string } = {
        'Action': 'Ação',
        'Adventure': 'Aventura',
        'Comedy': 'Comédia',
        'Drama': 'Drama',
        'Fantasy': 'Fantasia',
        'Horror': 'Terror',
        'Mystery': 'Mistério',
        'Romance': 'Romance',
        'Sci-Fi': 'Ficção Científica',
        'Slice of Life': 'Slice of Life',
        'Sports': 'Esportes',
        'Supernatural': 'Sobrenatural'
        // Add more mappings as needed
      }
      
      const animeData = {
        title: selectedMalAnime.node.title || parsedAnime.title,
        description: selectedMalAnime.node.synopsis || 'Sem descrição disponível.',
        image: selectedMalAnime.node.main_picture?.large || '',
        status: statusMap[selectedMalAnime.node.status] || 'Em Andamento',
        totalEpisodes: selectedMalAnime.node.num_episodes || parsedAnime.episodes.length,
        studio: selectedMalAnime.node.studios?.[0]?.name || 'Desconhecido',
        year: selectedMalAnime.node.start_date ? new Date(selectedMalAnime.node.start_date).getFullYear() : new Date().getFullYear(),
        genres: selectedMalAnime.node.genres?.map((g: any) => genreMap[g.name] || g.name) || ['Anime'],
      }
      
      // Call the provided callback to create the anime
      onCreateAnime(animeData)
      
      // Reset form
      setUrls('')
      setParsedAnime(null)
      setSelectedMalAnime(null)
      setMalSearchQuery('')
    } catch (error) {
      console.error('Error creating anime:', error)
      setError('Erro ao criar anime')
    } finally {
      setIsCreatingAnime(false)
    }
  }

  const createEpisodesFromParsed = async () => {
    if (!parsedAnime || !selectedAnimeSlug) return
    
    try {
      setIsUpdatingEpisodes(true)
      
      // Create episode data for each episode number
      const episodesData = parsedAnime.episodes.map(episodeNumber => {
        return {
          number: episodeNumber,
          title: `Episódio ${episodeNumber}`,
          airDate: new Date().toISOString(),
          frame: animes.find(a => a.slug === selectedAnimeSlug)?.image || '',
          videoUrl: parsedAnime.urlPattern.replace('{episode}', episodeNumber.toString())
        }
      })
      
      // Call the provided callback to update episodes
      onUpdateEpisodes(selectedAnimeSlug, episodesData)
      
      // Reset form
      setUrls('')
      setParsedAnime(null)
      setSelectedAnimeSlug('')
    } catch (error) {
      console.error('Error creating episodes:', error)
      setError('Erro ao criar episódios')
    } finally {
      setIsUpdatingEpisodes(false)
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
      <h2 className="text-xl font-bold mb-4">Criador de Animes por URL</h2>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-300 mb-1">
          URLs dos Episódios (uma por linha)
        </label>
        <textarea
          value={urls}
          onChange={(e) => setUrls(e.target.value)}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={5}
          placeholder="https://lightspeedst.net/s5/mp4/wind-breaker-season-2/hd/1.mp4&#10;https://lightspeedst.net/s5/mp4/wind-breaker-season-2/hd/2.mp4"
        />
      </div>
      
      <div className="flex justify-end mb-6">
        <button
          onClick={parseUrls}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Analisar URLs
        </button>
      </div>
      
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-200">
          {error}
        </div>
      )}
      
      {parsedAnime && (
        <div className="mb-6 p-4 bg-gray-700 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Informações Detectadas</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-400">Título</p>
              <p className="font-medium">{parsedAnime.title}</p>
            </div>
            <div>
              <p className="text-sm text-gray-400">Servidor</p>
              <p className="font-medium">{parsedAnime.server}</p>
            </div>
            <div>
              <p className="text-sm text-gray-400">Formato</p>
              <p className="font-medium">{parsedAnime.format}</p>
            </div>
            <div>
              <p className="text-sm text-gray-400">Qualidade</p>
              <p className="font-medium">{parsedAnime.quality}</p>
            </div>
            <div className="md:col-span-2">
              <p className="text-sm text-gray-400">Padrão de URL</p>
              <p className="font-medium break-all">{parsedAnime.urlPattern}</p>
            </div>
            <div className="md:col-span-2">
              <p className="text-sm text-gray-400">Episódios Detectados ({parsedAnime.episodes.length})</p>
              <p className="font-medium">
                {parsedAnime.episodes.length > 10 
                  ? `${parsedAnime.episodes.slice(0, 10).join(', ')}... (total: ${parsedAnime.episodes.length})`
                  : parsedAnime.episodes.join(', ')}
              </p>
            </div>
          </div>
          
          <div className="border-t border-gray-600 pt-4 mt-4">
            <h3 className="text-lg font-semibold mb-2">Buscar no MyAnimeList</h3>
            <div className="flex gap-2 mb-4">
              <input
                type="text"
                value={malSearchQuery}
                onChange={(e) => setMalSearchQuery(e.target.value)}
                placeholder="Buscar anime..."
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={searchMalAnime}
                disabled={isSearchingMal || !malSearchQuery.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {isSearchingMal ? 'Buscando...' : 'Buscar'}
              </button>
            </div>
            
            {malSearchResults.length > 0 && (
              <div className="max-h-60 overflow-y-auto mb-4 bg-gray-700 border border-gray-600 rounded-lg">
                {malSearchResults.map((anime) => (
                  <div
                    key={anime.node.id}
                    onClick={() => handleSelectMalAnime(anime)}
                    className="p-3 hover:bg-gray-600 cursor-pointer border-b border-gray-600 last:border-b-0"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-14 bg-gray-600 mr-3">
                        {anime.node.main_picture && (
                          <img
                            src={anime.node.main_picture.medium}
                            alt={anime.node.title}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium">{anime.node.title}</h4>
                        <p className="text-sm text-gray-400">
                          {anime.node.start_date ? new Date(anime.node.start_date).getFullYear() : 'N/A'} • 
                          {anime.node.num_episodes ? ` ${anime.node.num_episodes} eps` : ' ? eps'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {selectedMalAnime && (
              <div className="p-3 bg-gray-700 border border-gray-600 rounded-lg mb-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-12 h-16 bg-gray-600 mr-3">
                    {selectedMalAnime.node.main_picture && (
                      <img
                        src={selectedMalAnime.node.main_picture.medium}
                        alt={selectedMalAnime.node.title}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium">{selectedMalAnime.node.title}</h4>
                    <p className="text-sm text-gray-400">
                      {selectedMalAnime.node.start_date ? new Date(selectedMalAnime.node.start_date).getFullYear() : 'N/A'} • 
                      {selectedMalAnime.node.num_episodes ? ` ${selectedMalAnime.node.num_episodes} eps` : ' ? eps'} •
                      {selectedMalAnime.node.studios && selectedMalAnime.node.studios.length > 0 ? ` ${selectedMalAnime.node.studios[0].name}` : ' Estúdio desconhecido'}
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            <button
              onClick={createAnimeFromParsed}
              disabled={isCreatingAnime || !selectedMalAnime}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 mb-4"
            >
              {isCreatingAnime ? 'Criando Anime...' : 'Criar Novo Anime com Episódios'}
            </button>
            
            <div className="border-t border-gray-600 pt-4 mt-4">
              <h3 className="text-lg font-semibold mb-2">Adicionar Episódios a um Anime Existente</h3>
              <select
                value={selectedAnimeSlug}
                onChange={(e) => setSelectedAnimeSlug(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
              >
                <option value="">Selecione um anime...</option>
                {animes.map((anime) => (
                  <option key={anime.id} value={anime.slug}>
                    {anime.title}
                  </option>
                ))}
              </select>
              
              <button
                onClick={createEpisodesFromParsed}
                disabled={isUpdatingEpisodes || !selectedAnimeSlug}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
              >
                {isUpdatingEpisodes ? 'Adicionando Episódios...' : 'Adicionar Episódios ao Anime Selecionado'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
