'use client'

interface DeleteEpisodeButtonProps {
  animeSlug: string
  episodeId: string
}

export default function DeleteEpisodeButton({ animeSlug, episodeId }: DeleteEpisodeButtonProps) {
  const handleDelete = async () => {
    if (!confirm('Tem certeza que deseja excluir este episódio?')) {
      return
    }

    try {
      const response = await fetch(`/api/animes/${animeSlug}/episodes/${episodeId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete episode')
      }

      window.location.reload()
    } catch (error) {
      console.error('Error deleting episode:', error)
      alert('Erro ao excluir episódio')
    }
  }

  return (
    <button
      onClick={handleDelete}
      className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
    >
      Excluir
    </button>
  )
} 