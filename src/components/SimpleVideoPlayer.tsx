'use client'

import { useRef, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import CustomVideoPlayer from './CustomVideoPlayer'
import BloggerVideoPlayer from './BloggerVideoPlayer'

interface SimpleVideoPlayerProps {
  videoUrl: string
  poster?: string
  title?: string
  className?: string
  onNextEpisode?: () => void
  episodeId?: string
  animeSlug?: string
}

export default function SimpleVideoPlayer({
  videoUrl,
  poster,
  title = 'Episódio de Anime',
  className = '',
  onNextEpisode,
  episodeId,
  animeSlug
}: SimpleVideoPlayerProps) {
  const { data: session } = useSession()
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [videoError, setVideoError] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [currentVideoUrl, setCurrentVideoUrl] = useState(videoUrl)

  // Determina o tipo de vídeo com base na URL
  const isBloggerVideo = videoUrl.includes('blogger.com') || videoUrl.includes('blogspot.com')
  const isLightspeedVideo = videoUrl && videoUrl.includes('lightspeedst.net')

  // Para vídeos do lightspeedst.net, usamos a API de proxy
  useEffect(() => {
    let finalVideoUrl = videoUrl || ''
    if (isLightspeedVideo && episodeId) {
      finalVideoUrl = `/api/video/${episodeId}`
    }
    setCurrentVideoUrl(finalVideoUrl)
    setIsLoading(true)
    setVideoError(false)
    setErrorMessage('')

    // Pequeno timeout para garantir que o player seja reinicializado corretamente
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 500)

    return () => {
      clearTimeout(timer)

      // Salvar progresso antes de desmontar o componente
      if (episodeId) {
        try {
          const savedProgress = localStorage.getItem(`video_progress_${episodeId}`)
          if (savedProgress) {
            const currentTime = parseFloat(savedProgress)
            // Enviar beacon para salvar o progresso no servidor
            if (currentTime > 0) {
              try {
                const blob = new Blob([JSON.stringify({
                  episodeId,
                  currentTime,
                  duration: 0 // Não temos a duração aqui, mas o servidor pode lidar com isso
                })], { type: 'application/json' })

                navigator.sendBeacon('/api/watch-progress', blob)
              } catch (e) {
                console.error('Erro ao enviar beacon ao desmontar:', e)
              }
            }
          }
        } catch (e) {
          console.error('Erro ao recuperar progresso do localStorage ao desmontar:', e)
        }
      }
    }
  }, [videoUrl, isLightspeedVideo, episodeId])

  // Renderizar o player para vídeos do Blogger
  const renderBloggerPlayer = () => {
    return (
      <BloggerVideoPlayer
        videoUrl={currentVideoUrl}
        title={title}
        episodeId={episodeId}
        animeSlug={animeSlug}
        className=""
        onNextEpisode={onNextEpisode}
      />
    )
  }

  // Renderizar a tela de erro
  const renderErrorScreen = () => {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-900 flex-col p-4">
        <svg className="w-16 h-16 text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 className="text-xl font-bold text-white mb-2">Erro ao reproduzir vídeo</h3>
        <p className="text-gray-400 text-center mb-4">{errorMessage}</p>
        <div className="flex flex-wrap gap-2 justify-center">
          <button
            onClick={() => {
              setVideoError(false)
              setIsLoading(true)

              // Tentar recarregar o vídeo
              setTimeout(() => {
                setIsLoading(false)
              }, 1000)
            }}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Tentar novamente
          </button>

          {episodeId && (
            <button
              onClick={async () => {
                setVideoError(false)
                setIsLoading(true)
                setErrorMessage('')

                try {
                  // Tentar carregar o vídeo através da API de proxy
                  const proxyUrl = `/api/video/${episodeId}`
                  setCurrentVideoUrl(proxyUrl)
                } catch (error) {
                  console.error('Erro ao tentar usar proxy alternativo:', error)
                  setVideoError(true)
                  setErrorMessage('Não foi possível carregar o vídeo através do proxy alternativo.')
                }
              }}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Usar servidor alternativo
            </button>
          )}
        </div>
      </div>
    )
  }

  // Renderizar o player para vídeos MP4 usando nosso player personalizado
  const renderCustomPlayer = () => {
    return (
      <CustomVideoPlayer
        videoUrl={currentVideoUrl}
        poster={poster}
        title={title}
        episodeId={episodeId}
        animeSlug={animeSlug}
        onNextEpisode={onNextEpisode}
      />
    );
  };

  return (
    <div ref={containerRef} className={`relative aspect-video bg-black ${className}`}>
      {/* Player de vídeo */}
      {videoError ? (
        renderErrorScreen()
      ) : isBloggerVideo ? (
        renderBloggerPlayer()
      ) : (
        renderCustomPlayer()
      )}
    </div>
  )
}
