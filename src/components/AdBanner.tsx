'use client'

import { useState, useEffect } from 'react'

interface AdBannerProps {
  position: 'top' | 'bottom' | 'sidebar' | 'in-content'
  className?: string
}

export default function AdBanner({ position, className = '' }: AdBannerProps) {
  const [isClient, setIsClient] = useState(false)
  const [adId] = useState(`ad-${Math.random().toString(36).substring(2, 9)}`)
  const [iframeLoaded, setIframeLoaded] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Definir tamanhos e estilos com base na posição e dispositivo
  let containerStyle = {}
  let adWidth = 300
  let adHeight = 250

  // Ajustar tamanho e posicionamento com base na posição
  switch (position) {
    case 'top':
      containerStyle = {
        maxWidth: '100%',
        margin: '0 auto 1.5rem auto',
        display: 'flex',
        justifyContent: 'center'
      }
      break
    case 'bottom':
      containerStyle = {
        maxWidth: '100%',
        margin: '1.5rem auto 0 auto',
        display: 'flex',
        justifyContent: 'center'
      }
      break
    case 'sidebar':
      containerStyle = {
        margin: '1rem auto',
        display: 'flex',
        justifyContent: 'center'
      }
      break
    case 'in-content':
      containerStyle = {
        maxWidth: '100%',
        margin: '1.5rem auto',
        display: 'flex',
        justifyContent: 'center'
      }
      break
    default:
      containerStyle = {
        margin: '1rem auto',
        display: 'flex',
        justifyContent: 'center'
      }
  }

  // Função para criar o HTML do anúncio
  const createAdHtml = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
          }
          .ad-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
          }
        </style>
      </head>
      <body>
        <div class="ad-container">
          <script>
          (function(wqutvy){
            var d = document,
                s = d.createElement('script'),
                l = d.scripts[d.scripts.length - 1];
            s.settings = wqutvy || {};
            s.src = "//complete-drink.com/bLXYVPs/d.GRlv0sYSWfcp/Le/mK9ku/ZzUJlBkxP_TcYSzgNyzNUA1/MODDA/tUNGjnMK3/NQT/UrwfMAQd";
            s.async = true;
            s.referrerPolicy = 'no-referrer-when-downgrade';
            l.parentNode.insertBefore(s, l);
          })({})
          </script>
        </div>
      </body>
      </html>
    `;
  };

  if (!isClient) {
    return null;
  }

  return (
    <div
      id={adId}
      className={`ad-container ${className} relative`}
      data-ad-position={position}
      style={containerStyle}
    >
      <div className="relative" style={{ width: `${adWidth}px`, height: `${adHeight}px` }}>
        {/* Mensagem sobre anúncios */}
        <p className="text-xs text-gray-400 absolute -top-4 left-0 right-0 text-center">
          Anúncios ajudam a manter o site
        </p>

        {/* Iframe para isolar o anúncio */}
        <iframe
          title={`ad-${position}-${adId}`}
          width={adWidth}
          height={adHeight}
          frameBorder="0"
          scrolling="no"
          sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
          style={{
            backgroundColor: '#1f2937',
            borderRadius: '0.5rem',
            opacity: iframeLoaded ? 1 : 0,
            transition: 'opacity 0.3s ease'
          }}
          onLoad={() => setIframeLoaded(true)}
          srcDoc={createAdHtml()}
        />
      </div>
    </div>
  );
}
