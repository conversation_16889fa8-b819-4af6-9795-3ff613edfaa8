'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import Link from 'next/link'
import { Card } from '@/components/ui/card'
import { toast } from 'react-hot-toast'
import { TrashIcon, CheckCircleIcon } from '@heroicons/react/24/outline'
import AdBanner from '@/components/AdBanner'

interface WatchProgress {
  id: string
  episodeId: string
  userId: string
  currentTime: number
  duration: number
  percentage: number
  updatedAt: string
  isBloggerEpisode?: boolean // Adicionado para identificar episódios do Blogger
  episode: {
    id: string
    number: number
    title: string
    animeId: string
    frame?: string
    videoUrl?: string
    anime: {
      id: string
      slug?: string
      title: string
      image?: string
    }
  }
}

export default function ContinueAssistindoPage() {
  const { data: session, status } = useSession()
  const [watchHistory, setWatchHistory] = useState<WatchProgress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'recent' | 'progress'>('recent')

  useEffect(() => {
    if (status === 'authenticated') {
      fetchWatchHistory()
    } else if (status === 'unauthenticated') {
      setIsLoading(false)
    }
  }, [status])

  const fetchWatchHistory = async () => {
    try {
      const response = await fetch('/api/watch-progress')
      if (!response.ok) throw new Error('Erro ao buscar histórico de visualização')
      const data = await response.json()
      setWatchHistory(data)
    } catch (error) {
      console.error('Erro ao buscar histórico de visualização:', error)
      toast.error('Erro ao buscar histórico de visualização')
    } finally {
      setIsLoading(false)
    }
  }

  // Remover um item do progresso de visualização
  const removeWatchProgress = async (progressId: string, episodeId: string) => {
    try {
      // Atualizar a UI imediatamente (Optimistic UI)
      setWatchHistory(prev => prev.filter(item => item.id !== progressId))

      const response = await fetch(`/api/watch-progress?episodeId=${episodeId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Erro ao remover progresso de visualização')
      }

      toast.success('Progresso removido com sucesso')
    } catch (error) {
      console.error('Erro ao remover progresso de visualização:', error)
      toast.error('Erro ao remover progresso de visualização')

      // Recarregar os dados em caso de erro
      fetchWatchHistory()
    }
  }

  // Remover todos os itens do progresso de visualização
  const removeAllWatchProgress = async () => {
    if (!confirm('Tem certeza que deseja limpar todo o histórico de visualização?')) {
      return
    }

    try {
      // Atualizar a UI imediatamente (Optimistic UI)
      setWatchHistory([])

      const response = await fetch('/api/watch-progress', {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Erro ao limpar histórico de visualização')
      }

      toast.success('Histórico de visualização limpo com sucesso')
    } catch (error) {
      console.error('Erro ao limpar histórico de visualização:', error)
      toast.error('Erro ao limpar histórico de visualização')

      // Recarregar os dados em caso de erro
      fetchWatchHistory()
    }
  }

  const filteredHistory = watchHistory.filter(progress => {
    const searchLower = searchTerm.toLowerCase()
    return (
      progress.episode.anime.title.toLowerCase().includes(searchLower) ||
      `episódio ${progress.episode.number}`.includes(searchLower)
    )
  })

  const sortedHistory = [...filteredHistory].sort((a, b) => {
    if (sortBy === 'recent') {
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    } else {
      return b.percentage - a.percentage
    }
  })

  if (status === 'loading' || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Continue Assistindo</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Faça login para ver seu histórico de assistidos.
          </p>
          <Link
            href="/login"
            className="inline-block bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Fazer Login
          </Link>
        </div>
      </div>
    )
  }

  if (watchHistory.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Continue Assistindo</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Você ainda não tem nenhum anime em seu histórico.
        </p>
        <Link
          href="/animes"
          className="inline-block bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
        >
          Explorar Animes
        </Link>
      </div>
    )
  }

  // Formatar o tempo para exibição
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  // Formatar a data para exibição
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold">Continue Assistindo</h1>
        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <input
            type="text"
            placeholder="Buscar anime ou episódio..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 w-full sm:w-64"
          />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'recent' | 'progress')}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 w-full sm:w-48"
          >
            <option value="recent">Mais Recentes</option>
            <option value="progress">Mais Assistidos</option>
          </select>

          {watchHistory.length > 0 && (
            <button
              onClick={removeAllWatchProgress}
              className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <TrashIcon className="w-5 h-5 mr-2" />
              Limpar histórico
            </button>
          )}
        </div>
      </div>

      {/* Banner único */}
      <div className="mb-6">
        <AdBanner position="in-content" />
      </div>

      {filteredHistory.length === 0 ? (
        <div className="text-center py-8">
          {searchTerm ? (
            <p className="text-gray-600 dark:text-gray-400">
              Nenhum resultado encontrado para "{searchTerm}"
            </p>
          ) : (
            <p className="text-gray-600 dark:text-gray-400">
              Você ainda não tem nenhum anime em seu histórico.
            </p>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {sortedHistory.map((progress) => {
            const episode = progress.episode

            return (
              <div key={progress.id} className="group relative">
                <Link
                  href={`/animes/${episode.anime.slug}?episode=${episode.number}&autoplay=true`}
                  className="block"
                >
                  <Card className="overflow-hidden transition-transform duration-300 hover:scale-105">
                    <div className="relative aspect-video">
                      <Image
                        src={episode.frame || episode.anime.image || '/placeholder.png'}
                        alt={`${episode.anime.title} - Episódio ${episode.number}`}
                        fill
                        className="object-cover object-center"
                        unoptimized
                        sizes="(max-width: 768px) 100vw, 300px"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                      <div className="absolute top-2 right-2 bg-purple-600 px-2 py-1 rounded-md">
                        <span className="text-sm font-bold text-white">
                          EP {episode.number}
                        </span>
                      </div>


                      <div className="absolute bottom-0 left-0 right-0 p-3">
                        <h3 className="text-sm font-medium text-white line-clamp-1">
                          {episode.anime.title}
                        </h3>
                        <p className="text-xs text-gray-300 mt-1">
                          {formatDate(progress.updatedAt)}
                        </p>
                      </div>
                    </div>
                    <div className="p-3 bg-gray-800">
                      {/* Verificar se é um episódio do Blogger */}
                      {progress.isBloggerEpisode ? (
                        <div className="flex justify-center items-center py-1">
                          <CheckCircleIcon className="w-5 h-5 text-purple-500 mr-2" />
                          <span className="text-sm text-purple-400 font-medium">
                            Assistido
                          </span>
                        </div>
                      ) : (
                        <>
                          <div className="w-full bg-gray-700 rounded-full h-1.5">
                            <div
                              className="bg-purple-600 h-1.5 rounded-full"
                              style={{
                                width: `${progress.percentage}%`,
                              }}
                            />
                          </div>
                          <div className="flex justify-between items-center mt-2">
                            <p className="text-xs text-gray-400">
                              {formatTime(progress.currentTime)} de {formatTime(progress.duration)}
                            </p>
                            <p className="text-xs text-gray-400">
                              {progress.percentage}%
                            </p>
                          </div>
                        </>
                      )}
                    </div>
                  </Card>
                </Link>
                <button
                  onClick={() => removeWatchProgress(progress.id, progress.episodeId)}
                  className="absolute top-2 left-2 bg-red-600 p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  title="Remover do histórico"
                >
                  <TrashIcon className="w-4 h-4 text-white" />
                </button>
              </div>
            )
          })}
        </div>
      )}


    </div>
  )
}