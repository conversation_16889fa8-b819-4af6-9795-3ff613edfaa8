'use client'

import { useState } from 'react'

export default function GenerateSitemap() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{
    success: boolean
    message: string
  } | null>(null)

  const handleGenerateSitemap = async () => {
    try {
      setIsLoading(true)
      setResult(null)

      const response = await fetch('/api/sitemap/generate')
      const data = await response.json()

      if (response.ok) {
        setResult({
          success: true,
          message: data.message
        })
      } else {
        setResult({
          success: false,
          message: data.error || 'Erro ao gerar sitemap'
        })
      }
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full">
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        O sitemap.xml é atualizado automaticamente quando novos animes ou episódios são adicionados.
        Use esta opção apenas se precisar gerar o sitemap manualmente.
      </p>

      {result && (
        <div className={`p-4 mb-4 rounded-md ${result.success ? 'bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-300' : 'bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-300'}`}>
          <p className="font-medium">{result.success ? 'Sucesso!' : 'Erro!'}</p>
          <p className="text-sm">{result.message}</p>
        </div>
      )}

      <button
        onClick={handleGenerateSitemap}
        disabled={isLoading}
        className="w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Gerando sitemap...' : 'Gerar Sitemap'}
      </button>
    </div>
  )
}
