# Ambiente
NODE_ENV=development

# Banco de Dados
DATABASE_URL="postgresql://postgres:password@localhost:5432/animezera?schema=public"

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret

# JWT
JWT_SECRET=your-jwt-secret

# Admin Credentials (Painel Administrativo)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password

# Email (Brevo/Sendinblue)
BREVO_API_KEY=your-brevo-api-key
BREVO_SMTP_USER=your-brevo-smtp-user
BREVO_SMTP_PASSWORD=your-brevo-smtp-password
EMAIL_FROM=<EMAIL>

# Email (para desenvolvimento, usamos Ethereal)
EMAIL_SERVER_HOST=smtp.ethereal.email
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=ethereal.pass
EMAIL_SERVER_SECURE=false

# URL da aplicação
NEXT_PUBLIC_APP_URL=http://localhost:3000
