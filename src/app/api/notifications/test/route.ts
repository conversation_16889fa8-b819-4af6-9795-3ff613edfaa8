import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { sendSimpleAnimeNotification } from '@/services/simpleNotificationService'

export async function POST(request: NextRequest) {
  console.log('API test notification chamada');
  
  try {
    const session = await getServerSession(authOptions)
    console.log('Sessão obtida:', session ? 'Sim' : 'Não');

    if (!session?.user) {
      console.log('Usuário não autenticado');
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { animeId } = body
    
    console.log('Testando notificação para anime:', animeId);

    if (!animeId) {
      return NextResponse.json(
        { error: 'ID do anime é obrigatório' },
        { status: 400 }
      )
    }

    // Enviar notificação de teste
    const result = await sendSimpleAnimeNotification(
      animeId,
      999, // Episódio de teste
      'Teste de Notificação',
      'Esta é uma notificação de teste para verificar se o sistema está funcionando!'
    )

    console.log('Resultado do teste:', result);

    return NextResponse.json({
      success: true,
      message: 'Notificação de teste enviada com sucesso',
      result
    })
  } catch (error) {
    console.error('Erro ao enviar notificação de teste:', error)
    return NextResponse.json(
      { error: 'Erro ao enviar notificação de teste', details: error instanceof Error ? error.message : 'Erro desconhecido' },
      { status: 500 }
    )
  }
}
