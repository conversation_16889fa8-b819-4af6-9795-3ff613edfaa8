'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

export default function ScheduleStatusPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [lastRunTime, setLastRunTime] = useState<string | null>(null)
  const [nextRunTime, setNextRunTime] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [token, setToken] = useState('')
  const [isTokenSet, setIsTokenSet] = useState(false)

  useEffect(() => {
    // Carregar token do localStorage
    const savedToken = localStorage.getItem('cronToken')
    if (savedToken) {
      setToken(savedToken)
      setIsTokenSet(true)
    }
  }, [])

  const handleSaveToken = () => {
    localStorage.setItem('cronToken', token)
    setIsTokenSet(true)
  }

  const handleRunNow = async () => {
    if (!token) {
      setError('Por favor, configure o token primeiro')
      return
    }

    setIsLoading(true)
    setError(null)
    setMessage(null)

    console.log('Iniciando verificação com token:', token)

    try {
      const authHeader = `Bearer ${token}`
      console.log('Cabeçalho de autorização:', authHeader)

      const response = await fetch('/api/schedule/check-episodes', {
        method: 'GET',
        headers: {
          'Authorization': authHeader
        }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao executar verificação')
      }

      setMessage('Verificação executada com sucesso!')
      setLastRunTime(new Date().toLocaleString())

      // Calcular próxima execução (4 horas depois)
      const nextRun = new Date()
      nextRun.setHours(nextRun.getHours() + 4)
      setNextRunTime(nextRun.toLocaleString())

      // Exibir resultados
      console.log('Resultados:', data.results)

      // Contar episódios adicionados
      const totalEpisodes = data.results.reduce((total: number, result: any) => {
        return total + (result.totalEpisodes || 0)
      }, 0)

      const missingEpisodes = data.results.reduce((total: number, result: any) => {
        return total + (result.missingEpisodes || 0)
      }, 0)

      const newEpisodes = data.results.reduce((total: number, result: any) => {
        return total + (result.newEpisodes || 0)
      }, 0)

      // Criar mensagem detalhada
      let message = `Verificação concluída! ${totalEpisodes} episódios adicionados no total.`;

      if (missingEpisodes > 0) {
        message += ` (${missingEpisodes} episódios faltantes, ${newEpisodes} novos episódios)`;
      }

      setMessage(message)
    } catch (error) {
      console.error('Erro:', error)
      setError(error instanceof Error ? error.message : 'Erro desconhecido')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-6">
        <h1 className="text-2xl font-bold">Status do Agendamento</h1>
        <Link href="/admin" className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 text-center">
          Voltar
        </Link>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Informações</h2>
        <p className="mb-2">
          A verificação de novos episódios é executada automaticamente a cada 4 horas usando GitHub Actions.
        </p>
        <p className="mb-4">
          Você também pode executar a verificação manualmente usando o botão abaixo.
        </p>

        {!isTokenSet ? (
          <div className="mb-4">
            <h3 className="text-lg font-medium mb-2">Configurar Token</h3>
            <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2">
              <input
                type="password"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                placeholder="Digite o token de autenticação"
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleSaveToken}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Salvar
              </button>
            </div>
            <p className="text-sm text-gray-400 mt-1">
              Este token é o mesmo configurado na variável de ambiente CRON_SECRET.
              <span className="block mt-1 text-yellow-400">
                Se CRON_SECRET não estiver definido, use o token padrão: <code className="bg-gray-700 px-1 py-0.5 rounded">your-secret-token</code>
              </span>
            </p>
          </div>
        ) : (
          <div className="mb-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Token Configurado</h3>
              <button
                onClick={() => setIsTokenSet(false)}
                className="text-sm text-blue-400 hover:text-blue-300"
              >
                Alterar
              </button>
            </div>
          </div>
        )}

        <div className="flex justify-center mt-4">
          <button
            onClick={handleRunNow}
            disabled={isLoading || !isTokenSet}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Verificando...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Verificar Agora
              </>
            )}
          </button>
        </div>
      </div>

      {(message || error || lastRunTime) && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Status</h2>

          {lastRunTime && (
            <div className="mb-2">
              <span className="font-medium text-gray-300">Última execução:</span>{' '}
              <span className="text-green-400">{lastRunTime}</span>
            </div>
          )}

          {nextRunTime && (
            <div className="mb-4">
              <span className="font-medium text-gray-300">Próxima execução automática (estimada):</span>{' '}
              <span className="text-blue-400">{nextRunTime}</span>
            </div>
          )}

          {message && (
            <div className="p-3 bg-green-900/30 border border-green-800 rounded-lg mb-3 text-green-300">
              {message}
            </div>
          )}

          {error && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded-lg text-red-300">
              {error}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
