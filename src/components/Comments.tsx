'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import { toast } from 'react-hot-toast'
import { ExclamationTriangleIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline'

interface Comment {
  id: string
  content: string
  createdAt: string
  user: {
    id: string
    name: string | null
    image: string | null
  }
}

interface CommentsProps {
  slug: string
  episodeId?: string
}

export default function Comments({ slug, episodeId }: CommentsProps) {
  const { data: session } = useSession()
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showComments, setShowComments] = useState(false)

  useEffect(() => {
    if (showComments) {
      fetchComments()
    }
  }, [slug, episodeId, showComments])

  const fetchComments = async () => {
    try {
      const url = `/api/animes/${slug}/comments${episodeId ? `?episodeId=${episodeId}` : ''}`
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error('Erro ao buscar comentários')
      }
      const data = await response.json()
      setComments(data)
    } catch (error) {
      console.error('Error fetching comments:', error)
      toast.error('Erro ao carregar comentários')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!session) {
      toast.error('Você precisa estar logado para comentar')
      return
    }

    if (!newComment.trim()) {
      toast.error('O comentário não pode estar vazio')
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/animes/${slug}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newComment,
          episodeId
        }),
      })

      if (!response.ok) {
        throw new Error('Erro ao enviar comentário')
      }

      const comment = await response.json()
      setComments([comment, ...comments])
      setNewComment('')
      toast.success('Comentário enviado com sucesso!')
    } catch (error) {
      console.error('Error submitting comment:', error)
      toast.error('Erro ao enviar comentário')
    } finally {
      setIsSubmitting(false)
    }
  }

  const toggleComments = () => {
    setShowComments(!showComments)
  }

  const renderCommentsContent = () => {
    if (isLoading) {
      return (
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex space-x-3">
              <div className="h-10 w-10 rounded-full bg-gray-700" />
              <div className="flex-1 space-y-2">
                <div className="h-4 w-1/4 bg-gray-700 rounded" />
                <div className="h-4 w-3/4 bg-gray-700 rounded" />
              </div>
            </div>
          ))}
        </div>
      )
    }

    return (
      <div className="space-y-6">
        {session && (
          <form onSubmit={handleSubmit} className="space-y-4">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Escreva seu comentário..."
              className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-none"
              rows={3}
              disabled={isSubmitting}
            />
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Enviando...' : 'Enviar comentário'}
            </button>
          </form>
        )}

        <div className="space-y-4">
          {comments.length === 0 ? (
            <p className="text-gray-400 text-center py-4">
              Nenhum comentário ainda. Seja o primeiro a comentar!
            </p>
          ) : (
            comments.map((comment) => (
              <div key={comment.id} className="flex space-x-3">
                <div className="flex-shrink-0">
                  {comment.user.image ? (
                    <Image
                      src={comment.user.image}
                      alt={comment.user.name || 'Usuário'}
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                      {comment.user.name?.[0]?.toUpperCase() || 'U'}
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-white">
                      {comment.user.name || 'Usuário Anônimo'}
                    </span>
                    <span className="text-gray-400 text-sm">
                      {new Date(comment.createdAt).toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                  <p className="mt-1 text-gray-300">{comment.content}</p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <button
          onClick={toggleComments}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          <span>{showComments ? 'Ocultar' : 'Mostrar'} Comentários</span>
          {showComments ? (
            <ChevronUpIcon className="h-5 w-5" />
          ) : (
            <ChevronDownIcon className="h-5 w-5" />
          )}
        </button>
      </div>

      {!showComments ? (
        <div className="text-center py-8">
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-4">
              <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
            </div>
            <h3 className="text-lg font-semibold text-yellow-500 mb-2">
              ALERTA de possível SPOILER!
            </h3>
            <p className="text-gray-300 mb-6">
              Como não podemos controlar totalmente os comentários, pense muito bem antes de continuar!
            </p>
            <button
              onClick={toggleComments}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Mostrar Comentários
            </button>
          </div>
        </div>
      ) : (
        renderCommentsContent()
      )}
    </div>
  )
} 