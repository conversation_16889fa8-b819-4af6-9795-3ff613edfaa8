'use client'

import { useState, useEffect } from 'react'
import { X, Download, ExternalLink } from 'lucide-react'

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed', platform: string }>
}

export default function InstallPWABanner() {
  const [showBanner, setShowBanner] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [isIOS, setIsIOS] = useState(false)

  useEffect(() => {
    // Verificar se o usuário já fechou o banner antes
    const bannerDismissed = localStorage.getItem('pwa-banner-dismissed')
    const appInstalled = localStorage.getItem('pwa-app-installed')
    
    // Verificar se é um dispositivo iOS
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream
    setIsIOS(isIOSDevice)
    
    // Verificar se é um dispositivo móvel
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    
    // Verificar se o app já está instalado
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    
    if (isStandalone) {
      localStorage.setItem('pwa-app-installed', 'true')
      return
    }
    
    // Mostrar o banner apenas em dispositivos móveis e se não foi fechado antes
    if (isMobile && !bannerDismissed && !appInstalled) {
      setShowBanner(true)
    }
    
    // Capturar o evento beforeinstallprompt para dispositivos Android
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevenir o comportamento padrão do navegador
      e.preventDefault()
      // Armazenar o evento para uso posterior
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      // Mostrar o banner
      setShowBanner(true)
    }
    
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    
    // Detectar quando o PWA é instalado
    window.addEventListener('appinstalled', () => {
      localStorage.setItem('pwa-app-installed', 'true')
      setShowBanner(false)
    })
    
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    }
  }, [])
  
  const handleInstall = async () => {
    if (!deferredPrompt && !isIOS) return
    
    if (deferredPrompt) {
      // Mostrar o prompt de instalação
      await deferredPrompt.prompt()
      
      // Aguardar a escolha do usuário
      const choiceResult = await deferredPrompt.userChoice
      
      if (choiceResult.outcome === 'accepted') {
        console.log('Usuário aceitou a instalação do PWA')
        localStorage.setItem('pwa-app-installed', 'true')
      }
      
      // Limpar o prompt
      setDeferredPrompt(null)
    }
    
    // Fechar o banner
    setShowBanner(false)
  }
  
  const handleDismiss = () => {
    // Marcar o banner como fechado
    localStorage.setItem('pwa-banner-dismissed', 'true')
    setShowBanner(false)
  }
  
  if (!showBanner) return null
  
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-gray-800 border-t border-gray-700 shadow-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="mr-3 bg-purple-600 p-2 rounded-full">
            <Download size={20} className="text-white" />
          </div>
          <div>
            <h3 className="font-medium text-white">Instale o AnimesZera</h3>
            <p className="text-sm text-gray-300">
              {isIOS 
                ? 'Adicione à tela inicial para acesso rápido' 
                : 'Instale nosso app para ter acesso rapido ao seus animes favorito.'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center">
          {isIOS ? (
            <button
              onClick={() => {
                handleDismiss()
                // Mostrar instruções para iOS
                alert('Para instalar o AnimesZera:\n1. Toque no botão de compartilhamento\n2. Role para baixo e toque em "Adicionar à Tela de Início"')
              }}
              className="mr-2 bg-purple-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center"
            >
              <ExternalLink size={16} className="mr-1" />
              Como instalar
            </button>
          ) : (
            <button
              onClick={handleInstall}
              className="mr-2 bg-purple-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center"
            >
              <Download size={16} className="mr-1" />
              Instalar
            </button>
          )}
          
          <button
            onClick={handleDismiss}
            className="text-gray-400 p-1.5 rounded-full hover:bg-gray-700"
            aria-label="Fechar"
          >
            <X size={20} />
          </button>
        </div>
      </div>
    </div>
  )
}
