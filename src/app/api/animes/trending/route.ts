import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { Anime } from '@prisma/client'

export async function GET() {
  try {
    // Busca os animes mais votados da última semana
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)

    const trendingAnimes = await prisma.anime.findMany({
      where: {
        votes: {
          some: {
            createdAt: {
              gte: oneWeekAgo
            }
          }
        }
      },
      include: {
        _count: {
          select: {
            votes: {
              where: {
                isLike: true,
                createdAt: {
                  gte: oneWeekAgo
                }
              }
            }
          }
        }
      },
      orderBy: {
        votes: {
          _count: 'desc'
        }
      },
      take: 5
    })

    // Formata os dados para retornar
    const formattedAnimes = trendingAnimes.map((anime: Anime & { _count: { votes: number } }) => ({
      id: anime.id,
      title: anime.title,
      image: anime.image,
      description: anime.description,
      status: anime.status,
      totalEpisodes: anime.totalEpisodes,
      studio: anime.studio,
      year: anime.year,
      genres: anime.genres,
      slug: anime.slug,
      audio: anime.audio,
      likes: anime._count.votes
    }))

    return NextResponse.json(formattedAnimes)
  } catch (error) {
    console.error('Error fetching trending animes:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar animes em destaque' },
      { status: 500 }
    )
  }
}