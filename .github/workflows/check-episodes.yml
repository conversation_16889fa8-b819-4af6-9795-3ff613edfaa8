name: Check New Episodes

on:
  schedule:
    # Executa a cada 4 horas (6 vezes por dia)
    - cron: '0 */4 * * *'
  workflow_dispatch: # Permite execução manual

jobs:
  check-episodes:
    runs-on: ubuntu-latest
    steps:
      - name: Check for new episodes
        run: |
          curl -X GET \
          -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
          https://animes-zera.vercel.app/api/schedule/check-episodes
