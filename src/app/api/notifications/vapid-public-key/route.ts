import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// Chaves VAPID para Web Push
// Na produção, estas devem vir de variáveis de ambiente
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U'

export async function GET() {
  console.log('API vapid-public-key chamada');

  try {
    const session = await getServerSession(authOptions)
    console.log('Sessão obtida:', session ? 'Sim' : 'Não');

    if (!session?.user) {
      console.log('Usuário não autenticado');
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    console.log('Retornando chave pública VAPID');
    return NextResponse.json({ publicKey: VAPID_PUBLIC_KEY })
  } catch (error) {
    console.error('Erro na API vapid-public-key:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
