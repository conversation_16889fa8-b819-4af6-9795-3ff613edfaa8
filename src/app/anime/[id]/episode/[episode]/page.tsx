'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'

// Mock data - will be replaced with API data later
const episodeData = {
  id: 1,
  number: 1,
  title: '<PERSON> Re<PERSON> das Maldições',
  animeTitle: '<PERSON><PERSON><PERSON>',
  videoSources: [
    {
      id: 1,
      name: 'MP4Upload',
      url: 'https://mp4upload.com/embed-1234567890.html',
      quality: 'HD',
    },
    {
      id: 2,
      name: 'Filemo<PERSON>',
      url: 'https://filemoon.sx/embed-1234567890.html',
      quality: 'HD',
    },
    {
      id: 3,
      name: 'Streamtape',
      url: 'https://streamtape.com/embed/1234567890',
      quality: 'HD',
    },
  ],
  nextEpisode: 2,
  prevEpisode: null,
}

export default function EpisodePage() {
  const params = useParams()
  const [selectedSource, setSelectedSource] = useState(episodeData.videoSources[0])

  return (
    <div className="space-y-8">
      {/* Video Player */}
      <div className="bg-black rounded-lg overflow-hidden aspect-video">
        <iframe
          src={selectedSource.url}
          className="w-full h-full"
          allowFullScreen
          title={`${episodeData.animeTitle} - Episódio ${episodeData.number}`}
        />
      </div>

      {/* Episode Info */}
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
              {episodeData.animeTitle}
            </h1>
            <h2 className="text-xl text-gray-600 dark:text-gray-400">
              Episódio {episodeData.number} - {episodeData.title}
            </h2>
          </div>
          <div className="flex space-x-4 mt-4 md:mt-0">
            {episodeData.prevEpisode && (
              <Link
                href={`/anime/${params.id}/episode/${episodeData.prevEpisode}`}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Episódio Anterior
              </Link>
            )}
            {episodeData.nextEpisode && (
              <Link
                href={`/anime/${params.id}/episode/${episodeData.nextEpisode}`}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Próximo Episódio
              </Link>
            )}
          </div>
        </div>

        {/* Video Sources */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Fontes de Vídeo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {episodeData.videoSources.map((source) => (
              <button
                key={source.id}
                onClick={() => setSelectedSource(source)}
                className={`p-4 rounded-lg border transition-colors ${
                  selectedSource.id === source.id
                    ? 'border-purple-600 bg-purple-50 dark:bg-purple-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-purple-600'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-800 dark:text-gray-200">
                    {source.name}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {source.quality}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Comments Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Comentários
          </h3>
          <div className="min-h-[200px] flex items-center justify-center text-gray-600 dark:text-gray-400">
            {/* Disqus or other comment system will be integrated here */}
            Sistema de comentários em breve...
          </div>
        </div>
      </div>
    </div>
  )
} 