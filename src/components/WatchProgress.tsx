'use client'

import { useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'

interface WatchProgressProps {
  episodeId: string
  animeId: string
  videoRef: React.RefObject<HTMLVideoElement>
}

export default function WatchProgress({ episodeId, animeId, videoRef }: WatchProgressProps) {
  const { data: session } = useSession()
  const lastSaveTime = useRef(0)
  const SAVE_INTERVAL = 5000 // Save every 5 seconds

  useEffect(() => {
    if (!session || !videoRef.current) return

    const video = videoRef.current

    // Load saved progress
    const loadProgress = async () => {
      try {
        const response = await fetch(`/api/watch-progress?episodeId=${episodeId}`)
        const data = await response.json()
        if (data.progress && video) {
          video.currentTime = data.progress
        }
      } catch (error) {
        console.error('Error loading watch progress:', error)
      }
    }

    loadProgress()

    // Save progress periodically
    const saveProgress = async () => {
      if (!video || !session) return

      const currentTime = Date.now()
      if (currentTime - lastSaveTime.current < SAVE_INTERVAL) return

      try {
        await fetch('/api/watch-progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            episodeId,
            animeId,
            progress: video.currentTime,
          }),
        })
        lastSaveTime.current = currentTime
      } catch (error) {
        console.error('Error saving watch progress:', error)
      }
    }

    const handleTimeUpdate = () => {
      saveProgress()
    }

    video.addEventListener('timeupdate', handleTimeUpdate)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
    }
  }, [session, episodeId, animeId, videoRef])

  return null
} 