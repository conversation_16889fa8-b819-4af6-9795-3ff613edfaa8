'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { CheckCircleIcon, EyeIcon } from '@heroicons/react/24/outline'
import { CheckCircleIcon as CheckCircleSolidIcon } from '@heroicons/react/24/solid'
import { useWatchedEpisodes } from '@/hooks/useWatchedEpisodes'

interface WatchedEpisodeButtonProps {
  animeSlug: string
  episodeId: string
  className?: string
  asChild?: boolean // Indica se deve renderizar como div em vez de button
  initialWatched?: boolean // Estado inicial (opcional)
}

/**
 * Componente de botão para marcar/desmarcar episódios como assistidos
 *
 * Este componente usa o hook useWatchedEpisodes para gerenciar o estado
 * e a comunicação com o servidor.
 */
export default function WatchedEpisodeButton({
  animeSlug,
  episodeId,
  className = '',
  asChild = false,
  initialWatched
}: WatchedEpisodeButtonProps) {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(false)

  // Usar o hook useWatchedEpisodes para gerenciar o estado
  const { isEpisodeWatched, updateWatchedStatus } = useWatchedEpisodes(animeSlug)

  // Determinar se o episódio está assistido
  const isWatched = initialWatched !== undefined ? initialWatched : isEpisodeWatched(episodeId)

  // Função para alternar o status de assistido
  const toggleWatchedStatus = async (e: React.MouseEvent) => {
    // Impedir propagação do evento para evitar que o clique no botão também acione o clique no card do episódio
    e.stopPropagation()

    if (!session) return
    if (isLoading) return

    try {
      setIsLoading(true)

      // Chamar a função do hook para atualizar o status
      const newStatus = !isWatched
      await updateWatchedStatus(episodeId, newStatus)
    } finally {
      setIsLoading(false)
    }
  }

  // Conteúdo do botão/div
  const content = isWatched ? (
    <CheckCircleSolidIcon className="w-5 h-5" />
  ) : (
    <EyeIcon className="w-5 h-5" />
  )

  // Classes comuns
  const commonClasses = `flex items-center justify-center p-2 rounded-full transition-colors ${
    isWatched
      ? 'bg-green-600/20 text-green-500 hover:bg-green-600/30'
      : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'
  } ${className} ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`

  // Título comum
  const title = isWatched ? 'Marcar como não assistido' : 'Marcar como assistido'

  // Renderizar como div ou button dependendo do valor de asChild
  if (asChild) {
    return (
      <div
        onClick={(e) => toggleWatchedStatus(e)}
        className={`${commonClasses} cursor-pointer`}
        title={title}
        role="button"
        tabIndex={0}
        aria-label={title}
      >
        {content}
      </div>
    )
  } else {
    return (
      <button
        onClick={(e) => toggleWatchedStatus(e)}
        disabled={isLoading}
        className={commonClasses}
        title={title}
        aria-label={title}
      >
        {content}
      </button>
    )
  }
}
