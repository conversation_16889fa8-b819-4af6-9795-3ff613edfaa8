import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { writeFile, unlink } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { existsSync } from 'fs';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const image = formData.get('image') as File;

    if (!image) {
      return NextResponse.json(
        { error: 'Nenhuma imagem enviada' },
        { status: 400 }
      );
    }

    // Verifica o tipo do arquivo
    if (!image.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Arquivo deve ser uma imagem' },
        { status: 400 }
      );
    }

    // Verifica o tamanho do arquivo (máximo 5MB)
    if (image.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Imagem deve ter no máximo 5MB' },
        { status: 400 }
      );
    }

    // Busca o usuário atual para obter a imagem antiga
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { image: true }
    });

    // Remove a imagem antiga se existir
    if (currentUser?.image) {
      const oldImagePath = join(process.cwd(), 'public', currentUser.image);
      if (existsSync(oldImagePath)) {
        await unlink(oldImagePath);
      }
    }

    const bytes = await image.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Gera um nome único para o arquivo
    const uniqueId = uuidv4();
    const extension = image.name.split('.').pop();
    const fileName = `${uniqueId}.${extension}`;

    // Define o caminho do arquivo
    const uploadDir = join(process.cwd(), 'public', 'uploads');
    const filePath = join(uploadDir, fileName);

    // Salva o arquivo
    await writeFile(filePath, buffer);

    // Atualiza a URL do avatar no banco
    const imageUrl = `/uploads/${fileName}`;
    await prisma.user.update({
      where: { email: session.user.email },
      data: { image: imageUrl }
    });

    return NextResponse.json({ imageUrl });
  } catch (error) {
    console.error('Error uploading avatar:', error);
    return NextResponse.json(
      { error: 'Erro ao fazer upload da imagem' },
      { status: 500 }
    );
  }
} 