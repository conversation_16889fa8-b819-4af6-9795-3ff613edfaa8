import { NextRequest, NextResponse } from 'next/server'
import * as cheerio from 'cheerio'

// Helper function to extract video URL from HTML content
async function extractVideoUrl(html: string, sourceType: string, originalUrl: string): Promise<string | null> {
  try {
    const $ = cheerio.load(html)

    if (sourceType === 'animefire') {
      // AnimeFirePlus extraction logic
      console.log('Extracting from AnimeFirePlus')

      // Método 1: Procurar por elementos de vídeo diretamente com foco no padrão específico do site
      console.log('Looking for video elements with specific attributes...')

      // Procurar por vídeo com classe vjs-tech (VideoJS player)
      const vjsVideoElements = $('video.vjs-tech').toArray()
      for (const video of vjsVideoElements) {
        // Verificar atributo src
        const src = $(video).attr('src')
        if (src) {
          console.log('Found VideoJS video element with src:', src)
          return src
        }

        // Verificar atributo data-video-src
        const dataVideoSrc = $(video).attr('data-video-src')
        if (dataVideoSrc) {
          console.log('Found VideoJS video element with data-video-src:', dataVideoSrc)
          return dataVideoSrc
        }
      }

      // Procurar por qualquer elemento de vídeo
      const videoElements = $('video').toArray()
      for (const video of videoElements) {
        // Verificar atributo src
        const src = $(video).attr('src')
        if (src) {
          console.log('Found video element with src:', src)
          return src
        }

        // Verificar atributos de dados específicos
        const dataAttrs = ['data-video-src', 'data-src', 'data-source']
        for (const attr of dataAttrs) {
          const dataSrc = $(video).attr(attr)
          if (dataSrc) {
            console.log(`Found video element with ${attr}:`, dataSrc)
            return dataSrc
          }
        }

        // Verificar source tags dentro do vídeo
        const sources = $(video).find('source')
        if (sources.length > 0) {
          const sourceSrc = $(sources[0]).attr('src')
          if (sourceSrc) {
            console.log('Found video source tag:', sourceSrc)
            return sourceSrc
          }
        }
      }

      // Método 2: Procurar por iframes de player
      const videoIframe = $('iframe#playerdesktop, iframe#player-iframe, iframe[src*="player"], iframe[src*="embed"]')
      if (videoIframe.length) {
        const src = videoIframe.attr('src')
        console.log('Found iframe source:', src)
        return src || null
      }

      // Método 3: Procurar por padrões específicos em scripts
      const scripts = $('script').toArray()
      for (const script of scripts) {
        const content = $(script).html() || ''

        // Procurar por URLs de vídeo em várias formas
        const patterns = [
          /videoSource\s*=\s*['"]([^'"]+)['"]/i,
          /src\s*:\s*['"]([^'"]+)['"]/i,
          /file\s*:\s*['"]([^'"]+)['"]/i,
          /source\s*:\s*['"]([^'"]+)['"]/i,
          /url\s*:\s*['"]([^'"]+\.mp4[^'"]*)['"]/i,
          /['"]([^'"]+\.mp4[^'"]*)['"]/i,
          /['"]([^'"]+\/video[^'"]*)['"]/i,
          /['"]([^'"]+\/play[^'"]*)['"]/i
        ]

        for (const pattern of patterns) {
          const match = content.match(pattern)
          if (match && match[1]) {
            // Verificar se é uma URL de vídeo válida
            if (match[1].includes('.mp4') ||
                match[1].includes('/video') ||
                match[1].includes('/play') ||
                match[1].includes('stream')) {
              console.log('Found video source in script:', match[1])
              return match[1]
            }
          }
        }

        // Procurar por objetos JSON que possam conter a URL do vídeo
        try {
          // Procurar por player_options
          if (content.includes('player_options')) {
            const jsonMatch = content.match(/player_options\s*=\s*({.*?});/s)
            if (jsonMatch && jsonMatch[1]) {
              const playerOptions = JSON.parse(jsonMatch[1])
              if (playerOptions.file) {
                console.log('Found video source in player_options:', playerOptions.file)
                return playerOptions.file
              }
              if (playerOptions.source) {
                console.log('Found video source in player_options:', playerOptions.source)
                return playerOptions.source
              }
            }
          }

          // Procurar por qualquer objeto que possa conter uma URL de vídeo
          const jsonMatches = content.match(/(\{.*?\})/gs)
          if (jsonMatches) {
            for (const jsonStr of jsonMatches) {
              try {
                if (jsonStr.includes('mp4') || jsonStr.includes('video') || jsonStr.includes('source')) {
                  const obj = JSON.parse(jsonStr)
                  if (obj.file) return obj.file
                  if (obj.source) return obj.source
                  if (obj.src) return obj.src
                  if (obj.url) return obj.url
                }
              } catch (e) {
                // Ignorar erros de parse JSON
              }
            }
          }
        } catch (e) {
          console.error('Error parsing JSON from script:', e)
        }
      }

      // Método 4: Procurar por qualquer iframe que possa conter o vídeo
      const iframes = $('iframe').toArray()
      for (const iframe of iframes) {
        const src = $(iframe).attr('src')
        if (src && (
          src.includes('player') ||
          src.includes('embed') ||
          src.includes('video') ||
          src.includes('watch') ||
          src.includes('stream')
        )) {
          console.log('Found potential video iframe:', src)
          return src
        }
      }

      // Método 5: Procurar por elementos com data attributes que possam conter URLs
      const dataElements = $('[data-video], [data-src], [data-source], [data-url], [data-file]').toArray()
      for (const el of dataElements) {
        const dataVideo = $(el).attr('data-video')
        const dataSrc = $(el).attr('data-src')
        const dataSource = $(el).attr('data-source')
        const dataUrl = $(el).attr('data-url')
        const dataFile = $(el).attr('data-file')

        const potentialUrl = dataVideo || dataSrc || dataSource || dataUrl || dataFile
        if (potentialUrl && (
          potentialUrl.includes('.mp4') ||
          potentialUrl.includes('/video') ||
          potentialUrl.includes('/stream')
        )) {
          console.log('Found video URL in data attribute:', potentialUrl)
          return potentialUrl
        }
      }

      // Método 6: Procurar por padrões de URL de vídeo em todo o HTML
      const htmlString = $.html()

      // Padrões comuns de URLs de vídeo
      const urlPatterns = [
        /https?:\/\/[^"'\s]+\.mp4[^"'\s]*/g,
        /https?:\/\/[^"'\s]+\/video[^"'\s]*/g,
        /https?:\/\/[^"'\s]+\/stream[^"'\s]*/g,
        /https?:\/\/[^"'\s]+\/play[^"'\s]*/g
      ]

      for (const pattern of urlPatterns) {
        const matches = htmlString.match(pattern)
        if (matches && matches.length > 0) {
          for (const match of matches) {
            // Verificar se parece uma URL de vídeo válida
            if (match.includes('.mp4') ||
                match.includes('/video') ||
                match.includes('/stream') ||
                match.includes('/play')) {
              console.log('Found video URL in HTML:', match)
              return match
            }
          }
        }
      }
    } else if (sourceType === 'blogger') {
      // Blogger video extraction logic
      console.log('Extracting from Blogger')

      // Look for Blogger video iframe
      const bloggerIframe = $('iframe[src*="blogger.com"]')
      if (bloggerIframe.length) {
        const src = bloggerIframe.attr('src')
        console.log('Found Blogger iframe:', src)
        // Retornar diretamente o src do iframe para URLs do Blogger
        if (src) {
          return src
        }
      }

      // Try to find Blogger video ID in script tags
      const scripts = $('script').toArray()
      for (const script of scripts) {
        const content = $(script).html() || ''

        // Look for Blogger video ID
        const bloggerIdMatch = content.match(/videoId\s*=\s*['"]([^'"]+)['"]/i)

        if (bloggerIdMatch && bloggerIdMatch[1]) {
          const bloggerUrl = `https://www.blogger.com/video.g?token=${bloggerIdMatch[1]}`
          console.log('Found Blogger video ID:', bloggerIdMatch[1], 'URL:', bloggerUrl)
          return bloggerUrl
        }
      }

      // Procurar por qualquer iframe que possa ser do Blogger
      const iframes = $('iframe').toArray()
      for (const iframe of iframes) {
        const src = $(iframe).attr('src')
        if (src && (
          src.includes('blogger.com') ||
          src.includes('blogspot.com') ||
          src.includes('google.com/videoplay') ||
          src.includes('drive.google.com/file')
        )) {
          console.log('Found potential Blogger/Google iframe:', src)
          return src
        }
      }
    } else {
      // Generic extraction for any video source
      console.log('Using generic extraction')

      // Try to find any video player iframe
      const iframes = $('iframe').toArray()
      for (const iframe of iframes) {
        const src = $(iframe).attr('src')
        if (src && (
          src.includes('player') ||
          src.includes('embed') ||
          src.includes('video') ||
          src.includes('watch')
        )) {
          console.log('Found generic iframe:', src)
          return src
        }
      }

      // Try to find video tags
      const videoTags = $('video source').toArray()
      for (const video of videoTags) {
        const src = $(video).attr('src')
        if (src) {
          console.log('Found video source tag:', src)
          return src
        }
      }

      // Try to find video tags directly
      const videoElements = $('video').toArray()
      for (const video of videoElements) {
        const src = $(video).attr('src')
        if (src) {
          console.log('Found video element with src:', src)
          return src
        }
      }
    }

    console.log('No video URL found in HTML')
    return null
  } catch (error) {
    console.error('Error extracting video URL:', error)
    return null
  }
}

// Detect the source type from URL
function detectSourceType(url: string): string {
  // Check if it's a direct MP4 URL or LightSpeed URL
  if (url.toLowerCase().endsWith('.mp4') ||
      url.includes('/mp4/') ||
      url.includes('/mp4_temp/') ||
      url.includes('lightspeedst.net')) {
    return 'direct_mp4'
  } else if (url.includes('blogger.com') ||
             url.includes('blogspot.com') ||
             url.includes('google.com/videoplay') ||
             url.includes('drive.google.com/file') ||
             url.includes('googlevideo.com')) {
    return 'blogger'
  } else if (url.includes('animefire.plus/video/') ||
             (url.includes('animefire.plus') && url.includes('/video/'))) {
    // Detecta URLs internas do AnimeFirePlus (formato de vídeo)
    return 'animefire_internal'
  } else if (url.includes('animefire.plus/animes/') ||
             (url.includes('animefire.plus') && url.includes('/animes/'))) {
    // Detecta URLs de páginas de anime do AnimeFirePlus
    return 'animefire_page'
  }
  // Para outros casos, usamos o tipo genérico
  return 'generic'
}

// Function to fetch internal AnimeFirePlus URLs and extract the LightSpeed URL
async function fetchInternalUrl(internalUrl: string): Promise<string | null> {
  try {
    console.log('Fetching internal URL:', internalUrl)

    // Garantir que a URL seja absoluta
    if (internalUrl.startsWith('/')) {
      internalUrl = 'https://animefire.plus' + internalUrl
    } else if (!internalUrl.startsWith('http')) {
      internalUrl = 'https://animefire.plus/' + internalUrl
    }

    // Adicionar cabeçalhos específicos para simular um navegador real
    const response = await fetch(internalUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        'Referer': 'https://animefire.plus/',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      },
      cache: 'no-store',
      // Não seguir redirecionamentos automaticamente
      redirect: 'manual'
    })

    if (!response.ok) {
      console.log(`Failed to fetch internal URL: ${response.status} ${response.statusText}`)
      return null
    }

    // Primeiro, tenta analisar a resposta
    const text = await response.text()
    console.log('Response text:', text.substring(0, 200) + '...')

    // Verifica o tipo de conteúdo
    const contentType = response.headers.get('content-type')
    console.log('Content-Type:', contentType)

    // Verifica se o conteúdo parece ser JSON
    if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
      console.log('Response appears to be JSON, using JSON extraction function')

      // Tenta extrair a URL do JSON
      const jsonVideoUrl = extractLightSpeedUrlFromJson(text)
      if (jsonVideoUrl && jsonVideoUrl.includes('lightspeedst.net')) {
        console.log('Successfully extracted LightSpeed URL from JSON response:', jsonVideoUrl)
        return jsonVideoUrl
      }

      // Se não conseguiu extrair, tenta procurar diretamente por URLs do LightSpeed no texto
      const directMatches = text.match(/https:\/\/lightspeedst\.net\/[^"'\s]+\.mp4/g)
      if (directMatches && directMatches.length > 0) {
        // Procurar por 720p primeiro
        const hdMatch = directMatches.find(url => url.includes('720p'))
        if (hdMatch) {
          console.log('Found direct HD LightSpeed URL in JSON text:', hdMatch)
          return hdMatch
        }

        console.log('Found direct LightSpeed URL in JSON text:', directMatches[0])
        return directMatches[0]
      }

      console.log('Could not extract LightSpeed URL from JSON, will try HTML parsing')
    }

    // Se não conseguiu extrair do JSON, trata como HTML
    const html = text
    console.log('Internal page HTML length:', html.length)

    // Usar a função específica para extrair a URL do LightSpeed
    const lightSpeedUrl = extractLightSpeedUrlFromHtml(html)
    if (lightSpeedUrl) {
      return lightSpeedUrl
    }

    // Se não encontrou, tenta com padrões específicos
    // Procurar pelo elemento de vídeo específico que você encontrou
    // Padrões mais flexíveis para capturar o elemento de vídeo em diferentes formatos
    const exactVideoPatterns = [
      /<video[^>]*id="my-video_html5_api"[^>]*class="vjs-tech"[^>]*src="([^"]+)"/,
      /<video[^>]*class="vjs-tech"[^>]*id="my-video_html5_api"[^>]*src="([^"]+)"/,
      /<video[^>]*src="([^"]+)"[^>]*id="my-video_html5_api"[^>]*class="vjs-tech"/,
      /<video[^>]*src="([^"]+)"[^>]*class="vjs-tech"[^>]*id="my-video_html5_api"/,
      /<video[^>]*id="my-video_html5_api"[^>]*src="([^"]+)"[^>]*class="vjs-tech"/,
      /<video[^>]*class="vjs-tech"[^>]*src="([^"]+)"[^>]*id="my-video_html5_api"/
    ]

    for (const pattern of exactVideoPatterns) {
      const exactMatch = html.match(pattern)
      if (exactMatch && exactMatch[1]) {
        console.log('Found exact video element with src:', exactMatch[1])
        // Verificar se é uma URL do LightSpeed
        if (exactMatch[1].includes('lightspeedst.net')) {
          return exactMatch[1]
        }
      }
    }

    // Procurar por qualquer URL do LightSpeed no HTML
    const lightSpeedPattern = /https:\/\/lightspeedst\.net\/[^"'\s]+/g
    const matches = [...html.matchAll(lightSpeedPattern)]
    if (matches.length > 0) {
      for (const match of matches) {
        if (match[0]) {
          console.log('Found LightSpeed URL in HTML:', match[0])
          return match[0]
        }
      }
    }

    // Procurar por qualquer elemento de vídeo com src
    const videoSrcPattern = /<video[^>]*src="(https:\/\/lightspeedst\.net\/[^"]+)"/g
    const videoMatches = [...html.matchAll(videoSrcPattern)]
    if (videoMatches.length > 0) {
      for (const match of videoMatches) {
        if (match[1]) {
          console.log('Found video element with LightSpeed src:', match[1])
          return match[1]
        }
      }
    }

    // Procurar diretamente por URLs do LightSpeed no HTML
    const lightSpeedPatterns = [
      /https:\/\/lightspeedst\.net\/[^"'\s]+\.mp4/g,
      /https:\/\/lightspeedst\.net\/s[0-9]\/mp4[^"'\s]+/g,
      /https:\/\/lightspeedst\.net\/s[0-9]\/mp4_temp[^"'\s]+/g
    ]

    for (const pattern of lightSpeedPatterns) {
      const matches = html.match(pattern)
      if (matches && matches.length > 0) {
        console.log('Found LightSpeed URL in internal page:', matches[0])
        return matches[0]
      }
    }

    // Usar cheerio para analisar o HTML
    const $ = cheerio.load(html)

    // Procurar pelo elemento de vídeo específico
    const specificVideo = $('#my-video_html5_api.vjs-tech')
    if (specificVideo.length > 0) {
      const src = specificVideo.attr('src')
      if (src && src.includes('lightspeedst.net')) {
        console.log('Found specific video element with LightSpeed src:', src)
        return src
      }
    }

    // Procurar por qualquer elemento de vídeo
    const videoElements = $('video')
    for (let i = 0; i < videoElements.length; i++) {
      const src = $(videoElements[i]).attr('src')
      if (src && src.includes('lightspeedst.net')) {
        console.log('Found video element with LightSpeed src:', src)
        return src
      }
    }

    // Procurar por qualquer elemento com src que contenha lightspeedst.net
    const anyElements = $('*[src*="lightspeedst.net"]')
    for (let i = 0; i < anyElements.length; i++) {
      const src = $(anyElements[i]).attr('src')
      if (src) {
        console.log('Found element with LightSpeed src:', src)
        return src
      }
    }

    // Procurar por scripts que possam conter a URL
    const scripts = $('script')
    for (let i = 0; i < scripts.length; i++) {
      const scriptContent = $(scripts[i]).html() || ''
      if (scriptContent.includes('lightspeedst.net')) {
        const urlMatch = scriptContent.match(/['"]?(https:\/\/lightspeedst\.net\/[^'"]+)['"]?/)
        if (urlMatch && urlMatch[1]) {
          console.log('Found LightSpeed URL in script:', urlMatch[1])
          return urlMatch[1]
        }
      }
    }

    console.log('No LightSpeed URL found in internal page')
    return null
  } catch (error) {
    console.error('Error fetching internal URL:', error)
    return null
  }
}

// Interface para representar informações de episódio
interface EpisodeInfo {
  number: number;
  videoUrl: string;
  sourceType: string;
}

// Function to extract Blogger URL from anime page
async function extractBloggerUrlFromAnimePage(animeUrl: string): Promise<string | null> {
  try {
    console.log('Extracting Blogger URL from anime page:', animeUrl)

    // Ensure the URL is an anime page URL
    if (!animeUrl.includes('/animes/')) {
      // Convert video URL to anime URL
      animeUrl = animeUrl.replace('/video/', '/animes/').replace(/\?.*$/, '')
    }

    // Fetch the anime page
    const response = await fetch(animeUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
      }
    })

    if (!response.ok) {
      console.log(`Failed to fetch anime page: ${response.status} ${response.statusText}`)
      return null
    }

    // Get the HTML content
    const html = await response.text()
    console.log('Anime page HTML length:', html.length)

    // Look for Blogger URL in the HTML
    const bloggerMatch = html.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/)
    if (bloggerMatch && bloggerMatch[0]) {
      console.log('Found Blogger URL in anime page:', bloggerMatch[0])
      return bloggerMatch[0]
    }

    // Look for iframe with Blogger URL
    const iframeMatch = html.match(/<iframe[^>]*src=["'](https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+)["'][^>]*>/)
    if (iframeMatch && iframeMatch[1]) {
      console.log('Found Blogger URL in iframe:', iframeMatch[1])
      return iframeMatch[1]
    }

    // Look for data attributes that might contain Blogger URL
    const dataMatch = html.match(/data-[^=]*=["'](https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+)["']/)
    if (dataMatch && dataMatch[1]) {
      console.log('Found Blogger URL in data attribute:', dataMatch[1])
      return dataMatch[1]
    }

    console.log('No Blogger URL found in anime page')
    return null
  } catch (error) {
    console.error('Error extracting Blogger URL from anime page:', error)
    return null
  }
}

// Function to extract all episode URLs from an anime
async function extractAllEpisodeUrls(animeUrl: string): Promise<EpisodeInfo[]> {
  try {
    console.log('Extracting all episode URLs from anime:', animeUrl)

    // Ensure the URL is an anime page URL (without episode number)
    if (!animeUrl.includes('/animes/')) {
      // Convert video URL to anime URL
      animeUrl = animeUrl.replace('/video/', '/animes/').replace(/\?.*$/, '')
    }

    // Extract the base URL without episode number
    const baseUrlMatch = animeUrl.match(/^(https?:\/\/[^\/]+\/animes\/[^\/]+)(?:\/\d+)?$/)
    if (!baseUrlMatch) {
      console.error('Could not extract base URL from:', animeUrl)
      return []
    }

    const baseUrl = baseUrlMatch[1]
    console.log('Base anime URL:', baseUrl)

    // Fetch the anime page to determine total episodes
    const response = await fetch(animeUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
      }
    })

    if (!response.ok) {
      console.log(`Failed to fetch anime page: ${response.status} ${response.statusText}`)
      return []
    }

    // Get the HTML content
    const html = await response.text()
    console.log('Anime page HTML length:', html.length)

    // Use cheerio to parse the HTML
    const $ = cheerio.load(html)

    // Find all episode links
    const episodeLinks: { number: number; url: string }[] = []

    // Método 1: Procurar por links de episódios na lista de episódios
    $('.episodes-list a, .episodes a, .list-episodes a, .episodios a').each((i, el) => {
      const href = $(el).attr('href')
      if (href) {
        // Extract episode number from href
        const episodeMatch = href.match(/\/(\d+)(?:\?|$)/)
        if (episodeMatch && episodeMatch[1]) {
          const episodeNumber = parseInt(episodeMatch[1])
          if (!isNaN(episodeNumber)) {
            episodeLinks.push({ number: episodeNumber, url: href })
          }
        }
      }
    })

    // Método 2: Procurar por elementos com data-id ou data-episode
    $('[data-id], [data-episode]').each((i, el) => {
      const dataId = $(el).attr('data-id') || $(el).attr('data-episode')
      if (dataId && !isNaN(parseInt(dataId))) {
        const episodeNumber = parseInt(dataId)
        episodeLinks.push({ number: episodeNumber, url: `${baseUrl}/${episodeNumber}` })
      }
    })

    // Se não encontrou links, tentar determinar o número total de episódios de outra forma
    if (episodeLinks.length === 0) {
      // Procurar por texto que indique o número total de episódios
      const totalEpisodesMatch = html.match(/total\s+de\s+(\d+)\s+episódios/i) ||
                                html.match(/(\d+)\s+episódios/i) ||
                                html.match(/episódio\s+(\d+)/i)

      if (totalEpisodesMatch && totalEpisodesMatch[1]) {
        const totalEpisodes = parseInt(totalEpisodesMatch[1])
        console.log('Total episodes found in text:', totalEpisodes)

        // Gerar links para todos os episódios
        for (let i = 1; i <= totalEpisodes; i++) {
          episodeLinks.push({ number: i, url: `${baseUrl}/${i}` })
        }
      } else {
        // Se ainda não encontrou, assumir que há pelo menos 12 episódios (valor comum para animes)
        console.log('Could not determine total episodes, assuming 12 episodes')
        for (let i = 1; i <= 12; i++) {
          episodeLinks.push({ number: i, url: `${baseUrl}/${i}` })
        }
      }
    }

    // Ordenar os links por número de episódio
    episodeLinks.sort((a, b) => a.number - b.number)

    console.log(`Found ${episodeLinks.length} episode links`)

    // Extrair URLs de vídeo para cada episódio
    const episodeInfos: EpisodeInfo[] = []

    // Limitar o número de requisições simultâneas para evitar sobrecarga
    const batchSize = 3
    for (let i = 0; i < episodeLinks.length; i += batchSize) {
      const batch = episodeLinks.slice(i, i + batchSize)

      // Processar cada lote em paralelo
      const batchResults = await Promise.all(
        batch.map(async (episode) => {
          try {
            console.log(`Extracting URL for episode ${episode.number}`)

            // Garantir que a URL seja absoluta
            let episodeUrl = episode.url
            if (episodeUrl.startsWith('/')) {
              episodeUrl = `https://animefire.plus${episodeUrl}`
            } else if (!episodeUrl.startsWith('http')) {
              episodeUrl = `${baseUrl}/${episode.number}`
            }

            // Extrair URL do Blogger
            const bloggerUrl = await extractBloggerUrlFromAnimePage(episodeUrl)
            if (bloggerUrl) {
              return {
                number: episode.number,
                videoUrl: bloggerUrl,
                sourceType: 'blogger'
              }
            }

            // Se não encontrou URL do Blogger, tentar extrair URL do LightSpeed
            // Construir URL da página de vídeo
            const videoPageUrl = episodeUrl.replace('/animes/', '/video/') + '?tempsubs=1'

            // Tentar extrair URL do LightSpeed da página de vídeo
            const response = await fetch(videoPageUrl, {
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
                'Referer': 'https://animefire.plus/',
              }
            })

            if (response.ok) {
              const contentType = response.headers.get('content-type')
              if (contentType && contentType.includes('application/json')) {
                const jsonText = await response.text()
                const jsonVideoUrl = extractLightSpeedUrlFromJson(jsonText)

                if (jsonVideoUrl) {
                  return {
                    number: episode.number,
                    videoUrl: jsonVideoUrl,
                    sourceType: jsonVideoUrl.includes('lightspeedst.net') ? 'direct_mp4' : 'blogger'
                  }
                }
              }
            }

            // Se não conseguiu extrair, tentar gerar URL do LightSpeed
            const animeSlug = baseUrl.split('/').pop() || ''
            const lightSpeedUrls = generateLightSpeedUrls(animeSlug, episode.number)

            for (const url of lightSpeedUrls) {
              const isAccessible = await isUrlAccessible(url)
              if (isAccessible) {
                return {
                  number: episode.number,
                  videoUrl: url,
                  sourceType: 'direct_mp4'
                }
              }
            }

            // Se não conseguiu extrair nenhuma URL, retornar null
            return null
          } catch (error) {
            console.error(`Error extracting URL for episode ${episode.number}:`, error)
            return null
          }
        })
      )

      // Adicionar resultados válidos
      for (const result of batchResults) {
        if (result) {
          episodeInfos.push(result)
        }
      }

      // Esperar um pouco entre os lotes para evitar sobrecarga
      if (i + batchSize < episodeLinks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    console.log(`Successfully extracted ${episodeInfos.length} episode URLs`)
    return episodeInfos
  } catch (error) {
    console.error('Error extracting all episode URLs:', error)
    return []
  }
}

// Function to extract LightSpeed URLs from JSON response
function extractLightSpeedUrlFromJson(jsonText: string): string | null {
  try {
    console.log('Extracting LightSpeed URL from JSON response')

    // Limpar o texto JSON antes de analisar (remover caracteres não imprimíveis)
    const cleanJsonText = jsonText.trim().replace(/[\x00-\x1F\x7F-\x9F]/g, '')

    // Verificar se o texto é realmente JSON
    if (!cleanJsonText.startsWith('{') && !cleanJsonText.startsWith('[')) {
      console.log('Text does not appear to be JSON')
      return null
    }

    // Imprimir os primeiros 200 caracteres do JSON para debug
    console.log('JSON text (first 200 chars):', cleanJsonText.substring(0, 200))

    // Try to parse the JSON
    const jsonData = JSON.parse(cleanJsonText)
    console.log('Successfully parsed JSON data')

    // Check if it has the expected structure
    if (jsonData.data && Array.isArray(jsonData.data)) {
      console.log('Found data array in JSON with', jsonData.data.length, 'items')

      // Log the first item for debugging
      if (jsonData.data.length > 0) {
        console.log('First item in data array:', JSON.stringify(jsonData.data[0]))
      }

      // First, look for 720p quality (HD)
      for (const item of jsonData.data) {
        if (item.src && typeof item.src === 'string' &&
            (item.src.includes('lightspeedst.net') || item.src.includes('\\u002flightspeedst.net')) &&
            (item.label === '720p' || item.src.includes('720p.mp4'))) {
          console.log('Found HD (720p) LightSpeed URL in JSON data:', item.src)
          // Remove escaped slashes e caracteres Unicode escapados
          const cleanUrl = item.src
            .replace(/\\\//g, '/')
            .replace(/\\u002f/gi, '/')
          return cleanUrl
        }
      }

      // If 720p not found, look for 480p
      for (const item of jsonData.data) {
        if (item.src && typeof item.src === 'string' &&
            (item.src.includes('lightspeedst.net') || item.src.includes('\\u002flightspeedst.net')) &&
            (item.label === '480p' || item.label === '360p' || item.src.includes('480p.mp4'))) {
          console.log('Found 480p/360p LightSpeed URL in JSON data:', item.src)
          // Remove escaped slashes e caracteres Unicode escapados
          const cleanUrl = item.src
            .replace(/\\\//g, '/')
            .replace(/\\u002f/gi, '/')
          return cleanUrl
        }
      }

      // If specific qualities not found, get the first available LightSpeed URL
      for (const item of jsonData.data) {
        if (item.src && typeof item.src === 'string' &&
            (item.src.includes('lightspeedst.net') || item.src.includes('\\u002flightspeedst.net'))) {
          console.log('Found LightSpeed URL in JSON data:', item.src)
          // Remove escaped slashes e caracteres Unicode escapados
          const cleanUrl = item.src
            .replace(/\\\//g, '/')
            .replace(/\\u002f/gi, '/')
          return cleanUrl
        }
      }

      // Se não encontrou URLs do LightSpeed, procurar por URLs do Blogger
      // Procurar por URLs do Blogger nos dados
      for (const item of jsonData.data) {
        if (item.src && typeof item.src === 'string' && item.src.includes('blogger.com/video.g?token=')) {
          const bloggerUrl = item.src.replace(/\\\//g, '/');
          console.log('Found Blogger URL in JSON data:', bloggerUrl);
          return bloggerUrl;
        }
      }

      // Procurar por URLs do Blogger em todo o JSON
      const jsonString = JSON.stringify(jsonData);
      const bloggerMatches = jsonString.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/g);

      if (bloggerMatches && bloggerMatches.length > 0) {
        const bloggerUrl = bloggerMatches[0].replace(/\\\//g, '/');
        console.log('Found Blogger URL in JSON string:', bloggerUrl);
        return bloggerUrl;
      }

      // Se não encontrou URLs do Blogger, procurar por URLs do Google Video
      let bestGoogleVideoUrl = null
      let bestQualityLabel = '0'

      for (const item of jsonData.data) {
        if (item.src && typeof item.src === 'string' && item.src.includes('googlevideo.com')) {
          if (!bestGoogleVideoUrl || (item.label && parseInt(item.label) > parseInt(bestQualityLabel))) {
            bestGoogleVideoUrl = item.src
            bestQualityLabel = item.label || '0'
          }
        }
      }

      if (bestGoogleVideoUrl) {
        console.log('Found Google Video URL in JSON data:', bestGoogleVideoUrl)
        // Retornamos a URL do Google Video apenas se não encontrarmos URLs do Blogger
        return bestGoogleVideoUrl
      }
    }

    // If the structure is different, search for LightSpeed URLs in the entire JSON string
    const jsonString = JSON.stringify(jsonData)

    // Procurar por URLs do LightSpeed com diferentes padrões de escape
    const patterns = [
      /https:\\?\/\\?\/lightspeedst\.net\\?\/[^"'\\]+/g,
      /https:\\\\?\/\\\\?\/lightspeedst\.net\\\\?\/[^"'\\]+/g,
      /https:\\u002f\\u002flightspeedst\.net\\u002f[^"'\\]+/gi
    ]

    let lightSpeedMatches: string[] = []

    for (const pattern of patterns) {
      const matches = jsonString.match(pattern)
      if (matches && matches.length > 0) {
        lightSpeedMatches = [...lightSpeedMatches, ...matches]
      }
    }

    if (lightSpeedMatches.length > 0) {
      console.log('Found', lightSpeedMatches.length, 'LightSpeed URLs in JSON string')

      // Look for 720p URLs first
      const hdMatches = lightSpeedMatches.filter(url =>
        url.includes('720p.mp4') || url.includes('720p\\u002emp4')
      )
      if (hdMatches.length > 0) {
        // Remove escaped slashes e caracteres Unicode escapados
        const cleanUrl = hdMatches[0]
          .replace(/\\\//g, '/')
          .replace(/\\u002f/gi, '/')
          .replace(/\\u002e/gi, '.')
        console.log('Found HD (720p) LightSpeed URL in JSON string:', cleanUrl)
        return cleanUrl
      }

      // If 720p not found, look for 480p
      const sdMatches = lightSpeedMatches.filter(url =>
        url.includes('480p.mp4') || url.includes('480p\\u002emp4')
      )
      if (sdMatches.length > 0) {
        // Remove escaped slashes e caracteres Unicode escapados
        const cleanUrl = sdMatches[0]
          .replace(/\\\//g, '/')
          .replace(/\\u002f/gi, '/')
          .replace(/\\u002e/gi, '.')
        console.log('Found 480p LightSpeed URL in JSON string:', cleanUrl)
        return cleanUrl
      }

      // If specific qualities not found, get the first URL
      // Remove escaped slashes e caracteres Unicode escapados
      const cleanUrl = lightSpeedMatches[0]
        .replace(/\\\//g, '/')
        .replace(/\\u002f/gi, '/')
        .replace(/\\u002e/gi, '.')
      console.log('Found LightSpeed URL in JSON string:', cleanUrl)
      return cleanUrl
    }

    // Procurar diretamente por padrões de URL do LightSpeed no texto original
    const directPatterns = [
      /https:\/\/lightspeedst\.net\/[^"'\s]+\.mp4/g,
      /https:\\\/\\\/lightspeedst\.net\\\/[^"'\s]+\.mp4/g,
      /https:\\u002f\\u002flightspeedst\.net\\u002f[^"'\s]+\.mp4/gi
    ]

    for (const pattern of directPatterns) {
      const matches = cleanJsonText.match(pattern)
      if (matches && matches.length > 0) {
        // Procurar por 720p primeiro
        const hdMatch = matches.find(url => url.includes('720p'))
        if (hdMatch) {
          const cleanUrl = hdMatch
            .replace(/\\\//g, '/')
            .replace(/\\u002f/gi, '/')
            .replace(/\\u002e/gi, '.')
          console.log('Found direct HD LightSpeed URL in text:', cleanUrl)
          return cleanUrl
        }

        // Se não encontrou 720p, usar a primeira URL
        const cleanUrl = matches[0]
          .replace(/\\\//g, '/')
          .replace(/\\u002f/gi, '/')
          .replace(/\\u002e/gi, '.')
        console.log('Found direct LightSpeed URL in text:', cleanUrl)
        return cleanUrl
      }
    }

    return null
  } catch (error) {
    console.error('Error extracting LightSpeed URL from JSON:', error)
    // Tentar extrair diretamente do texto usando regex como último recurso
    try {
      const directMatches = jsonText.match(/https:\/\/lightspeedst\.net\/[^"'\s]+\.mp4/g)
      if (directMatches && directMatches.length > 0) {
        // Procurar por 720p primeiro
        const hdMatch = directMatches.find(url => url.includes('720p'))
        if (hdMatch) {
          console.log('Found direct HD LightSpeed URL after JSON parse error:', hdMatch)
          return hdMatch
        }

        console.log('Found direct LightSpeed URL after JSON parse error:', directMatches[0])
        return directMatches[0]
      }
    } catch (e) {
      console.error('Failed even with direct regex extraction:', e)
    }
    return null
  }
}

// Function to extract LightSpeed URL directly from HTML
function extractLightSpeedUrlFromHtml(html: string): string | null {
  try {
    console.log('Extracting LightSpeed URL directly from HTML')

    // Padrão específico para o elemento de vídeo
    const exactVideoPatterns = [
      /<video[^>]*id="my-video_html5_api"[^>]*class="vjs-tech"[^>]*src="([^"]+)"/,
      /<video[^>]*class="vjs-tech"[^>]*id="my-video_html5_api"[^>]*src="([^"]+)"/,
      /<video[^>]*src="([^"]+)"[^>]*id="my-video_html5_api"[^>]*class="vjs-tech"/,
      /<video[^>]*src="([^"]+)"[^>]*class="vjs-tech"[^>]*id="my-video_html5_api"/
    ]

    for (const pattern of exactVideoPatterns) {
      const match = html.match(pattern)
      if (match && match[1] && match[1].includes('lightspeedst.net')) {
        console.log('Found exact video element with LightSpeed src:', match[1])
        return match[1]
      }
    }

    // Procurar por qualquer elemento de vídeo com src que contenha lightspeedst.net
    const videoSrcPattern = /<video[^>]*src="(https:\/\/lightspeedst\.net\/[^"]+)"/g
    const videoMatches = [...html.matchAll(videoSrcPattern)]
    if (videoMatches.length > 0) {
      for (const match of videoMatches) {
        if (match[1]) {
          console.log('Found video element with LightSpeed src:', match[1])
          return match[1]
        }
      }
    }

    // Procurar diretamente por URLs do LightSpeed no HTML
    const lightSpeedPatterns = [
      /https:\/\/lightspeedst\.net\/[^"'\s]+\.mp4/g,
      /https:\/\/lightspeedst\.net\/s[0-9]\/mp4[^"'\s]+/g,
      /https:\/\/lightspeedst\.net\/s[0-9]\/mp4_temp[^"'\s]+/g
    ]

    for (const pattern of lightSpeedPatterns) {
      const matches = [...html.matchAll(pattern)]
      if (matches.length > 0) {
        for (const match of matches) {
          if (match[0]) {
            console.log('Found LightSpeed URL directly in HTML:', match[0])
            return match[0]
          }
        }
      }
    }

    // Procurar por qualquer URL do LightSpeed
    const generalPattern = /https:\/\/lightspeedst\.net\/[^"'\s]+/g
    const generalMatches = [...html.matchAll(generalPattern)]
    if (generalMatches.length > 0) {
      for (const match of generalMatches) {
        if (match[0]) {
          console.log('Found general LightSpeed URL in HTML:', match[0])
          return match[0]
        }
      }
    }

    // Procurar por atributos data-video-src
    const dataVideoSrcPattern = /data-video-src="([^"]+)"/g
    const dataVideoSrcMatches = [...html.matchAll(dataVideoSrcPattern)]
    if (dataVideoSrcMatches.length > 0) {
      for (const match of dataVideoSrcMatches) {
        if (match[1] && match[1].includes('lightspeedst.net')) {
          console.log('Found data-video-src with LightSpeed URL:', match[1])
          return match[1]
        }
      }
    }

    // Procurar por atributos data-src
    const dataSrcPattern = /data-src="([^"]+)"/g
    const dataSrcMatches = [...html.matchAll(dataSrcPattern)]
    if (dataSrcMatches.length > 0) {
      for (const match of dataSrcMatches) {
        if (match[1] && match[1].includes('lightspeedst.net')) {
          console.log('Found data-src with LightSpeed URL:', match[1])
          return match[1]
        }
      }
    }

    console.log('No LightSpeed URL found directly in HTML')
    return null
  } catch (error) {
    console.error('Error extracting LightSpeed URL from HTML:', error)
    return null
  }
}

// Function to check if a URL is accessible
async function isUrlAccessible(url: string): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      }
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.log(`URL not accessible: ${url}`);
    return false;
  }
}



// Function to generate LightSpeed URLs based on anime name and episode number
function generateLightSpeedUrls(animeTitle: string, episodeNumber: string | number): string[] {
  // Normalize anime title for URL
  const normalizedTitle = animeTitle.toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-')     // Replace spaces with hyphens
    .trim();

  // Ensure episode number is a string
  const episode = String(episodeNumber);

  // Common patterns for LightSpeed URLs
  const patterns = [
    // Standard patterns
    `https://lightspeedst.net/s1/mp4/${normalizedTitle}/sd/${episode}.mp4`,
    `https://lightspeedst.net/s1/mp4/${normalizedTitle}/hd/${episode}.mp4`,
    `https://lightspeedst.net/s2/mp4/${normalizedTitle}/sd/${episode}.mp4`,
    `https://lightspeedst.net/s2/mp4/${normalizedTitle}/hd/${episode}.mp4`,
    `https://lightspeedst.net/s3/mp4/${normalizedTitle}/sd/${episode}.mp4`,
    `https://lightspeedst.net/s3/mp4/${normalizedTitle}/hd/${episode}.mp4`,
    `https://lightspeedst.net/s4/mp4/${normalizedTitle}/sd/${episode}.mp4`,
    `https://lightspeedst.net/s4/mp4/${normalizedTitle}/hd/${episode}.mp4`,
    `https://lightspeedst.net/s5/mp4/${normalizedTitle}/sd/${episode}.mp4`,
    `https://lightspeedst.net/s5/mp4/${normalizedTitle}/hd/${episode}.mp4`,

    // Temp folder patterns
    `https://lightspeedst.net/s1/mp4_temp/${normalizedTitle}/${episode}/480p.mp4`,
    `https://lightspeedst.net/s2/mp4_temp/${normalizedTitle}/${episode}/480p.mp4`,
    `https://lightspeedst.net/s3/mp4_temp/${normalizedTitle}/${episode}/480p.mp4`,
    `https://lightspeedst.net/s4/mp4_temp/${normalizedTitle}/${episode}/480p.mp4`,
    `https://lightspeedst.net/s5/mp4_temp/${normalizedTitle}/${episode}/480p.mp4`,

    // Dublado patterns
    `https://lightspeedst.net/s1/mp4/${normalizedTitle}-dublado/sd/${episode}.mp4`,
    `https://lightspeedst.net/s1/mp4/${normalizedTitle}-dublado/hd/${episode}.mp4`,
    `https://lightspeedst.net/s5/mp4/${normalizedTitle}-dublado/sd/${episode}.mp4`,
    `https://lightspeedst.net/s5/mp4/${normalizedTitle}-dublado/hd/${episode}.mp4`,
  ];

  return patterns;
}

// Helper function to follow redirects for URLs
async function followRedirects(url: string, maxRedirects = 5): Promise<string> {
  let currentUrl = url;
  let redirectCount = 0;

  while (redirectCount < maxRedirects) {
    console.log(`Following redirect ${redirectCount + 1}/${maxRedirects}: ${currentUrl}`);

    try {
      // Primeiro tentamos com HEAD para ser mais rápido
      const headResponse = await fetch(currentUrl, {
        method: 'HEAD',
        redirect: 'manual',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        }
      });

      // Check if we got a redirect
      if (headResponse.status >= 300 && headResponse.status < 400) {
        const location = headResponse.headers.get('location');
        if (!location) {
          break;
        }

        // Handle relative URLs
        if (location.startsWith('/')) {
          const baseUrl = new URL(currentUrl);
          currentUrl = `${baseUrl.origin}${location}`;
        } else {
          currentUrl = location;
        }

        redirectCount++;
      } else {
        // No more redirects with HEAD, try GET to be sure
        const getResponse = await fetch(currentUrl, {
          method: 'GET',
          redirect: 'manual',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
          }
        });

        if (getResponse.status >= 300 && getResponse.status < 400) {
          const location = getResponse.headers.get('location');
          if (!location) {
            break;
          }

          // Handle relative URLs
          if (location.startsWith('/')) {
            const baseUrl = new URL(currentUrl);
            currentUrl = `${baseUrl.origin}${location}`;
          } else {
            currentUrl = location;
          }

          redirectCount++;
        } else {
          // No more redirects
          break;
        }
      }
    } catch (error) {
      console.error('Error following redirect:', error);
      break;
    }
  }

  return currentUrl;
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  let url = searchParams.get('url')
  const getAllEpisodes = searchParams.get('all_episodes') === 'true'

  if (!url) {
    return NextResponse.json(
      { error: 'URL não fornecida' },
      { status: 400 }
    )
  }

  try {
    console.log('Buscando URL do episódio:', url)

    // Verificar se o usuário quer extrair todos os episódios
    if (getAllEpisodes) {
      console.log('Modo de extração de todos os episódios ativado')

      // Verificar se é uma URL de anime
      if (url.includes('/animes/')) {
        console.log('Extraindo URLs de todos os episódios')
        const allEpisodes = await extractAllEpisodeUrls(url)

        if (allEpisodes.length > 0) {
          console.log(`Extraídas URLs de ${allEpisodes.length} episódios com sucesso`)
          return NextResponse.json({
            success: true,
            message: `Extraídas URLs de ${allEpisodes.length} episódios com sucesso`,
            episodes: allEpisodes
          })
        } else {
          console.log('Não foi possível extrair URLs de episódios')
          return NextResponse.json({
            success: false,
            message: 'Não foi possível extrair URLs de episódios',
            episodes: []
          })
        }
      } else {
        // Tentar converter para URL de anime
        const animeUrl = url.replace('/video/', '/animes/').replace(/\?.*$/, '')
        console.log('Convertendo para URL de anime:', animeUrl)

        const allEpisodes = await extractAllEpisodeUrls(animeUrl)

        if (allEpisodes.length > 0) {
          console.log(`Extraídas URLs de ${allEpisodes.length} episódios com sucesso`)
          return NextResponse.json({
            success: true,
            message: `Extraídas URLs de ${allEpisodes.length} episódios com sucesso`,
            episodes: allEpisodes
          })
        } else {
          console.log('Não foi possível extrair URLs de episódios')
          return NextResponse.json({
            success: false,
            message: 'Não foi possível extrair URLs de episódios',
            episodes: []
          })
        }
      }
    }

    // Modo normal: extrair URL de um único episódio

    // Verificar se é uma URL de página de anime do AnimeFirePlus
    if (url.includes('animefire.plus/animes/') || url.match(/\/animes\/[^\/]+\/\d+/)) {
      console.log('URL de página de anime do AnimeFirePlus detectada')

      try {
        // Primeiro, tentar extrair a URL do Blogger diretamente da página do anime
        console.log('Tentando extrair URL do Blogger da página do anime')
        const bloggerUrl = await extractBloggerUrlFromAnimePage(url)
        if (bloggerUrl) {
          console.log('URL do Blogger encontrada na página do anime:', bloggerUrl)
          return NextResponse.json({ videoUrl: bloggerUrl, sourceType: 'blogger' })
        }

        // Se não encontrou a URL do Blogger, continuar com o processo normal
        // Fazer uma requisição direta para a página do anime
        console.log('Fazendo requisição direta para a página do anime')
        const animePageResponse = await fetch(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
          }
        })

        if (!animePageResponse.ok) {
          console.log(`Falha ao acessar a página do anime: ${animePageResponse.status}`)
        } else {
          const animePageHtml = await animePageResponse.text()
          console.log('Página do anime obtida, procurando por URL de vídeo')

          // Procurar por URLs do Blogger na página do anime
          const bloggerMatch = animePageHtml.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/)
          if (bloggerMatch && bloggerMatch[0]) {
            console.log('URL do Blogger encontrada na página do anime:', bloggerMatch[0])
            return NextResponse.json({ videoUrl: bloggerMatch[0], sourceType: 'blogger' })
          }

          // Procurar por iframe com URL do Blogger
          const iframeMatch = animePageHtml.match(/<iframe[^>]*src=["'](https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+)["'][^>]*>/)
          if (iframeMatch && iframeMatch[1]) {
            console.log('URL do Blogger encontrada em iframe na página do anime:', iframeMatch[1])
            return NextResponse.json({ videoUrl: iframeMatch[1], sourceType: 'blogger' })
          }

          // Se não encontrou URL do Blogger, procurar por elementos com data-video-src
          const dataVideoSrcMatch = animePageHtml.match(/data-video-src="([^"]+)"/i)
          if (dataVideoSrcMatch && dataVideoSrcMatch[1]) {
            const videoPageUrl = dataVideoSrcMatch[1]
            console.log('URL da página de vídeo encontrada:', videoPageUrl)

            // Usar a URL da página de vídeo para o restante do processo
            url = videoPageUrl
          } else {
            console.log('Não foi possível encontrar a URL da página de vídeo na página do anime')

            // Tentar extrair o slug e o número do episódio para construir a URL
            const match = url.match(/animefire\.plus\/animes\/([^\/]+)\/(\d+)/)
            if (match && match.length >= 3) {
              const animeSlug = match[1]
              const episodeNumber = match[2]
              console.log(`Extraído slug: ${animeSlug}, episódio: ${episodeNumber}`)

              // Construir a URL da página de vídeo
              const constructedVideoUrl = `https://animefire.plus/video/${animeSlug}/${episodeNumber}?tempsubs=1`
              console.log('URL da página de vídeo construída:', constructedVideoUrl)

              // Usar a URL construída para o restante do processo
              url = constructedVideoUrl
            }
          }
        }
      } catch (error) {
        console.error('Erro ao processar a página do anime:', error)
      }
    }

    // Verificar se é uma URL do Blogger ou Google Video
    if (url.includes('blogger.com') ||
        url.includes('blogspot.com') ||
        url.includes('google.com/videoplay') ||
        url.includes('drive.google.com/file') ||
        url.includes('googlevideo.com')) {
      console.log('URL do Blogger/Google detectada, retornando diretamente')
      return NextResponse.json({ videoUrl: url, sourceType: 'blogger' })
    }

    // Verificar se a URL é de um JSON que contém URLs do Google Video
    if (url.includes('animefire.plus') && url.includes('?tempsubs=')) {
      try {
        console.log('Verificando se a URL contém URLs do Blogger ou Google Video')
        const response = await fetch(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Referer': 'https://animefire.plus/',
          }
        })

        if (response.ok) {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const jsonText = await response.text()

            // Verificar se o JSON contém URLs do Blogger
            if (jsonText.includes('blogger.com/video.g?token=')) {
              console.log('JSON contém URLs do Blogger')

              try {
                // Extrair a URL do Blogger usando regex
                const bloggerMatch = jsonText.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/);
                if (bloggerMatch && bloggerMatch[0]) {
                  const bloggerUrl = bloggerMatch[0].replace(/\\\//g, '/');
                  console.log('Encontrada URL do Blogger:', bloggerUrl);
                  return NextResponse.json({ videoUrl: bloggerUrl, sourceType: 'blogger' });
                }

                // Se não encontrou com regex, tentar analisar o JSON
                const jsonData = JSON.parse(jsonText);

                // Procurar por URLs do Blogger nos metadados ou em outras propriedades
                const jsonString = JSON.stringify(jsonData);
                const bloggerUrlMatches = jsonString.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/g);

                if (bloggerUrlMatches && bloggerUrlMatches.length > 0) {
                  const bloggerUrl = bloggerUrlMatches[0].replace(/\\\//g, '/');
                  console.log('Encontrada URL do Blogger no JSON:', bloggerUrl);
                  return NextResponse.json({ videoUrl: bloggerUrl, sourceType: 'blogger' });
                }
              } catch (jsonError) {
                console.error('Erro ao analisar JSON para Blogger:', jsonError);
              }
            }
            // Se não encontrou URLs do Blogger, verificar se contém URLs do Google Video
            else if (jsonText.includes('googlevideo.com')) {
              console.log('JSON contém URLs do Google Video')

              try {
                // Primeiro, tentar encontrar a URL do Blogger na página
                const pageResponse = await fetch(url.replace('/video/', '/animes/').replace(/\?.*$/, ''), {
                  headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                  }
                });

                if (pageResponse.ok) {
                  const pageHtml = await pageResponse.text();
                  const bloggerMatch = pageHtml.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/);

                  if (bloggerMatch && bloggerMatch[0]) {
                    console.log('Encontrada URL do Blogger na página:', bloggerMatch[0]);
                    return NextResponse.json({ videoUrl: bloggerMatch[0], sourceType: 'blogger' });
                  }
                }

                // Se não encontrou a URL do Blogger, usar a URL do Google Video como último recurso
                const jsonData = JSON.parse(jsonText)

                if (jsonData.data && Array.isArray(jsonData.data) && jsonData.data.length > 0) {
                  // Procurar pela melhor qualidade disponível
                  let bestQualityUrl = null
                  let bestQualityLabel = '0'

                  for (const item of jsonData.data) {
                    if (item.src && item.src.includes('googlevideo.com')) {
                      if (!bestQualityUrl || (item.label && parseInt(item.label) > parseInt(bestQualityLabel))) {
                        bestQualityUrl = item.src
                        bestQualityLabel = item.label || '0'
                      }
                    }
                  }

                  if (bestQualityUrl) {
                    console.log('Encontrada URL do Google Video de melhor qualidade:', bestQualityUrl)
                    return NextResponse.json({ videoUrl: bestQualityUrl, sourceType: 'blogger' })
                  }
                }
              } catch (jsonError) {
                console.error('Erro ao analisar JSON para Google Video:', jsonError)
              }
            }
          }
        }
      } catch (error) {
        console.error('Erro ao verificar URL do Blogger/Google Video:', error)
      }
    }

    // Verificar se é uma URL interna do AnimeFirePlus
    if (url.includes('animefire.plus/video/') || url.includes('/video/')) {
      console.log('URL interna do AnimeFirePlus detectada, tentando extrair diretamente')

      // Tenta fazer uma requisição direta primeiro para verificar se retorna JSON
      try {
        console.log('Tentando requisição direta para verificar se retorna JSON')
        const directResponse = await fetch(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Referer': 'https://animefire.plus/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
          cache: 'no-store'
        })

        // Verificar o tipo de conteúdo
        const contentType = directResponse.headers.get('content-type')
        console.log('Content-Type da resposta direta:', contentType)

        if (contentType && contentType.includes('application/json')) {
          console.log('Resposta direta é JSON, tentando extrair URL do LightSpeed')
          const jsonText = await directResponse.text()

          // Tentar extrair a URL do JSON
          const jsonVideoUrl = extractLightSpeedUrlFromJson(jsonText)
          if (jsonVideoUrl && jsonVideoUrl.includes('lightspeedst.net')) {
            console.log('URL do LightSpeed extraída com sucesso do JSON direto:', jsonVideoUrl)
            return NextResponse.json({ videoUrl: jsonVideoUrl, sourceType: 'direct_mp4' })
          }
        }
      } catch (directError) {
        console.error('Erro na requisição direta:', directError)
      }

      // Se a requisição direta falhar, tenta com a função fetchInternalUrl
      for (let attempt = 1; attempt <= 3; attempt++) {
        console.log(`Tentativa ${attempt} de extrair URL do LightSpeed usando fetchInternalUrl`)
        const lightSpeedUrl = await fetchInternalUrl(url)
        if (lightSpeedUrl && lightSpeedUrl.includes('lightspeedst.net')) {
          console.log('URL do LightSpeed extraída com sucesso:', lightSpeedUrl)
          return NextResponse.json({ videoUrl: lightSpeedUrl, sourceType: 'direct_mp4' })
        }

        // Espera um pouco antes de tentar novamente
        if (attempt < 3) {
          console.log('Aguardando antes da próxima tentativa...')
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      // Tenta uma abordagem mais direta como último recurso
      try {
        console.log('Tentando abordagem direta como último recurso')
        const lastResortResponse = await fetch(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          }
        })

        const responseText = await lastResortResponse.text()

        // Tentar extrair diretamente com regex
        const directMatches = responseText.match(/https:\/\/lightspeedst\.net\/[^"'\s]+\.mp4/g)
        if (directMatches && directMatches.length > 0) {
          // Procurar por 720p primeiro
          const hdMatch = directMatches.find(url => url.includes('720p'))
          if (hdMatch) {
            console.log('Found direct HD LightSpeed URL as last resort:', hdMatch)
            return NextResponse.json({ videoUrl: hdMatch, sourceType: 'direct_mp4' })
          }

          console.log('Found direct LightSpeed URL as last resort:', directMatches[0])
          return NextResponse.json({ videoUrl: directMatches[0], sourceType: 'direct_mp4' })
        }
      } catch (lastResortError) {
        console.error('Erro na abordagem de último recurso:', lastResortError)
      }
    }

    // Detect source type
    const sourceType = detectSourceType(url)
    console.log('Detected source type:', sourceType)

    // If it's a direct MP4 URL, return it immediately
    if (sourceType === 'direct_mp4') {
      console.log('Direct MP4 URL detected, returning as is')
      return NextResponse.json({ videoUrl: url, sourceType })
    }

    // If it's an internal AnimeFirePlus URL, fetch it directly
    if (sourceType === 'animefire_internal' || url.includes('/video/')) {
      console.log('Internal AnimeFirePlus URL detected, fetching directly')

      // Tenta várias vezes para garantir que a URL seja extraída corretamente
      for (let attempt = 1; attempt <= 3; attempt++) {
        console.log(`Attempt ${attempt} to fetch internal URL`)
        const internalVideoUrl = await fetchInternalUrl(url)
        if (internalVideoUrl && internalVideoUrl.includes('lightspeedst.net')) {
          console.log('Successfully fetched LightSpeed URL from internal URL:', internalVideoUrl)
          return NextResponse.json({ videoUrl: internalVideoUrl, sourceType: 'direct_mp4' })
        }

        // Espera um pouco antes de tentar novamente
        if (attempt < 3) {
          console.log('Waiting before next attempt...')
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      console.log('Failed to extract LightSpeed URL after multiple attempts')
    }

    // Follow redirects if needed
    let finalUrl = url;
    if (sourceType === 'animefire' || sourceType === 'animefire_internal') {
      finalUrl = await followRedirects(url);
      console.log('Final URL after redirects:', finalUrl);
    }

    // Fetch the page content with retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        response = await fetch(finalUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': sourceType === 'animefire' ? 'https://animefire.plus/' : url,
          },
          cache: 'no-store'
        });

        if (response.ok) {
          break;
        } else {
          console.log(`Attempt ${retryCount + 1}/${maxRetries} failed with status ${response.status}. Retrying...`);
          retryCount++;

          // Esperar um pouco antes de tentar novamente
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`Attempt ${retryCount + 1}/${maxRetries} failed with error:`, error);
        retryCount++;

        // Esperar um pouco antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Se for a última tentativa, lançar o erro
        if (retryCount >= maxRetries) {
          throw error;
        }
      }
    }

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`)
      return NextResponse.json(
        { error: `Erro ao acessar a página: ${response.status} ${response.statusText}` },
        { status: response.status }
      )
    }

    const responseText = await response.text()
    console.log('Response content length:', responseText.length)

    // Verifica se o conteúdo parece ser JSON
    const isJson = responseText.trim().startsWith('{') || responseText.trim().startsWith('[')

    if (isJson) {
      console.log('Response appears to be JSON, trying to extract LightSpeed URL from JSON')
      const jsonVideoUrl = extractLightSpeedUrlFromJson(responseText)

      if (jsonVideoUrl && jsonVideoUrl.includes('lightspeedst.net')) {
        console.log('Successfully extracted LightSpeed URL from JSON response:', jsonVideoUrl)
        return NextResponse.json({ videoUrl: jsonVideoUrl, sourceType: 'direct_mp4' })
      }

      console.log('Could not extract LightSpeed URL from JSON, will try HTML parsing')
    }

    const html = responseText
    console.log('HTML content length:', html.length)

    // Verifica se a URL é uma URL interna do AnimeFirePlus
    if (finalUrl.includes('animefire.plus/video/') || finalUrl.includes('/video/')) {
      console.log('Detected internal AnimeFirePlus URL in HTML, trying to fetch directly')

      // Tenta várias vezes para garantir que a URL seja extraída corretamente
      for (let attempt = 1; attempt <= 3; attempt++) {
        console.log(`Attempt ${attempt} to fetch internal URL from HTML`)
        const internalVideoUrl = await fetchInternalUrl(finalUrl)
        if (internalVideoUrl && internalVideoUrl.includes('lightspeedst.net')) {
          console.log('Successfully fetched LightSpeed URL from internal URL in HTML:', internalVideoUrl)
          return NextResponse.json({ videoUrl: internalVideoUrl, sourceType: 'direct_mp4' })
        }

        // Espera um pouco antes de tentar novamente
        if (attempt < 3) {
          console.log('Waiting before next attempt...')
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      console.log('Failed to extract LightSpeed URL from HTML after multiple attempts')
    }

    // Tenta extrair diretamente do HTML usando a função específica
    let videoUrl = extractLightSpeedUrlFromHtml(html)

    // Se encontrou uma URL do LightSpeed, retorna imediatamente
    if (videoUrl && videoUrl.includes('lightspeedst.net')) {
      console.log('Found LightSpeed URL directly in HTML:', videoUrl)
      return NextResponse.json({ videoUrl, sourceType: 'direct_mp4' })
    }

    // Se não encontrou, tenta com os padrões específicos
    if (!videoUrl) {
      console.log('Trying specific patterns...')

      // Padrões exatos que você encontrou
      const directPatterns = [
        /<video id="my-video_html5_api" class="vjs-tech"[^>]*src="(https:\/\/lightspeedst\.net\/[^"]+)"/,
        /<video class="vjs-tech" id="my-video_html5_api"[^>]*src="(https:\/\/lightspeedst\.net\/[^"]+)"/,
        /src="(https:\/\/lightspeedst\.net\/[^"]+\.mp4)"/,
        /data-video-src="[^"]*"[^>]*src="(https:\/\/lightspeedst\.net\/[^"]+)"/,
        /"src":"(https:\\\/\\\/lightspeedst\.net\\\/[^"]+)"/,
        /"src":"(https:\\\\\/\\\\\/lightspeedst\.net\\\\\/[^"]+)"/
      ]

      // Tenta cada padrão em sequência
      for (const pattern of directPatterns) {
        const match = html.match(pattern)
        if (match && match[1]) {
          videoUrl = match[1]
          console.log('Found LightSpeed URL directly in HTML with exact pattern:', videoUrl)

          // Se encontrou uma URL do LightSpeed, retorna imediatamente
          if (videoUrl.includes('lightspeedst.net')) {
            return NextResponse.json({ videoUrl, sourceType: 'direct_mp4' })
          }

          break
        }
      }
    }

    // Se não encontrou, carrega o HTML com cheerio para análise mais detalhada
    if (!videoUrl) {
      const $ = cheerio.load(html)

      // Verificar diretamente por elementos de vídeo com o padrão específico
      console.log('Direct pattern not found, trying with cheerio...')

      // Método 1: Procurar pelo elemento exato que você encontrou
      console.log('Looking for exact video element pattern...')

      // Primeiro, tenta encontrar o elemento exato com o ID e classe específicos
      const exactVideoElement = $('video#my-video_html5_api.vjs-tech[src]')
      if (exactVideoElement.length > 0) {
        const src = exactVideoElement.attr('src')
        if (src) {
          console.log('Found exact video element with src:', src)
          videoUrl = src
        }
      }

      // Se não encontrou, tenta encontrar o elemento div pai com data-video-src
      if (!videoUrl) {
        const exactDivElement = $('div.video-js[data-video-src]')
        if (exactDivElement.length > 0) {
          const dataVideoSrc = exactDivElement.attr('data-video-src')
          if (dataVideoSrc) {
            console.log('Found exact div element with data-video-src:', dataVideoSrc)

            // Verifica se o data-video-src é uma URL do LightSpeed
            if (dataVideoSrc.includes('lightspeedst.net')) {
              videoUrl = dataVideoSrc
            } else if (dataVideoSrc.includes('/video/')) {
              console.log('Found internal URL, looking for video element inside div')

              // Se for uma URL interna, procura por um vídeo dentro do div
              const videoInDiv = exactDivElement.find('video[src]')
              if (videoInDiv.length > 0) {
                const videoSrc = videoInDiv.attr('src')
                if (videoSrc) {
                  console.log('Found video inside div with src:', videoSrc)
                  videoUrl = videoSrc
                }
              }

              // Se ainda não encontrou, tenta fazer uma requisição para a URL interna
              if (!videoUrl || !videoUrl.includes('lightspeedst.net')) {
                console.log('Internal URL found, but no video element. Will try to fetch the internal URL:', dataVideoSrc)

                // Tenta buscar a URL do LightSpeed a partir da URL interna
                const internalVideoUrl = await fetchInternalUrl(dataVideoSrc)
                if (internalVideoUrl) {
                  console.log('Successfully fetched LightSpeed URL from internal URL:', internalVideoUrl)
                  videoUrl = internalVideoUrl
                } else {
                  videoUrl = null // Resetamos para continuar a busca
                }
              }
            }
          }
        }
      }

      // Método 2: Procurar por qualquer vídeo com ID my-video_html5_api
      if (!videoUrl) {
        const specificVideoElement = $('video#my-video_html5_api')
        if (specificVideoElement.length > 0) {
          // Verificar atributo src
          const src = specificVideoElement.attr('src')
          if (src) {
            console.log('Found specific video element with src:', src)
            videoUrl = src
          }
          // Se não encontrou src, verificar data-video-src
          else {
            const dataVideoSrc = specificVideoElement.attr('data-video-src')
            if (dataVideoSrc) {
              console.log('Found specific video element with data-video-src:', dataVideoSrc)
              videoUrl = dataVideoSrc
            }
          }
        }
      }

      // Método 3: Procurar pelo div pai com data-video-src
      if (!videoUrl) {
        const videoContainer = $('div[data-video-src]')
        if (videoContainer.length > 0) {
          const dataVideoSrc = videoContainer.attr('data-video-src')
          if (dataVideoSrc) {
            console.log('Found video container with data-video-src:', dataVideoSrc)
            videoUrl = dataVideoSrc
          }
        }
      }

      // Se não encontrou, usar a função de extração normal
      if (!videoUrl) {
        videoUrl = await extractVideoUrl(html, sourceType, finalUrl)
      }

      // Procurar por qualquer vídeo com classe vjs-tech se ainda não encontrou
      if (!videoUrl) {
        console.log('Trying to find any vjs-tech video...')
        const vjsVideos = $('video.vjs-tech')
        if (vjsVideos.length > 0) {
          // Verificar atributo src
          const src = vjsVideos.attr('src')
          if (src) {
            console.log('Found vjs-tech video with src:', src)
            videoUrl = src
          }
          // Se não encontrou src, verificar data-video-src
          else {
            const dataVideoSrc = vjsVideos.attr('data-video-src')
            if (dataVideoSrc) {
              console.log('Found vjs-tech video with data-video-src:', dataVideoSrc)
              videoUrl = dataVideoSrc
            }
          }
        }
      }

      // Método 4: Procurar por qualquer elemento com o padrão de URL do LightSpeed
      if (!videoUrl) {
        console.log('Searching for any element with LightSpeed URL pattern...')
        // Procurar por qualquer elemento com atributo src que contenha lightspeedst.net
        $('*[src*="lightspeedst.net"]').each(function() {
          const src = $(this).attr('src')
          if (src) {
            console.log('Found element with LightSpeed URL in src:', src)
            videoUrl = src
            return false // break the loop
          }
        })

        // Se ainda não encontrou, procurar por qualquer elemento com data-video-src
        if (!videoUrl) {
          $('*[data-video-src]').each(function() {
            const dataVideoSrc = $(this).attr('data-video-src')
            if (dataVideoSrc) {
              console.log('Found element with data-video-src:', dataVideoSrc)
              videoUrl = dataVideoSrc
              return false // break the loop
            }
          })
        }
      }

      // Se não encontrou a URL, tenta extrair informações do título e episódio para gerar URLs do LightSpeed
      if (!videoUrl && sourceType === 'generic') {
        console.log('Trying to extract anime title and episode number...')

        // Tenta extrair o título do anime e número do episódio da URL
        let animeTitle = '';
        let episodeNumber = '';

        // Padrão comum: /animes/TITULO/EPISODIO
        const urlMatch = finalUrl.match(/\/animes\/([^\/]+)\/(\d+)/);
        if (urlMatch && urlMatch.length >= 3) {
          animeTitle = urlMatch[1];
          episodeNumber = urlMatch[2];
          console.log(`Extracted anime title: ${animeTitle}, episode: ${episodeNumber}`);

          // Gera possíveis URLs do LightSpeed
          const lightSpeedUrls = generateLightSpeedUrls(animeTitle, episodeNumber);
          console.log(`Generated ${lightSpeedUrls.length} possible LightSpeed URLs`);

          // Testa cada URL para ver se está acessível
          for (const url of lightSpeedUrls) {
            console.log(`Testing URL: ${url}`);
            const isAccessible = await isUrlAccessible(url);
            if (isAccessible) {
              videoUrl = url;
              console.log(`Found accessible LightSpeed URL: ${url}`);
              break;
            }
          }
        }
      }

      // Se ainda não encontrou a URL, tenta extrair diretamente do HTML
      if (!videoUrl) {
        console.log('Trying to extract MP4 URL directly from HTML...')

        // Procura especificamente pelo padrão exato que você encontrou
        const exactPattern = /<video id="my-video_html5_api" class="vjs-tech"[^>]*src="(https:\/\/lightspeedst\.net\/[^"]+)"/g
        const exactMatches = [...html.matchAll(exactPattern)]
        if (exactMatches.length > 0) {
          for (const match of exactMatches) {
            if (match[1]) {
              videoUrl = match[1];
              console.log('Found exact LightSpeed URL pattern:', videoUrl);
              break;
            }
          }
        }

        // Se não encontrou, tenta com padrões mais genéricos
        if (!videoUrl) {
          const specificPatterns = [
            /src="(https:\/\/lightspeedst\.net\/[^"]+)"/g,
            /data-video-src="(https:\/\/lightspeedst\.net\/[^"]+)"/g,
            /data-src="(https:\/\/lightspeedst\.net\/[^"]+)"/g
          ]

          for (const pattern of specificPatterns) {
            const specificMatches = [...html.matchAll(pattern)]
            if (specificMatches.length > 0) {
              for (const match of specificMatches) {
                if (match[1]) {
                  videoUrl = match[1];
                  console.log('Found LightSpeed URL with specific pattern:', videoUrl);
                  break;
                }
              }
              if (videoUrl) break;
            }
          }
        }

        // Se ainda não encontrou, tenta extrair diretamente do HTML usando regex para MP4
        if (!videoUrl) {
          const mp4UrlMatch = html.match(/https?:\/\/[^"'\s]+\.mp4[^"'\s]*/g)
          if (mp4UrlMatch && mp4UrlMatch.length > 0) {
            for (const match of mp4UrlMatch) {
              if (match.includes('lightspeedst.net') || match.includes('.mp4')) {
                videoUrl = match;
                console.log('Found MP4 URL directly in HTML:', videoUrl);
                break;
              }
            }
          }
        }

        // Procura especificamente por URLs do LightSpeed
        if (!videoUrl) {
          const lightSpeedMatch = html.match(/https?:\/\/lightspeedst\.net\/[^"'\s]+/g)
          if (lightSpeedMatch && lightSpeedMatch.length > 0) {
            for (const match of lightSpeedMatch) {
              // Verifica se a URL parece ser de um vídeo
              if (match.includes('.mp4') || match.includes('/mp4/') || match.includes('/mp4_temp/')) {
                videoUrl = match;
                console.log('Found LightSpeed URL in HTML:', videoUrl);
                break;
              }
            }
          }
        }
      }
    }

    if (!videoUrl) {
      console.log('No video URL found')
      return NextResponse.json(
        { error: 'Não foi possível encontrar a URL do vídeo' },
        { status: 404 }
      )
    }

    console.log('Video URL found:', videoUrl)
    return NextResponse.json({ videoUrl, sourceType })
  } catch (error) {
    console.error('Erro ao buscar URL do episódio:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar URL do episódio: ' + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    )
  }
}
