import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  console.log('API subscription-status chamada');

  try {
    const session = await getServerSession(authOptions)
    console.log('Sessão obtida:', session ? 'Sim' : 'Não');

    if (!session?.user) {
      console.log('Usuário não autenticado');
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    // Obter o ID do anime da query string
    const searchParams = request.nextUrl.searchParams
    const animeId = searchParams.get('animeId')
    console.log('AnimeId recebido:', animeId);

    if (!animeId) {
      console.log('AnimeId não fornecido');
      return NextResponse.json(
        { error: 'ID do anime não fornecido' },
        { status: 400 }
      )
    }

    try {
      // Verificar se o usuário está inscrito neste anime
      const subscription = await prisma.animeSubscription.findUnique({
        where: {
          userId_animeId: {
            userId: session.user.id,
            animeId
          }
        }
      })
      console.log('Inscrição encontrada:', subscription ? 'Sim' : 'Não');

      return NextResponse.json({
        isSubscribed: !!subscription
      })
    } catch (error) {
      console.error('Erro ao verificar status da inscrição:', error);
      throw error;
    }
  } catch (error) {
    console.error('Erro ao verificar status da inscrição:', error)
    return NextResponse.json(
      { error: 'Erro ao verificar status da inscrição', details: error instanceof Error ? error.message : 'Erro desconhecido' },
      { status: 500 }
    )
  }
}
