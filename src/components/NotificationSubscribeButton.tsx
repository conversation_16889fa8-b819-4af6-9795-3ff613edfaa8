'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { Bell, BellOff } from 'lucide-react'

interface NotificationSubscribeButtonProps {
  animeId: string
  animeTitle: string
  animeSlug: string
  isOngoing: boolean
}

export default function NotificationSubscribeButton({
  animeId,
  animeTitle,
  animeSlug,
  isOngoing
}: NotificationSubscribeButtonProps) {
  const { data: session } = useSession()
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [permissionState, setPermissionState] = useState<NotificationPermission>('default')

  // Verificar o estado inicial das permissões de notificação
  useEffect(() => {
    if ('Notification' in window) {
      setPermissionState(Notification.permission)
    } else {
      setPermissionState('denied')
    }
  }, [])

  // Verificar se o usuário já está inscrito neste anime
  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (!session?.user) return

      try {
        const response = await fetch(`/api/notifications/subscription-status?animeId=${animeId}`)
        if (response.ok) {
          const data = await response.json()
          // Sempre começar com notificações desativadas, independente do status anterior
          setIsSubscribed(false)
        }
      } catch (error) {
        console.error('Erro ao verificar status da inscrição:', error)
      }
    }

    checkSubscriptionStatus()
  }, [session, animeId])

  // Verificar o status da inscrição
  const checkSubscriptionStatus = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/notifications/subscription-status?animeId=${animeId}`)

      if (response.ok) {
        const data = await response.json()
        setIsSubscribed(data.isSubscribed)
      }
    } catch (error) {
      console.error('Erro ao verificar status da inscrição:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Solicitar permissão para notificações
  const requestNotificationPermission = async () => {
    console.log('Solicitando permissão para notificações...');

    if (!('Notification' in window)) {
      console.log('Notificações não suportadas pelo navegador');
      toast.error('Seu navegador não suporta notificações')
      return false
    }

    try {
      // Verificar se já temos permissão
      if (Notification.permission === 'granted') {
        console.log('Permissão já concedida anteriormente');
        setPermissionState('granted')

        // Mostrar uma notificação de confirmação
        new Notification('AnimesZera', {
          body: 'Notificações ativadas com sucesso! Você receberá alertas sobre novos episódios.',
          icon: '/favicon.svg',
          badge: '/favicon.svg'
        });

        return true
      }

      // Se não temos permissão, solicitar
      toast('Clique em "Permitir" quando o navegador solicitar permissão para notificações', {
        duration: 4000,
        icon: '🔔'
      });

      const permission = await Notification.requestPermission()
      console.log('Permissão obtida:', permission);
      setPermissionState(permission)

      if (permission === 'granted') {
        // Mostrar uma notificação de teste
        new Notification('AnimesZera', {
          body: 'Notificações ativadas com sucesso! Você receberá alertas sobre novos episódios.',
          icon: '/favicon.svg',
          badge: '/favicon.svg'
        });
      }

      return permission === 'granted'
    } catch (error) {
      console.error('Erro ao solicitar permissão:', error)
      toast.error('Erro ao solicitar permissão para notificações')
      return false
    }
  }

  // Inscrever-se para receber notificações
  const subscribeToNotifications = async () => {
    console.log('=== INICIANDO PROCESSO DE INSCRIÇÃO ===');

    if (!session?.user) {
      console.log('❌ Usuário não está logado');
      toast.error('Você precisa estar logado para receber notificações')
      return
    }

    console.log('✅ Usuário logado:', session.user.email);
    setIsLoading(true)
    console.log('⏳ Estado de carregamento ativado');

    try {
      // Sempre solicitar permissão quando o usuário clicar no botão
      console.log('🔔 Solicitando permissão para notificações...');
      const hasPermission = await requestNotificationPermission()
      console.log('🔔 Permissão obtida?', hasPermission);

      if (!hasPermission) {
        console.log('❌ Permissão negada pelo usuário');
        toast.error('Permissão para notificações negada. Você pode ativar nas configurações do navegador.')
        setIsLoading(false)
        return
      }

      // Enviar apenas a inscrição simples para o servidor
      console.log('📡 Enviando inscrição para o servidor...');
      console.log('📡 AnimeId:', animeId);
      console.log('📡 URL da API:', '/api/notifications/subscribe');

      const requestBody = {
        animeId,
        simpleSubscription: true
      };
      console.log('📡 Corpo da requisição:', requestBody);

      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📡 Resposta do servidor - Status:', response.status);
      console.log('📡 Resposta do servidor - OK?', response.ok);

      if (response.ok) {
        console.log('✅ Inscrição realizada com sucesso!');
        const responseData = await response.json();
        console.log('📡 Dados da resposta:', responseData);
        setIsSubscribed(true)
        toast.success(`Você receberá notificações de novos episódios de ${animeTitle}`)
      } else {
        console.log('❌ Erro na resposta do servidor');
        const errorText = await response.text();
        console.log('❌ Texto do erro:', errorText);
        let error;
        try {
          error = JSON.parse(errorText);
        } catch {
          error = { message: errorText };
        }
        throw new Error(error.message || 'Falha ao se inscrever para notificações')
      }
    } catch (error) {
      console.error('❌ Erro ao se inscrever:', error)
      toast.error('Não foi possível ativar as notificações. Tente novamente.')
    } finally {
      console.log('🏁 Finalizando processo de inscrição');
      setIsLoading(false)
    }
  }

  // Cancelar inscrição
  const unsubscribeFromNotifications = async () => {
    if (!session?.user) return

    setIsLoading(true)

    try {
      const response = await fetch('/api/notifications/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          animeId
        })
      })

      if (response.ok) {
        setIsSubscribed(false)
        toast.success(`Notificações de ${animeTitle} desativadas`)
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Falha ao cancelar inscrição')
      }
    } catch (error) {
      console.error('Erro ao cancelar inscrição:', error)
      toast.error('Não foi possível desativar as notificações. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  // Se o anime não estiver em lançamento, não mostrar o botão
  if (!isOngoing) {
    return null
  }

  // Se notificações não forem suportadas pelo navegador, não mostrar o botão
  if (!('Notification' in window)) {
    return null
  }

  return (
    <button
      className={`flex items-center justify-center rounded-full p-2 transition-colors ${
        isLoading ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'
      } ${
        isSubscribed
          ? 'bg-gray-700 hover:bg-gray-600 text-white active:bg-gray-800'
          : 'bg-purple-600 hover:bg-purple-700 text-white active:bg-purple-800'
      }`}
      onClick={(e) => {
        console.log('BOTÃO CLICADO - isLoading:', isLoading, 'isSubscribed:', isSubscribed);

        e.preventDefault();
        e.stopPropagation();

        if (!isLoading) {
          if (isSubscribed) {
            console.log('Cancelando inscrição...');
            unsubscribeFromNotifications();
          } else {
            console.log('Fazendo inscrição...');
            subscribeToNotifications();
          }
        }
      }}
      disabled={isLoading}
      type="button"
      aria-label={isSubscribed ? "Cancelar notificações" : "Receber notificações"}
      title={isSubscribed ? "Cancelar notificações" : "Receber notificações"}
    >
      {isLoading ? (
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
      ) : isSubscribed ? (
        <BellOff size={18} className="flex-shrink-0" />
      ) : (
        <Bell size={18} className="flex-shrink-0" />
      )}
    </button>
  )
}
