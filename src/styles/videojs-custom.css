/* Estilos personalizados para o Video.js */

.video-js {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Botão de play grande no centro */
.video-js .vjs-big-play-button {
  background-color: rgba(139, 92, 246, 0.8);
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: background-color 0.3s ease;
}

.video-js:hover .vjs-big-play-button {
  background-color: rgba(139, 92, 246, 1);
}

/* Barra de controles */
.video-js .vjs-control-bar {
  background-color: rgba(17, 24, 39, 0.8);
  height: 4em;
  padding: 0 1em;
}

/* Sliders (progresso, volume) */
.video-js .vjs-slider {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.video-js .vjs-play-progress {
  background-color: #8b5cf6;
}

.video-js .vjs-play-progress:before {
  color: #8b5cf6;
}

.video-js .vjs-volume-level {
  background-color: #8b5cf6;
}

.video-js .vjs-load-progress div {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Menus */
.video-js .vjs-menu-button-popup .vjs-menu {
  background-color: rgba(17, 24, 39, 0.9);
  border-radius: 4px;
}

.video-js .vjs-menu-content {
  background-color: rgba(17, 24, 39, 0.9);
  border-radius: 4px;
  padding: 0.5em 0;
}

.video-js .vjs-menu-item {
  padding: 0.5em 1em;
  font-size: 0.9em;
}

.video-js .vjs-menu-item:hover,
.video-js .vjs-menu-item.vjs-selected {
  background-color: #8b5cf6;
  color: white;
}

/* Tooltips */
.video-js .vjs-time-tooltip,
.video-js .vjs-volume-tooltip,
.video-js .vjs-playback-rate-value {
  background-color: #8b5cf6;
  border-radius: 4px;
  color: white;
}

/* Botões */
.video-js .vjs-control {
  width: 3em;
}

.video-js .vjs-control:hover {
  color: #8b5cf6;
}

.video-js .vjs-control:focus {
  text-shadow: 0 0 1em white;
}

/* Tempo */
.video-js .vjs-time-control {
  font-size: 0.9em;
  padding-left: 0.5em;
  padding-right: 0.5em;
}

/* Tela cheia */
.video-js.vjs-fullscreen {
  border-radius: 0;
}

/* Animação de carregamento */
.vjs-waiting .vjs-loading-spinner {
  border: 3px solid rgba(139, 92, 246, 0.25);
}

.vjs-waiting .vjs-loading-spinner:before,
.vjs-waiting .vjs-loading-spinner:after {
  border-top-color: #8b5cf6;
}

/* Responsividade para mobile */
@media (max-width: 640px) {
  .video-js .vjs-big-play-button {
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 2.5em;
  }
  
  .video-js .vjs-control-bar {
    height: 3em;
  }
  
  .video-js .vjs-time-divider,
  .video-js .vjs-duration,
  .video-js .vjs-remaining-time {
    display: none;
  }
  
  .video-js .vjs-playback-rate {
    display: none;
  }
}

/* Tema escuro */
.video-js.vjs-theme-dark {
  --vjs-theme-dark--primary: #8b5cf6;
  --vjs-theme-dark--secondary: #fff;
}

/* Botões de qualidade */
.vjs-quality-selector .vjs-menu-button {
  margin-right: 0.5em;
  font-size: 0.8em;
}

.vjs-quality-selector .vjs-menu-button .vjs-icon-placeholder:before {
  content: "HD";
  font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
}

/* Botão de próximo episódio */
.vjs-next-episode {
  position: absolute;
  right: 0;
  top: 0;
  background-color: rgba(139, 92, 246, 0.8);
  color: white;
  padding: 0.5em 1em;
  font-size: 0.9em;
  border-radius: 0 0 0 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.vjs-next-episode:hover {
  background-color: rgba(139, 92, 246, 1);
}

/* Melhorias para acessibilidade */
.video-js .vjs-control:focus,
.video-js .vjs-control:focus:before,
.video-js .vjs-control:hover:before {
  text-shadow: 0 0 1em #fff;
}

.video-js .vjs-control:focus {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}
