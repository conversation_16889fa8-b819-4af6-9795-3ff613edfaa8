import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

// GET /api/favorites - Get user's favorites
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        favorites: {
          include: {
            anime: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Retorna apenas os animes favoritos
    const favorites = user.favorites.map((fav) => fav.anime);

    return NextResponse.json(favorites);
  } catch (error) {
    console.error('Error fetching favorites:', error);
    return NextResponse.json(
      { message: 'Error fetching favorites' },
      { status: 500 }
    );
  }
}

// POST /api/favorites - Add anime to favorites
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { animeId } = await req.json();

    if (!animeId) {
      return NextResponse.json(
        { message: 'Anime ID is required' },
        { status: 400 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Verifica se o favorito já existe
    const existingFavorite = await prisma.favorite.findUnique({
      where: {
        userId_animeId: {
          userId: user.id,
          animeId,
        },
      },
    });

    if (existingFavorite) {
      // Se já existe, retorna o anime favorito
      const anime = await prisma.anime.findUnique({
        where: { id: animeId },
      });
      return NextResponse.json(anime);
    }

    // Cria o favorito
    await prisma.favorite.create({
      data: {
        userId: user.id,
        animeId,
      },
    });

    // Retorna o anime favoritado
    const anime = await prisma.anime.findUnique({
      where: { id: animeId },
    });

    return NextResponse.json(anime);
  } catch (error) {
    console.error('Error adding favorite:', error);
    return NextResponse.json(
      { message: 'Error adding favorite' },
      { status: 500 }
    );
  }
}

// DELETE /api/favorites - Remove anime from favorites
export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { animeId } = await req.json();

    if (!animeId) {
      return NextResponse.json(
        { message: 'Anime ID is required' },
        { status: 400 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Verifica se o favorito existe antes de tentar deletar
    const existingFavorite = await prisma.favorite.findUnique({
      where: {
        userId_animeId: {
          userId: user.id,
          animeId,
        },
      },
    });

    if (!existingFavorite) {
      // Se não existe, retorna sucesso
      return NextResponse.json({ message: 'Favorite already removed' });
    }

    // Remove o favorito
    await prisma.favorite.delete({
      where: {
        userId_animeId: {
          userId: user.id,
          animeId,
        },
      },
    });

    return NextResponse.json({ message: 'Favorite removed successfully' });
  } catch (error) {
    console.error('Error removing favorite:', error);
    return NextResponse.json(
      { message: 'Error removing favorite' },
      { status: 500 }
    );
  }
} 