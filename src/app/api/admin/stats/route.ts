import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions);

    console.log('Sessão do usuário:', session ? {
      email: session.user.email,
      role: session.user.role
    } : 'Nenhuma sessão');

    // Temporariamente desabilitando a verificação de autenticação para depuração
    // if (!session || session.user.role !== 'admin') {
    //   return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    // }

    // Buscar estatísticas
    const [totalAnimes, totalEpisodes, totalUsers] = await Promise.all([
      prisma.anime.count(),
      prisma.episode.count(),
      prisma.user.count()
    ]);

    console.log('Estatísticas obtidas:', { totalAnimes, totalEpisodes, totalUsers });

    return NextResponse.json({
      totalAnimes,
      totalEpisodes,
      totalUsers
    });
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    return NextResponse.json({
      error: 'Erro ao buscar estatísticas',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}
