import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Extrai o ID da URL
    const url = new URL(request.url)
    const id = url.pathname.split('/').pop()

    if (!id) {
      return NextResponse.json(
        { error: 'ID do episódio não fornecido' },
        { status: 400 }
      )
    }

    // Busca o episódio no banco de dados

    const episode = await prisma.episode.findUnique({
      where: {
        id,
      },
    })

    if (!episode || !episode.videoUrl) {
      return NextResponse.json(
        { error: 'Episódio não encontrado' },
        { status: 404 }
      )
    }

    // Check if it's a Blogger video
    const isBloggerVideo = episode.videoUrl.includes('blogger.com') ||
                          episode.videoUrl.includes('blogspot.com');

    if (isBloggerVideo) {
      // For Blogger videos, we redirect to the iframe URL
      return NextResponse.json({
        videoUrl: episode.videoUrl,
        isBloggerVideo: true
      });
    }

    // For direct video URLs, stream the video
    // Determina o referer com base na URL do vídeo
    let referer = 'https://animefire.plus/'
    if (episode.videoUrl.includes('lightspeedst.net')) {
      referer = 'https://lightspeedst.net/'
    }

    // Busca o vídeo via proxy

    // Busca o vídeo da URL original
    const videoResponse = await fetch(episode.videoUrl, {
      headers: {
        'Range': request.headers.get('range') || 'bytes=0-',
        'Accept': '*/*',
        'Accept-Encoding': 'identity;q=1, *;q=0',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Referer': referer,
        'Origin': referer.replace(/\/$/, ''),
        'Sec-Fetch-Dest': 'video',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      cache: 'no-store'
    })

    if (!videoResponse.ok) {

      // Se for erro 401 (Unauthorized) ou 403 (Forbidden), retornar mensagem específica
      if (videoResponse.status === 401 || videoResponse.status === 403) {
        return NextResponse.json(
          {
            error: 'Acesso negado pelo servidor de vídeo',
            status: videoResponse.status,
            message: 'O servidor de vídeo está bloqueando o acesso. Tente novamente mais tarde.'
          },
          { status: videoResponse.status }
        )
      }

      // Se for erro 404 (Not Found), retornar mensagem específica
      if (videoResponse.status === 404) {
        return NextResponse.json(
          {
            error: 'Vídeo não encontrado',
            status: 404,
            message: 'O vídeo solicitado não foi encontrado no servidor.'
          },
          { status: 404 }
        )
      }

      throw new Error(`Erro ao buscar vídeo: ${videoResponse.status} ${videoResponse.statusText}`)
    }

    // Obtém o tipo de conteúdo do vídeo
    const contentType = videoResponse.headers.get('content-type') || 'video/mp4'

    // Cria um novo Response com o stream do vídeo
    const stream = videoResponse.body
    if (!stream) {
      throw new Error('Stream não disponível')
    }

    // Copia os headers relevantes da resposta original
    const headers = new Headers()
    headers.set('Content-Type', contentType)
    headers.set('Content-Disposition', `inline; filename="episode-${episode.number}.mp4"`)
    headers.set('Cache-Control', 'public, max-age=31536000')
    headers.set('Accept-Ranges', 'bytes')

    // Copia os headers de range e content-length se existirem
    const range = videoResponse.headers.get('content-range')
    const contentLength = videoResponse.headers.get('content-length')
    if (range) headers.set('Content-Range', range)
    if (contentLength) headers.set('Content-Length', contentLength)

    return new Response(stream, {
      status: videoResponse.status,
      statusText: videoResponse.statusText,
      headers
    })
  } catch (error) {
    console.error('Error streaming video:', error)
    return NextResponse.json(
      { error: 'Erro ao reproduzir vídeo' },
      { status: 500 }
    )
  }
}