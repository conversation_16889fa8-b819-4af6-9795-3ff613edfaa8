'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { Bell, BellOff } from 'lucide-react'

interface SimpleNotificationTestProps {
  animeId: string
  animeTitle: string
}

export default function SimpleNotificationTest({
  animeId,
  animeTitle
}: SimpleNotificationTestProps) {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)

  const handleClick = async () => {
    console.log('=== TESTE SIMPLES DE NOTIFICAÇÃO ===')
    console.log('AnimeId:', animeId)
    console.log('Session:', session ? 'Logado' : 'Não logado')
    
    if (!session?.user) {
      toast.error('Você precisa estar logado')
      return
    }

    setIsLoading(true)
    
    try {
      // Primeiro, testar permissão de notificação
      if ('Notification' in window) {
        console.log('Permissão atual:', Notification.permission)
        
        if (Notification.permission === 'default') {
          console.log('Solicitando permissão...')
          const permission = await Notification.requestPermission()
          console.log('Permissão obtida:', permission)
          
          if (permission === 'granted') {
            // Mostrar notificação de teste
            new Notification('Teste AnimesZera', {
              body: `Notificações funcionando para ${animeTitle}!`,
              icon: '/favicon.svg'
            })
          }
        } else if (Notification.permission === 'granted') {
          // Mostrar notificação de teste
          new Notification('Teste AnimesZera', {
            body: `Notificações funcionando para ${animeTitle}!`,
            icon: '/favicon.svg'
          })
        }
      }

      // Testar API de inscrição
      console.log('Testando API de inscrição...')
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          animeId,
          simpleSubscription: true
        })
      })

      console.log('Resposta da API:', response.status, response.ok)
      
      if (response.ok) {
        const data = await response.json()
        console.log('Dados da resposta:', data)
        setIsSubscribed(!isSubscribed)
        toast.success(isSubscribed ? 'Inscrição cancelada!' : 'Inscrito com sucesso!')
      } else {
        const errorText = await response.text()
        console.log('Erro da API:', errorText)
        toast.error('Erro na API: ' + errorText)
      }
    } catch (error) {
      console.error('Erro no teste:', error)
      toast.error('Erro: ' + (error instanceof Error ? error.message : 'Erro desconhecido'))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex flex-col items-center gap-2">
      <button
        onClick={handleClick}
        disabled={isLoading}
        className={`flex items-center justify-center rounded-full p-3 transition-colors ${
          isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        } ${
          isSubscribed
            ? 'bg-green-600 hover:bg-green-700 text-white'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {isLoading ? (
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
        ) : isSubscribed ? (
          <BellOff size={20} />
        ) : (
          <Bell size={20} />
        )}
      </button>
      <span className="text-xs text-gray-400">
        {isLoading ? 'Testando...' : isSubscribed ? 'Inscrito' : 'Teste Notificação'}
      </span>
    </div>
  )
}
