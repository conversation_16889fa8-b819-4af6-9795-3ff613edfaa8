# Configuração de Segurança do Painel Administrativo

Este documento explica como configurar as credenciais de admin de forma segura usando variáveis de ambiente.

## 🔒 Segurança

Por questões de segurança, as credenciais de admin agora são configuradas através de variáveis de ambiente em vez de estarem hardcoded no código.

## 📋 Variáveis de Ambiente Necessárias

Adicione as seguintes variáveis de ambiente:

```bash
# Credenciais do Administrador
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=sua-senha-super-segura
```

## 🚀 Configuração na Vercel

### 1. Acesse o Painel da Vercel
- Vá para [vercel.com](https://vercel.com)
- Acesse seu projeto AnimesZera

### 2. Configure as Variáveis de Ambiente
1. Clique em **Settings** (Configurações)
2. V<PERSON> para **Environment Variables** (Variáveis de Ambiente)
3. <PERSON><PERSON><PERSON> as seguintes variáveis:

| Nome | Valor | Ambiente |
|------|-------|----------|
| `ADMIN_EMAIL` | <EMAIL> | Production, Preview, Development |
| `ADMIN_PASSWORD` | sua-senha-super-segura | Production, Preview, Development |

### 3. Redeploy da Aplicação
Após adicionar as variáveis, faça um redeploy:
1. Vá para a aba **Deployments**
2. Clique nos três pontos (...) no último deployment
3. Selecione **Redeploy**

## 🛠️ Configuração Local (Desenvolvimento)

Para desenvolvimento local, crie um arquivo `.env.local`:

```bash
# .env.local
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
```

**⚠️ Importante:** Nunca commite o arquivo `.env.local` no Git!

## ✅ Verificação da Configuração

Após configurar, você pode verificar o status:

1. Acesse `/admin/animes`
2. Faça login com suas credenciais
3. Verifique o card de "Status da Configuração" no topo da página

### Status Possíveis:

- **🟢 Configuração Segura**: Variáveis configuradas corretamente
- **🟡 Atenção**: Usando credenciais padrão (inseguro)
- **🔴 Erro**: Variáveis não configuradas

## 🔐 Recomendações de Segurança

### Para o Email:
- Use um email específico para admin (ex: `<EMAIL>`)
- Evite usar emails pessoais

### Para a Senha:
- Use uma senha forte (mínimo 12 caracteres)
- Inclua letras maiúsculas, minúsculas, números e símbolos
- Não reutilize senhas de outras contas
- Considere usar um gerenciador de senhas

### Exemplos de Senhas Seguras:
```
Admin@AnimesZera2024!
Secure#Panel$2024
MyAdmin@Pass#2024
```

## 🚨 Troubleshooting

### Problema: "Configuração do servidor incompleta"
**Solução:** Verifique se as variáveis `ADMIN_EMAIL` e `ADMIN_PASSWORD` estão configuradas na Vercel.

### Problema: "Email ou senha inválidos"
**Solução:** Verifique se você está usando exatamente os mesmos valores configurados nas variáveis de ambiente.

### Problema: Ainda mostra "credenciais padrão"
**Solução:** Faça um redeploy completo da aplicação após configurar as variáveis.

## 📞 Suporte

Se você encontrar problemas:
1. Verifique os logs da Vercel
2. Confirme que as variáveis estão configuradas corretamente
3. Certifique-se de que fez o redeploy após as mudanças

## 🔄 Alterando Credenciais

Para alterar as credenciais:
1. Atualize as variáveis de ambiente na Vercel
2. Faça um redeploy da aplicação
3. Use as novas credenciais para fazer login

---

**Nota:** Esta configuração garante que suas credenciais de admin estejam seguras e não expostas no código fonte.
