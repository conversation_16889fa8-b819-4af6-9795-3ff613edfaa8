'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'

interface TranslationSummary {
  totalProcessed: number
  translated: number
  skipped: number
  errors: number
  timestamp: string
  translationLog: Array<{
    animeTitle: string
    episodeNumber: number
    originalTitle: string
    translatedTitle: string
    sourceLanguage: string
    confidence: number
    status: 'translated' | 'skipped' | 'error'
  }>
}

export default function EpisodeTitleTranslator() {
  const [isTranslating, setIsTranslating] = useState(false)
  const [lastTranslation, setLastTranslation] = useState<TranslationSummary | null>(null)
  const [animeId, setAnimeId] = useState('')
  const [forceTranslate, setForceTranslate] = useState(false)
  const [targetLanguage, setTargetLanguage] = useState('pt')

  const handleTranslateTitles = async () => {
    if (isTranslating) return

    setIsTranslating(true)
    
    try {
      toast.loading('Traduzindo títulos de episódios...', { id: 'translate-titles' })

      const payload: any = { forceTranslate, targetLanguage }
      if (animeId.trim()) {
        payload.animeId = animeId.trim()
      }

      const response = await fetch('/api/episodes/translate-titles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (response.ok) {
        setLastTranslation(data.summary)
        toast.success(data.message, { id: 'translate-titles' })
        
        // Limpar campos após sucesso
        setAnimeId('')
        setForceTranslate(false)
      } else {
        console.error('Erro da API:', data)
        throw new Error(data.error || data.message || 'Erro ao traduzir títulos')
      }
    } catch (error) {
      console.error('Erro na tradução:', error)
      
      let errorMessage = 'Erro ao traduzir títulos'
      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }
      
      toast.error(errorMessage, { id: 'translate-titles' })
    } finally {
      setIsTranslating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'translated': return 'text-green-400'
      case 'error': return 'text-red-400'
      case 'skipped': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'translated': return '🌐'
      case 'error': return '❌'
      case 'skipped': return '⏭️'
      default: return '❓'
    }
  }

  const getLanguageFlag = (language: string) => {
    switch (language) {
      case 'en': return '🇺🇸'
      case 'ja': return '🇯🇵'
      case 'pt': return '🇧🇷'
      default: return '🌍'
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-bold mb-4 flex items-center">
          <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
          </svg>
          Tradução de Títulos de Episódios
        </h2>

        <div className="space-y-4">
          {/* Configurações */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                ID do Anime (opcional)
              </label>
              <input
                type="text"
                placeholder="Deixe vazio para todos os animes"
                value={animeId}
                onChange={(e) => setAnimeId(e.target.value)}
                disabled={isTranslating}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Idioma de Destino
              </label>
              <select
                value={targetLanguage}
                onChange={(e) => setTargetLanguage(e.target.value)}
                disabled={isTranslating}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <option value="pt">🇧🇷 Português</option>
                <option value="en">🇺🇸 Inglês</option>
                <option value="es">🇪🇸 Espanhol</option>
              </select>
            </div>
            <div className="flex items-end">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={forceTranslate}
                  onChange={(e) => setForceTranslate(e.target.checked)}
                  disabled={isTranslating}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                />
                <span className="text-sm text-gray-300">
                  Forçar tradução (incluir já traduzidos)
                </span>
              </label>
            </div>
          </div>

          {/* Botão de tradução */}
          <button
            onClick={handleTranslateTitles}
            disabled={isTranslating}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
          >
            {isTranslating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Traduzindo títulos...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
                Traduzir Títulos
              </>
            )}
          </button>

          {/* Informações sobre o sistema */}
          <div className="bg-purple-900 bg-opacity-30 border border-purple-600 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <svg className="w-5 h-5 text-purple-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-purple-300 mb-1">Sistema de Tradução Inteligente</h3>
                <p className="text-sm text-purple-200 opacity-90">
                  O sistema detecta automaticamente o idioma dos títulos e traduz para o idioma selecionado.
                  <strong> Apenas animes "Em lançamento" ou "Em Andamento"</strong> são processados.
                  Traduções são armazenadas em cache para melhor performance.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Resultado da última tradução */}
        {lastTranslation && (
          <div className="mt-6 border-t border-gray-700 pt-6">
            <h3 className="text-lg font-semibold mb-4">Última Tradução</h3>
            
            {/* Resumo */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-blue-400">{lastTranslation.totalProcessed}</div>
                <div className="text-sm text-gray-300">Processados</div>
              </div>
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-green-400">{lastTranslation.translated}</div>
                <div className="text-sm text-gray-300">Traduzidos</div>
              </div>
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-red-400">{lastTranslation.errors}</div>
                <div className="text-sm text-gray-300">Erros</div>
              </div>
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-gray-400">{lastTranslation.skipped}</div>
                <div className="text-sm text-gray-300">Ignorados</div>
              </div>
            </div>

            {/* Log detalhado */}
            {lastTranslation.translationLog.length > 0 && (
              <div>
                <h4 className="text-md font-medium mb-2">Log de Traduções (últimas {lastTranslation.translationLog.length})</h4>
                <div className="bg-gray-900 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <div className="space-y-2 text-sm font-mono">
                    {lastTranslation.translationLog.map((log, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <span>{getStatusIcon(log.status)}</span>
                        <span className="text-gray-300">{log.animeTitle}</span>
                        <span className="text-gray-500">Ep {log.episodeNumber}:</span>
                        <span className={getStatusColor(log.status)}>
                          {log.status === 'translated' 
                            ? `"${log.originalTitle}" → "${log.translatedTitle}"`
                            : log.originalTitle
                          }
                        </span>
                        <span className="text-xs">
                          {getLanguageFlag(log.sourceLanguage)} 
                          {log.confidence > 0 && ` (${Math.round(log.confidence * 100)}%)`}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="text-xs text-gray-500 mt-2">
              Última tradução: {new Date(lastTranslation.timestamp).toLocaleString('pt-BR')}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
