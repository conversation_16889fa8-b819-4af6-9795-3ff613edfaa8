import { NextResponse, headers } from 'next/server'
import { prisma } from '@/lib/prisma'
import crypto from 'crypto'
import nodemailer from 'nodemailer'
import { sendEmail } from '@/lib/brevo'
import { sendEmailSmtp } from '@/lib/brevo-smtp'

// Configuração do transporte de e-mail
// Em produção, use um serviço de e-mail real como SendGrid, Mailgun, etc.
const getTransporter = async () => {
  // Verificar se estamos em ambiente de produção
  if (process.env.NODE_ENV === 'production') {
    // Em produção, usar variáveis de ambiente para configurar o transporte
    return nodemailer.createTransport({
      host: process.env.EMAIL_SERVER_HOST,
      port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
      secure: process.env.EMAIL_SERVER_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_SERVER_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD,
      },
    })
  } else {
    // Em desenvolvimento, criar uma conta de teste no Ethereal
    // Isso cria uma conta temporária para testes
    const testAccount = await nodemailer.createTestAccount();

    console.log('Credenciais Ethereal para testes:');
    console.log('- Email:', testAccount.user);
    console.log('- Senha:', testAccount.pass);
    console.log('- Servidor SMTP:', testAccount.smtp.host);
    console.log('- Porta:', testAccount.smtp.port);

    return nodemailer.createTransport({
      host: testAccount.smtp.host,
      port: testAccount.smtp.port,
      secure: testAccount.smtp.secure,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    });
  }
}

export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o usuário existe
    const user = await prisma.user.findUnique({
      where: { email },
    })

    // Mesmo que o usuário não exista, retornamos sucesso para evitar
    // que alguém possa usar esta rota para descobrir quais e-mails estão cadastrados
    if (!user) {
      return NextResponse.json(
        { success: true, message: 'Se o e-mail estiver cadastrado, você receberá instruções para redefinir sua senha.' },
        { status: 200 }
      )
    }

    // Gerar token de redefinição de senha
    const resetToken = crypto.randomBytes(32).toString('hex')
    const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hora

    // Salvar token no banco de dados
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetToken,
        resetTokenExpiry,
      },
    })

    // URL de redefinição de senha
    let baseUrl = process.env.NEXT_PUBLIC_APP_URL;

    // Em produção, se NEXT_PUBLIC_APP_URL não estiver definido, tentar usar a URL da solicitação
    if (!baseUrl && process.env.NODE_ENV === 'production') {
      // Tentar obter a URL da solicitação
      const host = headers().get('host');
      const protocol = headers().get('x-forwarded-proto') || 'https';

      if (host) {
        baseUrl = `${protocol}://${host}`;
        console.log('URL base detectada a partir da solicitação:', baseUrl);
      } else {
        // Fallback para a URL do Vercel
        baseUrl = 'https://animes-zera.vercel.app';
        console.log('Usando URL de fallback:', baseUrl);
      }
    } else if (!baseUrl) {
      // Em desenvolvimento, usar localhost
      baseUrl = 'http://localhost:3000';
      console.log('Usando URL de desenvolvimento:', baseUrl);
    } else {
      console.log('Usando URL configurada:', baseUrl);
    }

    const resetUrl = `${baseUrl}/reset-password?token=${resetToken}`

    // Preparar o e-mail
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: user.email,
      subject: 'Redefinição de Senha - AnimesZera',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #6200ea;">Redefinição de Senha</h2>
          <p>Olá ${user.name || 'usuário'},</p>
          <p>Recebemos uma solicitação para redefinir sua senha. Se você não solicitou esta alteração, ignore este e-mail.</p>
          <p>Para redefinir sua senha, clique no botão abaixo:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #6200ea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
              Redefinir Senha
            </a>
          </div>
          <p>Ou copie e cole o seguinte link no seu navegador:</p>
          <p style="word-break: break-all;">${resetUrl}</p>
          <p>Este link expirará em 1 hora.</p>
          <p>Atenciosamente,<br>Equipe AnimesZera</p>
        </div>
      `,
    }

    try {
      console.log('Iniciando processo de envio de email de recuperação de senha');
      console.log('Ambiente:', process.env.NODE_ENV);
      console.log('BREVO_API_KEY configurada:', !!process.env.BREVO_API_KEY);

      if (process.env.NODE_ENV === 'production') {
        // Em produção, tentar usar a API REST do Brevo primeiro
        if (process.env.BREVO_API_KEY) {
          try {
            console.log('Tentando usar API REST do Brevo para envio de email');

            await sendEmail({
              to: [{ email: user.email, name: user.name || user.email }],
              subject: 'Redefinição de Senha - AnimesZera',
              htmlContent: mailOptions.html,
            });

            console.log('E-mail enviado via API REST do Brevo para:', user.email);
            return; // Se o envio foi bem-sucedido, sair da função
          } catch (apiError) {
            console.error('Falha ao enviar via API REST do Brevo, tentando SMTP como fallback:', apiError.message);
          }
        }

        // Fallback para SMTP do Brevo
        try {
          console.log('Tentando usar SMTP do Brevo para envio de email');

          await sendEmailSmtp({
            to: user.email,
            subject: 'Redefinição de Senha - AnimesZera',
            html: mailOptions.html,
          });

          console.log('E-mail enviado via SMTP do Brevo para:', user.email);
        } catch (smtpError) {
          console.error('Falha ao enviar via SMTP do Brevo:', smtpError.message);
          throw smtpError; // Re-throw para ser capturado pelo catch externo
        }
      } else {
        // Em desenvolvimento, usar Ethereal
        console.log('Usando Ethereal para envio de email (ambiente de desenvolvimento)');

        const transporter = await getTransporter();
        const info = await transporter.sendMail(mailOptions);

        console.log('E-mail enviado para:', user.email);
        console.log('URL de redefinição:', resetUrl);
        console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
      }
    } catch (error) {
      console.error('Erro ao enviar e-mail:', error);
      // Não retornar erro para o cliente para não revelar se o e-mail existe ou não
      // Apenas registrar o erro no servidor
    }

    return NextResponse.json(
      { success: true, message: 'Se o e-mail estiver cadastrado, você receberá instruções para redefinir sua senha.' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Erro ao processar solicitação de redefinição de senha:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
