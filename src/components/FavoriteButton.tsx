'use client'

import { useState } from 'react'
import { HeartIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'
import { useSession } from 'next-auth/react'

interface FavoriteButtonProps {
  animeId: string
  isFavorite: boolean
  onToggle: () => void
  isLoading?: boolean
}

export default function FavoriteButton({ animeId, isFavorite, onToggle, isLoading: externalLoading }: FavoriteButtonProps) {
  const { data: session } = useSession()
  const [internalLoading, setInternalLoading] = useState(false)
  const isLoading = externalLoading || internalLoading

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault() // Prevent navigation when clicking the button
    if (!session) {
      // Redirect to login if not authenticated
      window.location.href = '/login'
      return
    }

    setInternalLoading(true)
    try {
      const response = await fetch('/api/favorites', {
        method: isFavorite ? 'DELETE' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ animeId }),
      })

      if (!response.ok) {
        throw new Error('Failed to update favorite status')
      }

      onToggle()
    } catch (error) {
      console.error('Error updating favorite status:', error)
    } finally {
      setInternalLoading(false)
    }
  }

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={`p-2 rounded-full transition-all duration-300 ${
        isFavorite 
          ? 'bg-purple-600 hover:bg-purple-700' 
          : 'bg-black/50 hover:bg-black/70'
      } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
      aria-label={isFavorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
    >
      {isFavorite ? (
        <HeartSolidIcon className="h-5 w-5 text-white" />
      ) : (
        <HeartIcon className="h-5 w-5 text-white" />
      )}
    </button>
  )
} 