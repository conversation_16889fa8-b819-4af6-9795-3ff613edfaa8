'use client'

import { useState, useEffect } from 'react'
import { useParams, useSearchParams, useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { toast } from 'react-hot-toast'
import FavoriteButton from '@/components/FavoriteButton'
import VoteButtons from '@/components/VoteButtons'
import Comments from '@/components/Comments'
import WatchedEpisodeButton from '@/components/WatchedEpisodeButton'
import { useSession } from 'next-auth/react'
import SimpleVideoPlayer from '@/components/SimpleVideoPlayer'
import { useWatchedEpisodes } from '@/hooks/useWatchedEpisodes'
import AdBanner from '@/components/AdBanner'
import ReportarBugButton from '@/components/ReportarBugButton'
import NotificationSubscribeButton from '@/components/NotificationSubscribeButton'
import SimpleNotificationTest from '@/components/SimpleNotificationTest'

// Component to play videos directly using the SimpleVideoPlayer
function DirectVideoPlayer({ episode, poster, onNextEpisode, animeSlug }: {
  episode: { id: string, number: number, title: string, videoUrl: string, sourceType?: string },
  poster?: string,
  onNextEpisode?: () => void,
  animeSlug?: string
}) {
  // Determina se o vídeo é do Blogger ou um arquivo direto
  const isBloggerVideo = episode.videoUrl && (
    episode.videoUrl.includes('blogger.com') ||
    episode.videoUrl.includes('blogspot.com') ||
    episode.sourceType === 'blogger'
  )

  // Determina se é uma URL direta de MP4
  const isDirectMp4 = episode.videoUrl && (
    episode.videoUrl.endsWith('.mp4') ||
    episode.videoUrl.includes('lightspeedst.net') ||
    episode.videoUrl.includes('/mp4/') ||
    episode.videoUrl.includes('/mp4_temp/') ||
    episode.sourceType === 'direct_mp4'
  )

  // Determina se o vídeo é do lightspeedst.net (que tem problemas de CORS)
  const isLightspeedVideo = episode.videoUrl && episode.videoUrl.includes('lightspeedst.net')

  // Para vídeos que não são do Blogger nem MP4 direto, usamos a API de proxy
  let videoUrl = episode.videoUrl || ''

  // Se for um vídeo do lightspeedst.net ou se não for uma URL direta de MP4 nem do Blogger,
  // usamos a API de proxy para evitar problemas de CORS
  if ((isLightspeedVideo || (!isBloggerVideo && !isDirectMp4)) && episode.videoUrl) {
    videoUrl = `/api/video/${episode.id}`
  }

  // Se não houver URL de vídeo, exibir mensagem
  if (!episode.videoUrl) {
    return (
      <div className="w-full h-full bg-gray-800 flex items-center justify-center">
        <div className="text-center p-6">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <p className="text-white text-lg font-medium mb-2">Vídeo não disponível</p>
          <p className="text-gray-400 text-sm">Este episódio ainda não possui uma fonte de vídeo.</p>
        </div>
      </div>
    )
  }

  return (
    <SimpleVideoPlayer
      videoUrl={videoUrl}
      poster={poster}
      title={`Episódio ${episode.number}`}
      onNextEpisode={onNextEpisode}
      episodeId={episode.id}
      animeSlug={animeSlug}
    />
  )
}

interface Anime {
  id: string
  title: string
  description: string
  image: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
  audio?: string
  releaseDay?: string
  episodes: {
    id: string
    number: number
    title: string
    airDate: string
    videoUrl: string
    frame?: string
  }[]
}

export default function AnimeDetailsPage() {
  const params = useParams<{ slug: string }>()
  const searchParams = useSearchParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [anime, setAnime] = useState<Anime | null>(null)
  const [selectedEpisode, setSelectedEpisode] = useState<Anime['episodes'][0] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showEpisodes, setShowEpisodes] = useState(true)
  const [episodeSearch, setEpisodeSearch] = useState('')
  const [sortOrder, setSortOrder] = useState<'date' | 'number'>('number')
  const [favorites, setFavorites] = useState<string[]>([])
  const [isFavoriteLoading, setIsFavoriteLoading] = useState(false)
  const [showDescription, setShowDescription] = useState(false)

  const slug = params?.slug || ''

  const {
    isEpisodeWatched,
    updateWatchedStatus
  } = useWatchedEpisodes(slug)

  // Função para carregar os favoritos
  const loadFavorites = async () => {
    if (!session?.user?.email) return

    try {
      const response = await fetch('/api/favorites')
      if (response.ok) {
        const data = await response.json()
        const favoriteIds = data.map((fav: any) => fav.animeId)
        setFavorites(favoriteIds)
        // Salva no localStorage
        localStorage.setItem('favorites', JSON.stringify(favoriteIds))
      }
    } catch (error) {
      console.error('Error fetching favorites:', error)
      // Em caso de erro, tenta carregar do localStorage
      const savedFavorites = localStorage.getItem('favorites')
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites))
      }
    }
  }

  // Carrega os favoritos quando a sessão mudar
  useEffect(() => {
    if (session?.user?.email) {
      loadFavorites()
    }
  }, [session])

  // Carrega os favoritos quando o anime mudar
  useEffect(() => {
    if (anime?.id && session?.user?.email) {
      loadFavorites()
    }
  }, [anime?.id])

  // Carrega os favoritos do localStorage ao montar o componente
  useEffect(() => {
    const savedFavorites = localStorage.getItem('favorites')
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites))
    }
  }, [])

  const handleFavoriteToggle = async () => {
    if (!session) {
      router.push('/auth/login')
      return
    }

    if (!anime) {
      return
    }

    if (isFavoriteLoading) return

    try {
      setIsFavoriteLoading(true)
      const isFavorite = favorites.includes(anime.id)
      const method = isFavorite ? 'DELETE' : 'POST'

      // Atualiza o estado local imediatamente
      const newFavorites = isFavorite
        ? favorites.filter(id => id !== anime.id)
        : [...favorites, anime.id]

      setFavorites(newFavorites)
      // Salva no localStorage
      localStorage.setItem('favorites', JSON.stringify(newFavorites))

      const response = await fetch('/api/favorites', {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ animeId: anime.id }),
      })

      if (!response.ok) {
        // Se houver erro, reverte o estado local
        setFavorites(favorites)
        localStorage.setItem('favorites', JSON.stringify(favorites))
        throw new Error('Erro ao atualizar favorito')
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
    } finally {
      setIsFavoriteLoading(false)
    }
  }

  useEffect(() => {
    const fetchAnime = async () => {
      try {
        setIsLoading(true)
        setError(null)
        const response = await fetch(`/api/animes/${slug}`)

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Anime não encontrado')
        }

        const data = await response.json()

        if (!data) {
          throw new Error('Dados do anime não encontrados')
        }

        setAnime(data)

        // Verifica se há um episódio específico na URL
        const episodeNumber = searchParams?.get('episode')
        const autoplay = searchParams?.get('autoplay') === 'true'

        if (episodeNumber && data.episodes) {
          const episode = data.episodes.find((ep: Anime['episodes'][0]) => ep.number === parseInt(episodeNumber))
          if (episode) {
            setSelectedEpisode(episode)
            setShowEpisodes(true)

            // Se o parâmetro autoplay estiver presente, abrir o player automaticamente
            if (autoplay) {
              // Usar setTimeout para garantir que o DOM esteja pronto
              setTimeout(() => {
                const modal = document.getElementById('videoPlayerModal')
                if (modal) {
                  modal.classList.remove('hidden')
                  modal.classList.remove('opacity-0')
                  modal.classList.remove('pointer-events-none')
                  modal.classList.add('opacity-100')
                  modal.classList.add('pointer-events-auto')

                  // Verificar se é mobile para abrir em tela cheia
                  if (window.innerWidth < 768) {
                    setTimeout(() => {
                      const videoElement = modal.querySelector('video')
                      if (videoElement && videoElement.requestFullscreen) {
                        videoElement.requestFullscreen().catch(err => {
                          console.log('Erro ao abrir em tela cheia:', err)
                        })
                      }
                    }, 500)
                  }
                }

                // Marcar o episódio como assistido automaticamente
                if (episode.id) {
                  updateWatchedStatus(episode.id, true)
                }
              }, 500)
            }
          } else if (data.episodes.length > 0) {
            setSelectedEpisode(data.episodes[0])
          }
        } else if (data.episodes && data.episodes.length > 0) {
          setSelectedEpisode(data.episodes[0])
        }
      } catch (error) {
        console.error('Error fetching anime:', error)
        setError(error instanceof Error ? error.message : 'Erro ao carregar anime')
      } finally {
        setIsLoading(false)
      }
    }

    if (slug) {
      fetchAnime()
    }
  }, [slug, searchParams])

  const filteredEpisodes = anime?.episodes.filter(episode =>
    episode.title.toLowerCase().includes(episodeSearch.toLowerCase()) ||
    episode.number.toString().includes(episodeSearch)
  ) || []

  const sortedEpisodes = [...filteredEpisodes].sort((a, b) => {
    if (sortOrder === 'number') {
      return a.number - b.number
    } else {
      return new Date(b.airDate).getTime() - new Date(a.airDate).getTime()
    }
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">{error}</h2>
          <Link href="/animes" className="text-blue-400 hover:text-blue-300">
            Voltar para lista de animes
          </Link>
        </div>
      </div>
    )
  }

  if (!anime) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Anime não encontrado</h2>
          <Link href="/animes" className="text-blue-400 hover:text-blue-300">
            Voltar para lista de animes
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header Section - Novo Layout Centralizado */}
      <div className="bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Capa do Anime (menor) com informações sobrepostas */}
          <div className="flex justify-center mb-4">
            <div className="relative w-48 h-72 rounded-lg overflow-hidden shadow-xl">
              <Image
                src={anime.image}
                alt={anime.title}
                fill
                className="object-cover"
                priority
              />
              {/* Overlay com gradiente para melhorar legibilidade das informações */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>

              {/* Status do anime (em lançamento/concluído) */}
              {anime.status && (
                <div className="absolute top-2 left-2 bg-purple-600/80 text-white text-xs px-2 py-0.5 rounded-sm">
                  {anime.status}
                </div>
              )}

              {/* Áudio (legendado/dublado) */}
              {anime.audio && (
                <div className="absolute top-2 right-2 bg-red-600/80 text-white text-xs px-2 py-0.5 rounded-sm">
                  {anime.audio}
                </div>
              )}

              {/* Dia de lançamento */}
              {anime.releaseDay && (
                <div className="absolute bottom-2 left-0 right-0 bg-black/70 text-white text-xs px-2 py-1 text-center">
                  Lançamento: {anime.releaseDay}
                </div>
              )}
            </div>
          </div>

          {/* Título do Anime (reduzido) */}
          <h1 className="text-xl font-bold text-center mb-3 px-4 line-clamp-2">
            {anime.title}
          </h1>

          {/* Apenas gêneros */}
          <div className="flex flex-wrap justify-center gap-2 mb-4">
            {/* Gêneros */}
            <div className="flex flex-wrap justify-center gap-2 mb-2 w-full">
              {anime.genres.map((genre) => (
                <span
                  key={genre}
                  className="px-3 py-1 bg-purple-600/40 text-purple-200 rounded-full text-xs"
                >
                  {genre}
                </span>
              ))}
            </div>

            {/* Episódios */}
            <div className="bg-blue-600/30 text-blue-300 px-3 py-1 rounded-full text-xs flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
              </svg>
              <span>{anime.totalEpisodes === 0 ? "??" : anime.totalEpisodes} Episódios</span>
            </div>

            {/* Estúdio */}
            <div className="bg-green-600/30 text-green-300 px-3 py-1 rounded-full text-xs flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              <span>{anime.studio}</span>
            </div>

            {/* Ano */}
            <div className="bg-yellow-600/30 text-yellow-300 px-3 py-1 rounded-full text-xs flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span>{anime.year}</span>
            </div>
          </div>

          {/* Botões de ação */}
          <div className="flex justify-center space-x-4 mb-6">
            {/* Botão de favorito */}
            <div className="flex items-center">
              <FavoriteButton
                animeId={anime.id}
                isFavorite={favorites.includes(anime.id)}
                onToggle={handleFavoriteToggle}
                isLoading={isFavoriteLoading}
              />
              {favorites.includes(anime.id) && (
                <span className="ml-2 text-xs text-purple-400">Favorito</span>
              )}
            </div>

            {/* Botões de voto */}
            <VoteButtons slug={slug} />

            {/* Botão de notificações - Sempre mostrar para teste */}
            <NotificationSubscribeButton
              animeId={anime.id}
              animeTitle={anime.title}
              animeSlug={slug}
              isOngoing={true}
            />

            {/* Componente de teste simples */}
            <SimpleNotificationTest
              animeId={anime.id}
              animeTitle={anime.title}
            />

            {/* Botão de comentários */}
            <button
              onClick={() => {
                // Obter a modal de comentários
                const commentsModal = document.getElementById('comments-modal');
                if (commentsModal) {
                  // Mostrar a modal
                  commentsModal.classList.remove('hidden');
                  commentsModal.classList.add('flex');

                  // Adicionar classe para animar a entrada
                  setTimeout(() => {
                    const modalContent = document.getElementById('comments-modal-content');
                    if (modalContent) {
                      modalContent.classList.remove('opacity-0', 'translate-y-4');
                      modalContent.classList.add('opacity-100', 'translate-y-0');
                    }
                  }, 10);

                  // Desabilitar o scroll do body
                  document.body.style.overflow = 'hidden';
                }
              }}
              className="flex items-center text-gray-400 hover:text-purple-400 transition-colors"
            >
              <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span className="text-sm">Comentários</span>
            </button>
          </div>

          {/* Descrição */}
          <div className="bg-gray-800/50 rounded-lg p-4 mb-0">
            <h2 className="text-lg font-semibold mb-2">Sinopse</h2>
            <div className="relative">
              <div
                className={`text-gray-300 text-sm transition-all duration-300 ${showDescription ? '' : 'line-clamp-3'}`}
                onClick={() => setShowDescription(!showDescription)}
              >
                {anime.description}
              </div>
              <button
                onClick={() => setShowDescription(!showDescription)}
                className="text-purple-400 hover:text-purple-300 text-sm mt-2"
              >
                {showDescription ? 'Mostrar menos' : 'Mostrar mais'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="bg-gray-900 px-4 pt-0 pb-4">

        {/* Modal para exibir o player (será controlado via JavaScript) */}
        {selectedEpisode && (
          <div
            id="videoPlayerModal"
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 transition-opacity opacity-0 pointer-events-none hidden"
          >
            <div className="relative w-full max-w-4xl max-h-screen p-2">
              <button
                className="absolute top-2 right-2 z-10 p-2 bg-black/50 text-white rounded-full"
                onClick={() => setSelectedEpisode(null)}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                <DirectVideoPlayer
                  episode={selectedEpisode}
                  poster={selectedEpisode.frame || anime.image}
                  animeSlug={slug}
                  onNextEpisode={() => {
                    const currentIndex = sortedEpisodes.findIndex(ep => ep.id === selectedEpisode.id)
                    if (currentIndex < sortedEpisodes.length - 1) {
                      setSelectedEpisode(sortedEpisodes[currentIndex + 1])

                      // Marcar o próximo episódio como assistido automaticamente
                      const nextEpisode = sortedEpisodes[currentIndex + 1]
                      if (nextEpisode) {
                        setTimeout(() => {
                          updateWatchedStatus(nextEpisode.id, true)
                        }, 500)
                      }
                    }
                  }}
                />
              </div>

              {/* Informações do episódio */}
              <div className="p-2 bg-gray-800 rounded-b-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-sm font-medium">
                      Episódio {selectedEpisode.number} - {selectedEpisode.title}
                    </h3>
                    <p className="text-xs text-gray-400">
                      {new Date(selectedEpisode.airDate).toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                      })}
                    </p>
                  </div>

                  <div className="flex space-x-1">
                    {/* Botão de próximo episódio */}
                    {sortedEpisodes.findIndex(ep => ep.id === selectedEpisode.id) < sortedEpisodes.length - 1 && (
                      <button
                        onClick={() => {
                          const currentIndex = sortedEpisodes.findIndex(ep => ep.id === selectedEpisode.id)
                          if (currentIndex < sortedEpisodes.length - 1) {
                            setSelectedEpisode(sortedEpisodes[currentIndex + 1])

                            // Marcar o próximo episódio como assistido automaticamente
                            const nextEpisode = sortedEpisodes[currentIndex + 1]
                            if (nextEpisode) {
                              setTimeout(() => {
                                updateWatchedStatus(nextEpisode.id, true)
                              }, 500)
                            }
                          }
                        }}
                        className="flex items-center space-x-1 px-2 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-xs transition-colors"
                        title="Próximo episódio"
                      >
                        <span>Próximo</span>
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" />
                        </svg>
                      </button>
                    )}

                    <WatchedEpisodeButton
                      animeSlug={slug}
                      episodeId={selectedEpisode.id}
                      className="bg-gray-700"
                    />
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(`${window.location.origin}/animes/${slug}?episode=${selectedEpisode.number}`)
                        toast.success('Link copiado!')
                      }}
                      className="p-1.5 text-gray-400 hover:text-purple-400 transition-colors bg-gray-700 rounded-full"
                      title="Copiar link"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 012-2H8a2 2 0 00-2 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Episodes List - Modern Design */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-6">
            <div className="p-3 border-b border-gray-700 flex justify-between items-center">
              <div className="flex items-center gap-3">
                <h2 className="text-base font-bold">Lista de Episódios</h2>
                <ReportarBugButton
                  animeTitle={anime.title}
                  animeSlug={slug}
                />
              </div>
              <div className="flex gap-1">
                <button
                  onClick={() => setSortOrder(sortOrder === 'number' ? 'date' : 'number')}
                  className="p-1.5 text-gray-400 hover:text-purple-400 transition-colors rounded-full hover:bg-gray-700"
                  title={sortOrder === 'number' ? 'Ordenar por data' : 'Ordenar por número'}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                  </svg>
                </button>
                <button
                  onClick={() => setShowEpisodes(!showEpisodes)}
                  className="p-1.5 text-gray-400 hover:text-purple-400 transition-colors rounded-full hover:bg-gray-700"
                  title={showEpisodes ? 'Ocultar episódios' : 'Mostrar episódios'}
                >
                  <svg className={`w-4 h-4 transform transition-transform ${showEpisodes ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Filtro de episódios - Versão compacta */}
            <div className="p-3 border-b border-gray-700 bg-gray-750">
              <div className="flex items-center gap-2">
                <div className="relative w-48">
                  <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Buscar ep..."
                    value={episodeSearch}
                    onChange={(e) => setEpisodeSearch(e.target.value)}
                    className="w-full pl-7 pr-7 py-1.5 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500 text-xs transition-all duration-200 focus:border-purple-500"
                  />
                  {episodeSearch && (
                    <button
                      onClick={() => setEpisodeSearch('')}
                      className="absolute inset-y-0 right-0 pr-2 flex items-center text-gray-400 hover:text-white"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>

                <div className="flex gap-1">
                  <button
                    onClick={() => setEpisodeSearch('')}
                    className={`px-2 py-1.5 rounded-md text-xs transition-all duration-200 ${!episodeSearch ? 'bg-purple-600 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-300'}`}
                  >
                    Todos
                  </button>
                  <button
                    onClick={() => setEpisodeSearch('1')}
                    className={`px-2 py-1.5 rounded-md text-xs transition-all duration-200 ${episodeSearch === '1' ? 'bg-purple-600 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-300'}`}
                  >
                    Primeiro
                  </button>
                  <button
                    onClick={() => {
                      if (anime.totalEpisodes > 0) {
                        setEpisodeSearch(anime.totalEpisodes.toString())
                      }
                    }}
                    className={`px-2 py-1.5 rounded-md text-xs transition-all duration-200 ${
                      anime.totalEpisodes > 0 && episodeSearch === anime.totalEpisodes.toString()
                        ? 'bg-purple-600 text-white'
                        : anime.totalEpisodes === 0
                          ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                          : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    }`}
                    disabled={anime.totalEpisodes === 0}
                  >
                    Último
                  </button>
                </div>

                <div className="ml-auto text-xs text-gray-400">
                  {sortedEpisodes.length} episódios
                </div>
              </div>
            </div>

            {/* Lista de episódios */}
            {showEpisodes && (
              <div className="p-4">
                {sortedEpisodes.length > 0 ? (
                  <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    {sortedEpisodes.map(episode => (
                      <button
                        key={episode.id}
                        onClick={() => {
                          setSelectedEpisode(episode)

                          // Abrir o modal do player
                          const modal = document.getElementById('videoPlayerModal');
                          if (modal) {
                            modal.classList.remove('hidden');
                            modal.classList.remove('opacity-0');
                            modal.classList.remove('pointer-events-none');
                            modal.classList.add('opacity-100');
                            modal.classList.add('pointer-events-auto');

                            // Verificar se é mobile para abrir em tela cheia
                            if (window.innerWidth < 768) {
                              setTimeout(() => {
                                const videoElement = modal.querySelector('video');
                                if (videoElement && videoElement.requestFullscreen) {
                                  videoElement.requestFullscreen().catch(err => {
                                    console.log('Erro ao abrir em tela cheia:', err);
                                  });
                                }
                              }, 500);
                            }
                          }

                          // Marcar o episódio como assistido automaticamente quando clicado
                          if (!isEpisodeWatched(episode.id)) {
                            updateWatchedStatus(episode.id, true)
                          }
                        }}
                        className={`group relative rounded-lg overflow-hidden transition-all duration-200 ${
                          selectedEpisode?.id === episode.id
                            ? 'ring-2 ring-purple-500 shadow-lg shadow-purple-500/20'
                            : isEpisodeWatched(episode.id)
                              ? 'ring-1 ring-green-500/50 shadow-md shadow-green-500/10'
                              : 'hover:shadow-md'
                        }`}
                      >
                        <div className="relative aspect-video bg-gray-700">
                          {/* Thumbnail do episódio (se disponível) */}
                          {episode.frame ? (
                            <Image
                              src={episode.frame}
                              alt={`Episódio ${episode.number}`}
                              fill
                              className="object-cover object-center opacity-70 group-hover:opacity-100 transition-opacity"
                              sizes="(max-width: 768px) 100vw, 300px"
                            />
                          ) : (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <svg className="w-12 h-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </div>
                          )}

                          {/* Número do episódio - Versão compacta */}
                          <div className="absolute top-1 left-1 bg-black/70 text-white px-1.5 py-0.5 rounded text-xs font-bold">
                            {episode.number}
                          </div>

                          {/* Botão de marcar como assistido - Versão compacta */}
                          <div className="absolute top-1 right-1 scale-75">
                            <WatchedEpisodeButton
                              animeSlug={slug}
                              episodeId={episode.id}
                              asChild={true} /* Usar como div para evitar aninhamento de botões */
                            />
                          </div>

                          {/* Indicador de disponibilidade - Versão compacta */}
                          <div className="absolute bottom-1 right-1">
                            {episode.videoUrl ? (
                              <span className="bg-green-500/80 text-white text-[10px] px-1.5 py-0.5 rounded-full flex items-center">
                                <svg className="w-2 h-2 mr-0.5" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M8 5v14l11-7z" />
                                </svg>
                                <span className="hidden sm:inline">Disponível</span>
                              </span>
                            ) : (
                              <span className="bg-red-500/80 text-white text-[10px] px-1.5 py-0.5 rounded-full flex items-center">
                                <svg className="w-2 h-2 mr-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span className="hidden sm:inline">Indisponível</span>
                              </span>
                            )}
                          </div>

                          {/* Overlay de play - Versão compacta */}
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/50">
                            <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-purple-600/80 flex items-center justify-center">
                              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z" />
                              </svg>
                            </div>
                          </div>
                        </div>

                        <div className="p-1.5 bg-gray-800 text-left">
                          <h3 className="text-xs font-medium text-white line-clamp-1">{episode.title || `Episódio ${episode.number}`}</h3>
                          <p className="text-[10px] text-gray-400 mt-0.5 flex items-center">
                            <svg className="w-2 h-2 mr-0.5 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {new Date(episode.airDate).toLocaleDateString('pt-BR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric'
                            })}
                          </p>
                        </div>
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-400">
                    <svg className="w-16 h-16 mx-auto mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <p className="text-lg">Nenhum episódio encontrado</p>
                    <button
                      onClick={() => setEpisodeSearch('')}
                      className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      Limpar filtro
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Modal de Comentários */}
          <div
            id="comments-modal"
            className="fixed inset-0 z-50 hidden items-center justify-center bg-black/80 p-4"
            onClick={(e) => {
              // Fechar a modal ao clicar fora do conteúdo
              if (e.target === e.currentTarget) {
                const commentsModal = document.getElementById('comments-modal');
                const modalContent = document.getElementById('comments-modal-content');

                if (modalContent) {
                  // Animar a saída
                  modalContent.classList.remove('opacity-100', 'translate-y-0');
                  modalContent.classList.add('opacity-0', 'translate-y-4');
                }

                // Fechar após a animação
                setTimeout(() => {
                  if (commentsModal) {
                    commentsModal.classList.remove('flex');
                    commentsModal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                  }
                }, 200);
              }
            }}
          >
            <div
              id="comments-modal-content"
              className="bg-gray-800 rounded-lg w-full max-w-3xl max-h-[80vh] overflow-y-auto transition-all duration-300 opacity-0 translate-y-4"
            >
              <div className="sticky top-0 bg-gray-800 p-4 border-b border-gray-700 flex justify-between items-center z-10">
                <h2 className="text-xl font-bold">Comentários</h2>
                <button
                  onClick={() => {
                    const commentsModal = document.getElementById('comments-modal');
                    const modalContent = document.getElementById('comments-modal-content');

                    if (modalContent) {
                      // Animar a saída
                      modalContent.classList.remove('opacity-100', 'translate-y-0');
                      modalContent.classList.add('opacity-0', 'translate-y-4');
                    }

                    // Fechar após a animação
                    setTimeout(() => {
                      if (commentsModal) {
                        commentsModal.classList.remove('flex');
                        commentsModal.classList.add('hidden');
                        document.body.style.overflow = 'auto';
                      }
                    }, 200);
                  }}
                  className="text-gray-400 hover:text-red-400 transition-colors p-1"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="p-4">
                <Comments
                  slug={slug}
                  episodeId={selectedEpisode?.id}
                />
              </div>
            </div>
          </div>

          {/* Banner único */}
          <div className="my-8">
            <AdBanner position="in-content" />
          </div>
        </div>
      </div>
    </div>
  )
}