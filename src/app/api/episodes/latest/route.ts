import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const episodes = await prisma.episode.findMany({
      take: 30, // Aumentado para 30 episódios mais recentes
      orderBy: {
        airDate: 'desc'
      },
      include: {
        anime: {
          select: {
            id: true,
            slug: true,
            title: true,
            image: true,
            audio: true
          }
        }
      }
    })

    return NextResponse.json(episodes)
  } catch (error) {
    console.error('Error fetching latest episodes:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}