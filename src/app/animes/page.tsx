'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import Link from 'next/link'
import {
  FunnelIcon,
  XMarkIcon
} from '@heroicons/react/24/solid'
import AdBanner from '@/components/AdBanner'
import FavoriteButton from '@/components/FavoriteButton'
import SugestaoAnimeButton from '@/components/SugestaoAnimeButton'

interface Anime {
  id: string
  title: string
  description: string
  image: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
  slug: string
  audio?: string
  releaseDay?: string
}

interface FilterState {
  searchQuery: string
  selectedGenre: string | null
  selectedStatus: string | null
  selectedYear: number | null
  selectedStudio: string | null
  selectedAudio: string | null
  minEpisodes: number | null
  maxEpisodes: number | null
  onlyFavorites: boolean
  sortBy: 'title' | 'year' | 'episodes'
  sortOrder: 'asc' | 'desc'
}

// Function to debounce input
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

const AnimesPage: React.FC = () => {
  const { data: session } = useSession()
  const [animes, setAnimes] = useState<any[]>([])
  const [favorites, setFavorites] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)

  // Filter states
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedGenre, setSelectedGenre] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null)
  const [selectedYear, setSelectedYear] = useState<number | null>(null)
  const [selectedStudio, setSelectedStudio] = useState<string | null>(null)
  const [selectedAudio, setSelectedAudio] = useState<string | null>(null)
  const [minEpisodes, setMinEpisodes] = useState<number | null>(null)
  const [maxEpisodes, setMaxEpisodes] = useState<number | null>(null)
  const [onlyFavorites, setOnlyFavorites] = useState(false)
  const [sortBy, setSortBy] = useState<'title' | 'year' | 'episodes'>('title')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // Apply debounce to search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Load saved filters from localStorage on initial load
  useEffect(() => {
    const savedFilters = localStorage.getItem('animeFilters')
    if (savedFilters) {
      try {
        const parsedFilters = JSON.parse(savedFilters) as FilterState
        setSearchQuery(parsedFilters.searchQuery || '')
        setSelectedGenre(parsedFilters.selectedGenre)
        setSelectedStatus(parsedFilters.selectedStatus)
        setSelectedYear(parsedFilters.selectedYear)
        setSelectedStudio(parsedFilters.selectedStudio)
        setSelectedAudio(parsedFilters.selectedAudio)
        setMinEpisodes(parsedFilters.minEpisodes)
        setMaxEpisodes(parsedFilters.maxEpisodes)
        setOnlyFavorites(parsedFilters.onlyFavorites || false)
        setSortBy(parsedFilters.sortBy || 'title')
        setSortOrder(parsedFilters.sortOrder || 'asc')
      } catch (error) {
        console.error('Error parsing saved filters:', error)
        // If there's an error, clear the saved filters
        localStorage.removeItem('animeFilters')
      }
    }
  }, [])

  // Save filters to localStorage whenever they change
  useEffect(() => {
    const filtersToSave: FilterState = {
      searchQuery,
      selectedGenre,
      selectedStatus,
      selectedYear,
      selectedStudio,
      selectedAudio,
      minEpisodes,
      maxEpisodes,
      onlyFavorites,
      sortBy,
      sortOrder
    }
    localStorage.setItem('animeFilters', JSON.stringify(filtersToSave))
  }, [
    searchQuery,
    selectedGenre,
    selectedStatus,
    selectedYear,
    selectedStudio,
    selectedAudio,
    minEpisodes,
    maxEpisodes,
    onlyFavorites,
    sortBy,
    sortOrder
  ])

  useEffect(() => {
    fetchAnimes()
    if (session) {
      fetchFavorites()
    }
  }, [session])

  const fetchAnimes = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/animes')
      if (!response.ok) {
        throw new Error('Failed to fetch animes')
      }
      const data = await response.json()
      setAnimes(data)
    } catch (error) {
      console.error('Error fetching animes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchFavorites = async () => {
    try {
      const response = await fetch('/api/favorites')
      if (!response.ok) {
        throw new Error('Failed to fetch favorites')
      }
      const data = await response.json()
      setFavorites(data.map((anime: any) => anime.id))
    } catch (error) {
      console.error('Error fetching favorites:', error)
    }
  }

  // Function to clear all filters
  const clearFilters = useCallback(() => {
    setSearchQuery('')
    setSelectedGenre(null)
    setSelectedStatus(null)
    setSelectedYear(null)
    setSelectedStudio(null)
    setSelectedAudio(null)
    setMinEpisodes(null)
    setMaxEpisodes(null)
    setOnlyFavorites(false)
    setSortBy('title')
    setSortOrder('asc')
  }, [])

  const handleFavoriteToggle = async (animeId: string) => {
    try {
      const response = await fetch('/api/favorites', {
        method: favorites.includes(animeId) ? 'DELETE' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ animeId }),
      })

      if (!response.ok) {
        throw new Error('Failed to update favorite status')
      }

      setFavorites(prev =>
        prev.includes(animeId)
          ? prev.filter(id => id !== animeId)
          : [...prev, animeId]
      )
    } catch (error) {
      console.error('Error updating favorite status:', error)
      fetchFavorites()
    }
  }

  // Count active filters for UI indicator
  const activeFilterCount = [
    selectedGenre,
    selectedStatus,
    selectedYear,
    selectedStudio,
    selectedAudio,
    minEpisodes,
    maxEpisodes,
    onlyFavorites && session // Only count favorites filter if user is logged in
  ].filter(Boolean).length

  const filteredAnimes = animes.filter(anime => {
    // Basic filters
    const matchesSearch = anime.title.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
    const matchesGenre = !selectedGenre || anime.genres.includes(selectedGenre)
    const matchesStatus = !selectedStatus || anime.status === selectedStatus
    const matchesYear = !selectedYear || anime.year === selectedYear

    // New filters
    const matchesStudio = !selectedStudio || anime.studio === selectedStudio
    const matchesAudio = !selectedAudio || anime.audio === selectedAudio
    const matchesMinEpisodes = !minEpisodes || anime.totalEpisodes >= minEpisodes
    const matchesMaxEpisodes = !maxEpisodes || anime.totalEpisodes <= maxEpisodes
    const matchesFavorites = !onlyFavorites || favorites.includes(anime.id)

    return matchesSearch &&
           matchesGenre &&
           matchesStatus &&
           matchesYear &&
           matchesStudio &&
           matchesAudio &&
           matchesMinEpisodes &&
           matchesMaxEpisodes &&
           matchesFavorites
  })

  const sortedAnimes = [...filteredAnimes].sort((a, b) => {
    if (sortBy === 'title') {
      return sortOrder === 'asc'
        ? a.title.localeCompare(b.title)
        : b.title.localeCompare(a.title)
    } else if (sortBy === 'year') {
      return sortOrder === 'asc'
        ? a.year - b.year
        : b.year - a.year
    } else {
      return sortOrder === 'asc'
        ? a.totalEpisodes - b.totalEpisodes
        : b.totalEpisodes - a.totalEpisodes
    }
  })

  // Extract unique values for filter dropdowns
  const allGenres = Array.from(new Set(animes.flatMap(anime => anime.genres))).sort()
  const allYears = Array.from(new Set(animes.map(anime => anime.year))).sort((a, b) => b - a)
  const allStatuses = Array.from(new Set(animes.map(anime => anime.status))).sort()
  const allStudios = Array.from(new Set(animes.map(anime => anime.studio))).sort()
  const allAudios = Array.from(new Set(animes.map(anime => anime.audio).filter(Boolean))).sort()

  // Find min/max episode counts for range inputs
  const episodeCounts = animes.map(anime => anime.totalEpisodes)
  const maxPossibleEpisodes = episodeCounts.length ? Math.max(...episodeCounts) : 0
  const minPossibleEpisodes = episodeCounts.length ? Math.min(...episodeCounts) : 0

  // Add proper return type and fix the function declaration
  const renderAnimeCard = (anime: Anime) => {
    return (
      <Link
        key={anime.id}
        href={`/animes/${anime.slug}`}
        className="group relative bg-gray-800 rounded-lg overflow-hidden shadow-lg transition-all duration-300 hover:scale-[1.02]"
      >
      <div className="relative aspect-[2/3] w-full">
        <Image
          src={anime.image}
          alt={anime.title}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="absolute top-2 right-2 z-10">
          <FavoriteButton
            animeId={anime.id}
            isFavorite={favorites.includes(anime.id)}
            onToggle={() => handleFavoriteToggle(anime.id)}
          />
        </div>
        {favorites.includes(anime.id) && (
          <div className="absolute top-2 left-2 bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium">
            Favorito
          </div>
        )}
      </div>
      <div className="p-3 md:p-4">
        <h2 className="text-base md:text-lg font-semibold mb-2 line-clamp-2">{anime.title}</h2>
        <div className="flex flex-wrap gap-1 md:gap-2 mb-2">
          {anime.genres.slice(0, 3).map((genre, index) => (
            <span
              key={index}
              className="px-1.5 md:px-2 py-0.5 md:py-1 bg-purple-900 text-purple-200 rounded-full text-[10px] md:text-xs"
            >
              {genre}
            </span>
          ))}
          {anime.genres.length > 3 && (
            <span className="px-1.5 md:px-2 py-0.5 md:py-1 bg-gray-700 text-gray-300 rounded-full text-[10px] md:text-xs">
              +{anime.genres.length - 3}
            </span>
          )}
        </div>
        <div className="flex items-center justify-between text-xs md:text-sm text-gray-400">
          <span>{anime.year}</span>
          <span>{anime.totalEpisodes === 0 ? "??" : anime.totalEpisodes} episódios</span>
        </div>
        {anime.audio && (
          <div className="mt-2 flex items-center">
            <span className={`px-2 py-0.5 rounded-full text-[10px] md:text-xs ${
              anime.audio === 'Dublado'
                ? 'bg-red-900/50 text-red-200'
                : anime.audio === 'Dual Áudio'
                  ? 'bg-yellow-900/50 text-yellow-200'
                  : 'bg-blue-900/50 text-blue-200'
            }`}>
              {anime.audio}
            </span>
          </div>
        )}
      </div>
    </Link>
    )
  }

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-screen bg-gray-900 text-white">Carregando...</div>
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Banner Topo - Removido para melhorar a experiência do usuário */}

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 space-y-4 md:space-y-0">
          <h1 className="text-3xl font-bold">Animes</h1>

          {/* Search and filter toggle */}
          <div className="flex w-full md:w-auto gap-2">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Buscar anime..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              )}
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-1 px-4 py-2 rounded-lg transition-colors ${
                activeFilterCount > 0
                  ? 'bg-purple-600 hover:bg-purple-700'
                  : 'bg-gray-800 hover:bg-gray-700'
              }`}
            >
              <FunnelIcon className="h-5 w-5" />
              <span className="hidden sm:inline">Filtros</span>
              {activeFilterCount > 0 && (
                <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-bold rounded-full bg-white text-purple-800">
                  {activeFilterCount}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Expanded filter section */}
        {showFilters && (
          <>
            <div className="bg-gray-800 rounded-lg p-4 mb-6 animate-fadeIn">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Filtros</h2>
                <div className="flex gap-2">
                  <button
                    onClick={clearFilters}
                    className="px-3 py-1 text-sm bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    Limpar Filtros
                  </button>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="p-1 rounded-full hover:bg-gray-700 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {/* Genre filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Gênero</label>
                  <select
                    value={selectedGenre || ''}
                    onChange={(e) => setSelectedGenre(e.target.value || null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Todos os Gêneros</option>
                    {allGenres.map(genre => (
                      <option key={genre} value={genre}>{genre}</option>
                    ))}
                  </select>
                </div>

                {/* Status filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
                  <select
                    value={selectedStatus || ''}
                    onChange={(e) => setSelectedStatus(e.target.value || null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Todos os Status</option>
                    {allStatuses.map(status => (
                      <option key={status} value={status}>{status}</option>
                    ))}
                  </select>
                </div>

                {/* Year filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Ano</label>
                  <select
                    value={selectedYear || ''}
                    onChange={(e) => setSelectedYear(e.target.value ? parseInt(e.target.value) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Todos os Anos</option>
                    {allYears.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>

                {/* Studio filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Estúdio</label>
                  <select
                    value={selectedStudio || ''}
                    onChange={(e) => setSelectedStudio(e.target.value || null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Todos os Estúdios</option>
                    {allStudios.map(studio => (
                      <option key={studio} value={studio}>{studio}</option>
                    ))}
                  </select>
                </div>

                {/* Audio filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Áudio</label>
                  <select
                    value={selectedAudio || ''}
                    onChange={(e) => setSelectedAudio(e.target.value || null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Todos os Tipos</option>
                    {allAudios.map(audio => (
                      <option key={audio} value={audio}>{audio}</option>
                    ))}
                  </select>
                </div>

                {/* Episode range filters */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Mínimo de Episódios</label>
                  <input
                    type="number"
                    min={minPossibleEpisodes}
                    max={maxPossibleEpisodes}
                    value={minEpisodes || ''}
                    onChange={(e) => setMinEpisodes(e.target.value ? parseInt(e.target.value) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Mínimo"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Máximo de Episódios</label>
                  <input
                    type="number"
                    min={minPossibleEpisodes}
                    max={maxPossibleEpisodes}
                    value={maxEpisodes || ''}
                    onChange={(e) => setMaxEpisodes(e.target.value ? parseInt(e.target.value) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Máximo"
                  />
                </div>

                {/* Only favorites toggle */}
                {session && (
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={onlyFavorites}
                        onChange={(e) => setOnlyFavorites(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                      <span className="ml-3 text-sm font-medium text-gray-300">Apenas Favoritos</span>
                    </label>
                  </div>
                )}

                {/* Sort options */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Ordenar por</label>
                  <select
                    value={`${sortBy}-${sortOrder}`}
                    onChange={(e) => {
                      const [by, order] = e.target.value.split('-')
                      setSortBy(by as 'title' | 'year' | 'episodes')
                      setSortOrder(order as 'asc' | 'desc')
                    }}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="title-asc">Título (A-Z)</option>
                    <option value="title-desc">Título (Z-A)</option>
                    <option value="year-desc">Ano (Mais Recente)</option>
                    <option value="year-asc">Ano (Mais Antigo)</option>
                    <option value="episodes-desc">Episódios (Mais)</option>
                    <option value="episodes-asc">Episódios (Menos)</option>
                  </select>
                </div>
              </div>
            </div>
            {/* Banner In-Content após filtros */}
            <div className="mb-6">
              <AdBanner position="in-content" />
            </div>
          </>
        )}

        {/* Results count */}
        <div className="flex justify-between items-center mb-4">
          <p className="text-gray-400">
            {sortedAnimes.length} {sortedAnimes.length === 1 ? 'anime encontrado' : 'animes encontrados'}
          </p>
        </div>

        {sortedAnimes.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-16 px-4 bg-gray-800 rounded-lg">
            <FunnelIcon className="h-16 w-16 text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold mb-2">Nenhum anime encontrado</h3>
            <p className="text-gray-400 text-center mb-6">Não encontramos nenhum anime com os filtros selecionados.</p>
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
            >
              Limpar Filtros
            </button>
          </div>
        ) : (
          <>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 md:gap-6">
            {sortedAnimes.slice(0, Math.min(10, sortedAnimes.length)).map(anime => renderAnimeCard(anime))}
          </div>

        {/* In-Content Banner Ad */}
        {/* {sortedAnimes.length > 10 && (
          <div className="my-8">
            <AdPlacement position="in-content" />
          </div>
        )} */}

        {/* Second part of animes list */}
        {sortedAnimes.length > 10 && (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 md:gap-6">
            {sortedAnimes.slice(10).map(anime => renderAnimeCard(anime))}
          </div>
        )}
        </>
        )}

        {/* Banner Rodapé - Mantido para monetização */}
        <div className="mt-8">
          <AdBanner position="bottom" />
        </div>
      </div>

      {/* Botão de sugestão de anime */}
      <SugestaoAnimeButton />
    </div>
  )
}

export default AnimesPage