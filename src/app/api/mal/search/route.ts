import { NextResponse } from 'next/server'

// Cache simples em memória
const cache = new Map<string, { data: any; timestamp: number }>()
const CACHE_DURATION = 1000 * 60 * 60 // 1 hora em milissegundos

// Limite de requisições
const RATE_LIMIT = 3 // requisições por segundo
const requestTimestamps: number[] = []

// Função para traduzir texto usando Google Translate
async function translateText(text: string): Promise<string> {
  try {
    const response = await fetch(
      `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=pt&dt=t&q=${encodeURIComponent(text)}`
    )
    
    if (!response.ok) {
      console.error('Translation error:', response.statusText)
      return text // Retorna o texto original em caso de erro
    }

    const data = await response.json()
    if (data && data[0] && data[0][0] && data[0][0][0]) {
      return data[0][0][0]
    }
    return text
  } catch (error) {
    console.error('Translation error:', error)
    return text
  }
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')

  if (!query) {
    return NextResponse.json({ error: 'Query is required' }, { status: 400 })
  }

  // Verifica cache
  const cached = cache.get(query)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return NextResponse.json(cached.data)
  }

  // Implementa rate limiting
  const now = Date.now()
  requestTimestamps.push(now)
  
  // Remove timestamps antigos (mais de 1 segundo)
  while (requestTimestamps.length > 0 && now - requestTimestamps[0] > 1000) {
    requestTimestamps.shift()
  }

  // Verifica se excedeu o limite
  if (requestTimestamps.length > RATE_LIMIT) {
    return NextResponse.json(
      { error: 'Rate limit exceeded. Please try again in a moment.' },
      { status: 429 }
    )
  }

  try {
    const url = `https://api.jikan.moe/v4/anime?q=${encodeURIComponent(query)}&limit=10`
    console.log('Fetching from Jikan:', url)

    const response = await fetch(url)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Jikan API Error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      })
      throw new Error(`Failed to fetch from Jikan: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid response format from Jikan API')
    }

    // Transforma os dados do Jikan para o formato que esperamos
    const transformedData = await Promise.all(data.data.map(async (anime: any) => {
      // Traduz a descrição para português
      let translatedSynopsis = anime.synopsis || ''
      try {
        const translateUrl = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=pt&dt=t&q=${encodeURIComponent(anime.synopsis || '')}`
        const translateResponse = await fetch(translateUrl)
        if (translateResponse.ok) {
          const translateData = await translateResponse.json()
          translatedSynopsis = translateData[0][0][0]
        }
      } catch (error) {
        console.error('Error translating synopsis:', error)
      }

      return {
        node: {
          id: anime.mal_id,
          title: anime.title,
          main_picture: {
          medium: anime.images?.jpg?.image_url || '',
          large: anime.images?.jpg?.large_image_url || '',
          },
          synopsis: translatedSynopsis,
          status: anime.status === 'Currently Airing' ? 'currently_airing' : 'finished_airing',
        num_episodes: anime.episodes || 0,
        start_date: anime.aired?.from || '',
        studios: anime.studios?.map((studio: any) => ({ name: studio.name })) || [],
        genres: anime.genres?.map((genre: any) => ({ name: genre.name })) || [],
        }
      }
    }))

    // Salva no cache
    cache.set(query, {
      data: transformedData,
      timestamp: now
    })

    return NextResponse.json(transformedData)
  } catch (error) {
    console.error('Error searching Jikan:', error)
    return NextResponse.json(
      { error: 'Failed to search anime', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 