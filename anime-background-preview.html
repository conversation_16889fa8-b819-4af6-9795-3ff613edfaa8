<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualização de Fundo de Anime</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            height: 100%;
            background-color: #0f1116;
            color: white;
        }
        
        .hero-section {
            position: relative;
            height: 600px;
            display: flex;
            align-items: center;
        }
        
        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://i.imgur.com/placeholder.jpg'); /* Substitua pelo URL da sua imagem */
            background-size: cover;
            background-position: center;
        }
        
        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, rgba(15, 17, 22, 1), rgba(15, 17, 22, 0.8), transparent);
        }
        
        .hero-content {
            position: relative;
            z-index: 10;
            max-width: 600px;
            margin-left: 10%;
        }
        
        .hero-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 24px;
        }
        
        .gradient-text {
            background: linear-gradient(to right, #a855f7, #3b82f6);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .hero-description {
            font-size: 20px;
            color: #d1d5db;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .hero-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 16px 32px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            background: linear-gradient(to right, #a855f7, #3b82f6);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .hero-button:hover {
            transform: scale(1.05);
            background: linear-gradient(to right, #9333ea, #2563eb);
        }
        
        .arrow-icon {
            transition: transform 0.3s ease;
        }
        
        .hero-button:hover .arrow-icon {
            transform: translateX(4px);
        }
        
        /* Instruções */
        .instructions {
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 8px;
            margin: 20px;
            max-width: 800px;
        }
        
        .instructions h2 {
            color: #a855f7;
        }
        
        .instructions pre {
            background-color: #1e1e1e;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .instructions code {
            color: #d1d5db;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="hero-background"></div>
        <div class="hero-overlay"></div>
        <div class="hero-content">
            <h1 class="hero-title">
                Bem-vindo ao <span class="gradient-text">AnimesZera</span>
            </h1>
            <p class="hero-description">
                Assista seus animes favoritos em alta qualidade, com legendas em português e sem anúncios.
            </p>
            <a href="#" class="hero-button">
                Explorar Animes
                <svg class="arrow-icon" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
    
    <div class="instructions">
        <h2>Instruções para Implementação</h2>
        <p>Para implementar a nova imagem de fundo na sua página inicial:</p>
        <ol>
            <li>Gere a imagem usando a descrição fornecida com ferramentas como DALL-E, Midjourney ou Stable Diffusion</li>
            <li>Salve a imagem como <code>anime-hero-bg.jpg</code> na pasta <code>public</code> do seu projeto</li>
            <li>Atualize o código da página inicial para usar a nova imagem</li>
        </ol>
        
        <h3>Código para Atualizar:</h3>
        <pre><code>// Em src/app/page.tsx
// Substitua a linha 51
src="/hero-bg.jpg"
// Por:
src="/anime-hero-bg.jpg"</code></pre>
        
        <p>Você também pode ajustar o gradiente de sobreposição para combinar melhor com as cores da sua imagem:</p>
        <pre><code>// Em src/app/page.tsx
// Substitua a linha 57
&lt;div className="absolute inset-0 bg-gradient-to-r from-gray-900 via-gray-900/80 to-transparent" /&gt;
// Por algo como:
&lt;div className="absolute inset-0 bg-gradient-to-r from-gray-900 via-gray-900/70 to-transparent" /&gt;</code></pre>
    </div>
</body>
</html>
