import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcrypt'

const prisma = new PrismaClient()

async function main() {
  const email = '<EMAIL>'
  const password = '123'
  const name = '<PERSON>'

  // Verifica se o usuário já existe
  const existingUser = await prisma.user.findUnique({
    where: { email },
  })

  if (existingUser) {
    console.log('Usuário já existe, atualizando senha...')
    const hashedPassword = await bcrypt.hash(password, 10)
    await prisma.user.update({
      where: { email },
      data: { password: hashedPassword },
    })
    console.log('Senha atualizada com sucesso!')
  } else {
    console.log('Criando novo usuário...')
    const hashedPassword = await bcrypt.hash(password, 10)
    await prisma.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
      },
    })
    console.log('Usuário criado com sucesso!')
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 