'use client'

import { useEffect } from 'react'
import { monetagAds } from '@/config/ads'

interface MontagAdsProps {
  type: 'vignette' | 'interstitial' | 'pushNotifications'
}

export function MontagAds({ type }: MontagAdsProps) {
  useEffect(() => {
    // Evitar duplicação de scripts
    const scriptId = `monetag-${type}`
    if (document.getElementById(scriptId)) {
      return
    }

    if (type === 'vignette') {
      // Vignette Banner
      const script = document.createElement('script')
      script.id = scriptId
      script.innerHTML = `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('${monetagAds.vignette.domain}',${monetagAds.vignette.zoneId},document.createElement('script'))`
      document.body.appendChild(script)
    }

    if (type === 'interstitial') {
      // Interstitial
      const script = document.createElement('script')
      script.id = scriptId
      script.innerHTML = `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('${monetagAds.interstitial.domain}',${monetagAds.interstitial.zoneId},document.createElement('script'))`
      document.body.appendChild(script)
    }

    if (type === 'pushNotifications') {
      // Push Notifications
      const script = document.createElement('script')
      script.id = scriptId
      script.src = `${monetagAds.pushNotifications.scriptUrl}?z=${monetagAds.pushNotifications.zoneId}`
      script.setAttribute('data-cfasync', 'false')
      script.async = true
      document.body.appendChild(script)
    }

    // Cleanup ao desmontar o componente
    return () => {
      const scriptElement = document.getElementById(scriptId)
      if (scriptElement) {
        scriptElement.remove()
      }
    }
  }, [type])

  // Nenhum dos scripts precisa renderizar algo visível
  return null
}
