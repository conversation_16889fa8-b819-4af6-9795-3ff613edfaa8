'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'

interface Anime {
  id: string
  title: string
  image: string
  description: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
}

export default function GenrePage() {
  const params = useParams()
  const [animes, setAnimes] = useState<Anime[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const genre = decodeURIComponent(params.genre as string)

  useEffect(() => {
    fetchAnimes()
  }, [genre])

  const fetchAnimes = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/animes')
      if (!response.ok) {
        throw new Error('Failed to fetch animes')
      }
      const data = await response.json()
      
      // Filtra animes por gênero
      const filteredAnimes = data.filter((anime: Anime) => 
        anime.genres.includes(genre)
      )
      
      setAnimes(filteredAnimes)
    } catch (error) {
      console.error('Error fetching animes:', error)
      alert('Erro ao carregar animes')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900 text-white">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/generos" className="text-blue-400 hover:text-blue-300 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Voltar para lista de gêneros
          </Link>
        </div>

        <h1 className="text-3xl font-bold mb-8">Animes de {genre}</h1>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {animes.map((anime) => (
            <Link
              key={anime.id}
              href={`/animes/${anime.id}`}
              className="group"
            >
              <div className="relative aspect-[2/3] rounded-lg overflow-hidden mb-2">
                <Image
                  src={anime.image}
                  alt={anime.title}
                  fill
                  className="object-cover transition-transform group-hover:scale-110"
                />
              </div>
              <div className="p-2">
                <h2 className="text-sm font-medium text-white mb-1 line-clamp-2">
                  {anime.title}
                </h2>
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>{anime.year}</span>
                  <span>{anime.totalEpisodes} eps</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
} 