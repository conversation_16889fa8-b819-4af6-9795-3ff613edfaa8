import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

export function useAuth() {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [user, setUser] = useState<{ id: string; email: string; role: string } | null>(null)

  useEffect(() => {
    verifyAuth()
    // Verificar autenticação a cada 5 minutos
    const interval = setInterval(verifyAuth, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  const verifyAuth = async () => {
    try {
      const response = await fetch('/api/auth/verify')
      if (response.ok) {
        const data = await response.json()
        setIsAuthenticated(true)
        setUser(data.user)
      } else {
        await logout()
      }
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error)
      await logout()
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      if (response.ok) {
        const data = await response.json()
        setIsAuthenticated(true)
        setUser(data.user)
        return true
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Erro ao fazer login')
      }
    } catch (error) {
      console.error('Erro ao fazer login:', error)
      return false
    }
  }

  const logout = async () => {
    try {
      // Chama a API de logout
      await fetch('/api/auth/logout', {
        method: 'POST',
      })
      
      // Limpa o cookie do token
      document.cookie = 'admin_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict'
      
      // Limpa o estado local
      setIsAuthenticated(false)
      setUser(null)
      
      // Redireciona para a página de login
      router.push('/auth/login')
    } catch (error) {
      console.error('Erro ao fazer logout:', error)
    }
  }

  return {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    verifyAuth,
  }
} 