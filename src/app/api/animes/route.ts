import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

export async function GET() {
  try {
    console.log('Fetching animes from database...')
    const animes = await prisma.anime.findMany({
      include: {
        episodes: {
          orderBy: {
            number: 'asc'
          }
        }
      },
      orderBy: {
        title: 'asc'
      }
    })
    console.log('Found animes:', animes)
    return NextResponse.json(animes)
  } catch (error) {
    console.error('Error fetching animes:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    console.log('Received POST request to create anime')
    const body = await request.json()
    console.log('Request body:', body)

    // Validar os campos obrigatórios
    if (!body.title || !body.description || !body.image || !body.status || 
        !body.totalEpisodes || !body.studio || !body.year || !body.genres) {
      console.error('Missing required fields:', body)
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    console.log('Creating anime with data:', {
      title: body.title,
      description: body.description,
      image: body.image,
      status: body.status,
      totalEpisodes: body.totalEpisodes,
      studio: body.studio,
      year: body.year,
      genres: body.genres
    })

    const slug = generateSlug(body.title)
    console.log('Generated slug:', slug)

    const anime = await prisma.anime.create({
      data: {
        ...body,
        slug,
      },
    })

    console.log('Successfully created anime:', anime)
    return NextResponse.json(anime)
  } catch (error) {
    console.error('Error creating anime:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const anime = await prisma.anime.update({
      where: { id },
      data: {
        title: body.title as string,
        description: body.description as string,
        image: body.image as string,
        status: body.status as string,
        totalEpisodes: parseInt(body.totalEpisodes as string),
        studio: body.studio as string,
        year: parseInt(body.year as string),
        genres: Array.isArray(body.genres) ? body.genres : [body.genres],
      },
    })

    return NextResponse.json(anime)
  } catch (error) {
    console.error('Error updating anime:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')

  if (!id) {
    return NextResponse.json(
      { error: 'ID is required' },
      { status: 400 }
    )
  }

  try {
    // Primeiro, excluir todos os episódios do anime
    await prisma.episode.deleteMany({
      where: {
        animeId: id
      }
    })

    // Depois, excluir o anime
    await prisma.anime.delete({
      where: { id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting anime:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
} 