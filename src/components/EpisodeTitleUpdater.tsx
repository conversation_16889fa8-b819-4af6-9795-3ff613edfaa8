'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'

interface UpdateSummary {
  totalChecked: number
  updated: number
  errors: number
  skipped: number
  timestamp: string
  updateLog: Array<{
    animeTitle: string
    episodeNumber: number
    oldTitle: string
    newTitle: string
    status: 'updated' | 'error' | 'skipped'
  }>
}

export default function EpisodeTitleUpdater() {
  const [isUpdating, setIsUpdating] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<UpdateSummary | null>(null)
  const [animeId, setAnimeId] = useState('')
  const [forceUpdate, setForceUpdate] = useState(false)

  const handleUpdateTitles = async () => {
    if (isUpdating) return

    setIsUpdating(true)
    
    try {
      toast.loading('Atualizando títulos de episódios...', { id: 'update-titles' })

      const payload: any = { forceUpdate }
      if (animeId.trim()) {
        payload.animeId = animeId.trim()
      }

      const response = await fetch('/api/episodes/update-titles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (response.ok) {
        setLastUpdate(data.summary)
        toast.success(data.message, { id: 'update-titles' })
        
        // Limpar campos após sucesso
        setAnimeId('')
        setForceUpdate(false)
      } else {
        throw new Error(data.error || 'Erro ao atualizar títulos')
      }
    } catch (error) {
      console.error('Erro na atualização:', error)
      toast.error(
        error instanceof Error ? error.message : 'Erro ao atualizar títulos',
        { id: 'update-titles' }
      )
    } finally {
      setIsUpdating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'updated': return 'text-green-400'
      case 'error': return 'text-red-400'
      case 'skipped': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'updated': return '✅'
      case 'error': return '❌'
      case 'skipped': return '⏭️'
      default: return '❓'
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-bold mb-4 flex items-center">
          <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Atualização de Títulos de Episódios
        </h2>

        <div className="space-y-4">
          {/* Configurações */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                ID do Anime (opcional)
              </label>
              <input
                type="text"
                placeholder="Deixe vazio para todos os animes"
                value={animeId}
                onChange={(e) => setAnimeId(e.target.value)}
                disabled={isUpdating}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              />
            </div>
            <div className="flex items-end">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={forceUpdate}
                  onChange={(e) => setForceUpdate(e.target.checked)}
                  disabled={isUpdating}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                />
                <span className="text-sm text-gray-300">
                  Forçar atualização (incluir episódios com títulos)
                </span>
              </label>
            </div>
          </div>

          {/* Botão de atualização */}
          <button
            onClick={handleUpdateTitles}
            disabled={isUpdating}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
          >
            {isUpdating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Atualizando títulos...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Atualizar Títulos
              </>
            )}
          </button>

          {/* Informações sobre o cron job */}
          <div className="bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <svg className="w-5 h-5 text-blue-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-blue-300 mb-1">Atualização Automática</h3>
                <p className="text-sm text-blue-200 opacity-90">
                  Os títulos são atualizados automaticamente todos os dias às 00:00 (horário de Brasília) 
                  através de um GitHub Action. Esta ferramenta permite atualizações manuais quando necessário.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Resultado da última atualização */}
        {lastUpdate && (
          <div className="mt-6 border-t border-gray-700 pt-6">
            <h3 className="text-lg font-semibold mb-4">Última Atualização</h3>
            
            {/* Resumo */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-blue-400">{lastUpdate.totalChecked}</div>
                <div className="text-sm text-gray-300">Verificados</div>
              </div>
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-green-400">{lastUpdate.updated}</div>
                <div className="text-sm text-gray-300">Atualizados</div>
              </div>
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-red-400">{lastUpdate.errors}</div>
                <div className="text-sm text-gray-300">Erros</div>
              </div>
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-gray-400">{lastUpdate.skipped}</div>
                <div className="text-sm text-gray-300">Ignorados</div>
              </div>
            </div>

            {/* Log detalhado */}
            {lastUpdate.updateLog.length > 0 && (
              <div>
                <h4 className="text-md font-medium mb-2">Log de Atualizações (últimas {lastUpdate.updateLog.length})</h4>
                <div className="bg-gray-900 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <div className="space-y-2 text-sm font-mono">
                    {lastUpdate.updateLog.map((log, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <span>{getStatusIcon(log.status)}</span>
                        <span className="text-gray-300">{log.animeTitle}</span>
                        <span className="text-gray-500">Ep {log.episodeNumber}:</span>
                        <span className={getStatusColor(log.status)}>
                          {log.status === 'updated' 
                            ? `"${log.oldTitle}" → "${log.newTitle}"`
                            : log.oldTitle
                          }
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="text-xs text-gray-500 mt-2">
              Última atualização: {new Date(lastUpdate.timestamp).toLocaleString('pt-BR')}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
