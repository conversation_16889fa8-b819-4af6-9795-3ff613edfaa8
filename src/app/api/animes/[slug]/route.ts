import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Configuração da rota
export const dynamic = 'force-dynamic'
export const revalidate = 0

// Configuração dos métodos permitidos
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Allow': 'GET, PUT, DELETE',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    // Primeiro tenta encontrar pelo slug exato
    let anime = await prisma.anime.findFirst({
      where: {
        slug: params.slug,
      },
      include: {
        episodes: {
          orderBy: {
            number: 'asc',
          },
        },
      },
    })

    // Se não encontrar, tenta encontrar pelo título
    if (!anime) {
      anime = await prisma.anime.findFirst({
        where: {
          title: {
            contains: params.slug.replace(/-/g, ' '),
            mode: 'insensitive',
          },
        },
        include: {
          episodes: {
            orderBy: {
              number: 'asc',
            },
          },
        },
      })
    }

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }
    return NextResponse.json(anime)
  } catch (error) {
    console.error('Error fetching anime:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar anime' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const data = await request.json()

    // Gera o slug a partir do título
    const slug = generateSlug(data.title)

    const anime = await prisma.anime.update({
      where: {
        slug: params.slug,
      },
      data: {
        ...data,
        slug,
      },
    })

    return NextResponse.json(anime)
  } catch (error) {
    console.error('Error updating anime:', error)
    return NextResponse.json(
      { error: 'Erro ao atualizar anime' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    // Verifica se o anime existe pelo slug
    const anime = await prisma.anime.findUnique({
      where: {
        slug: params.slug,
      },
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Deleta o anime (os registros relacionados serão deletados automaticamente devido ao onDelete: Cascade)
    await prisma.anime.delete({
      where: {
        slug: params.slug,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error('Erro ao deletar anime:', error)
    return NextResponse.json(
      { error: 'Erro ao excluir anime' },
      { status: 500 }
    )
  }
}