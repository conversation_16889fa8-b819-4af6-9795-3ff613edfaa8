'use client'

import { useRef, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface BasicVideoPlayerProps {
  videoUrl: string
  poster?: string
  title?: string
  className?: string
  onNextEpisode?: () => void
  episodeId?: string
  animeSlug?: string
}

export default function BasicVideoPlayer({
  videoUrl,
  poster,
  title = 'Episódio de Anime',
  className = '',
  onNextEpisode,
  episodeId,
  animeSlug
}: BasicVideoPlayerProps) {
  const { data: session } = useSession()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const [hasMarkedAsWatched, setHasMarkedAsWatched] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState(0)
  const saveInterval = 15000 // Salvar a cada 15 segundos

  // Determina o tipo de vídeo com base na URL
  const isBloggerVideo = videoUrl.includes('blogger.com') || videoUrl.includes('blogspot.com')

  // Configurar o player de vídeo
  useEffect(() => {
    if (!videoRef.current) return

    const videoElement = videoRef.current

    // Eventos do player
    const handleTimeUpdate = () => {
      const currentTime = videoElement.currentTime
      const duration = videoElement.duration

      // Marcar como assistido quando atingir 80% do vídeo
      if (!hasMarkedAsWatched && currentTime > duration * 0.8 && animeSlug && episodeId) {
        setHasMarkedAsWatched(true)
        updateWatchedStatus(episodeId, true)
      }

      // Salvar o progresso a cada intervalo
      const now = Date.now()
      if (now - lastSaveTime >= saveInterval) {
        setLastSaveTime(now)
        saveWatchProgress(currentTime, duration)
      }
    }

    const handleEnded = () => {
      if (onNextEpisode) {
        setTimeout(() => {
          onNextEpisode()
        }, 1000)
      }
    }

    // Carregar o progresso de visualização
    const loadWatchProgress = async () => {
      if (!session?.user || !episodeId) return

      try {
        const response = await fetch(`/api/watch-progress?episodeId=${episodeId}`)

        if (response.ok) {
          const data = await response.json()

          if (data.currentTime) {
            // Só definir o tempo se for maior que 0 e menor que 98% da duração
            if (data.currentTime > 0 && (!data.duration || data.percentage < 98)) {
              videoElement.currentTime = data.currentTime
            }
          }
        }
      } catch (error) {
        console.error('Erro ao carregar progresso:', error)
      }
    }

    // Adicionar event listeners
    videoElement.addEventListener('loadeddata', loadWatchProgress)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('ended', handleEnded)

    // Limpar event listeners quando o componente for desmontado
    return () => {
      videoElement.removeEventListener('loadeddata', loadWatchProgress)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('ended', handleEnded)
    }
  }, [videoRef.current, hasMarkedAsWatched, animeSlug, episodeId, onNextEpisode, lastSaveTime, session])

  // Salvar o progresso de visualização
  const saveWatchProgress = async (currentTime: number, duration: number) => {
    if (!session?.user || !episodeId || !animeSlug) return

    try {
      await fetch('/api/watch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          animeSlug, // Adicionando animeSlug que estava faltando
          currentTime,
          duration,
        }),
      })
    } catch (error) {
      console.error('Erro ao salvar progresso:', error)
    }
  }

  // Atualizar o status de assistido
  const updateWatchedStatus = async (episodeId: string, watched: boolean) => {
    if (!animeSlug || !session?.user) return

    try {
      const response = await fetch(`/api/animes/${animeSlug}/watched-episodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          watched,
        }),
      })

      if (!response.ok) {
        throw new Error('Falha ao atualizar status de assistido')
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error)
    }
  }

  // Renderizar o player para vídeos do Blogger
  const renderBloggerPlayer = () => {
    return (
      <div className="w-full h-full">
        <iframe
          src={videoUrl}
          className="w-full h-full"
          allowFullScreen
          frameBorder="0"
          scrolling="no"
          title={title}
        />
      </div>
    )
  }

  // Renderizar o player para vídeos MP4
  const renderVideoPlayer = () => {
    return (
      <video
        ref={videoRef}
        className="w-full h-full"
        poster={poster}
        controls
        playsInline
        preload="auto"
        src={videoUrl}
      >
        <source src={videoUrl} type="video/mp4" />
        Seu navegador não suporta o elemento de vídeo.
      </video>
    )
  }

  return (
    <div className={`relative aspect-video bg-black ${className}`}>
      {isBloggerVideo ? renderBloggerPlayer() : renderVideoPlayer()}
    </div>
  )
}
