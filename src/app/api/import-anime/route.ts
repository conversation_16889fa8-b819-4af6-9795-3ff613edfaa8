import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { parseDate } from '@/lib/dateUtils'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { generateSitemap } from '@/services/sitemapService'

// Cache para armazenar os títulos dos episódios já buscados
const episodeTitleCache: Record<string, Record<number, string>> = {};

// Controle de rate limiting para a API do Jikan
const jikanRequestTimestamps: number[] = [];
const JIKAN_RATE_LIMIT = 1; // requisição por 3 segundos (muito conservador)
const JIKAN_RATE_WINDOW = 3000; // 3 segundos em milissegundos

// Função para esperar antes de fazer uma requisição à API do Jikan
async function waitForJikanRateLimit(): Promise<void> {
  const now = Date.now();

  // Remover timestamps antigos (mais de 3 segundos)
  while (jikanRequestTimestamps.length > 0 && now - jikanRequestTimestamps[0] > JIKAN_RATE_WINDOW) {
    jikanRequestTimestamps.shift();
  }

  // Se já atingiu o limite, esperar
  if (jikanRequestTimestamps.length >= JIKAN_RATE_LIMIT) {
    const oldestTimestamp = jikanRequestTimestamps[0];
    const timeToWait = JIKAN_RATE_WINDOW - (now - oldestTimestamp);

    if (timeToWait > 0) {
      console.log(`Esperando ${timeToWait}ms para respeitar o rate limit do Jikan...`);
      await new Promise(resolve => setTimeout(resolve, timeToWait + 500)); // Adiciona 500ms extra para garantir
    }
  }

  // Sempre esperar pelo menos 1 segundo entre requisições
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Registrar esta requisição
  jikanRequestTimestamps.push(Date.now());
}

// Função para buscar títulos de episódios do MyAnimeList usando a API interna
async function getEpisodeTitles(animeTitle: string, episodeNumber: number): Promise<string | null> {
  try {
    // Verificar se já temos o título no cache
    const cacheKey = animeTitle.toLowerCase();
    if (episodeTitleCache[cacheKey] && episodeTitleCache[cacheKey][episodeNumber]) {
      console.log(`Título encontrado no cache para ${animeTitle}, episódio ${episodeNumber}: ${episodeTitleCache[cacheKey][episodeNumber]}`);
      return episodeTitleCache[cacheKey][episodeNumber];
    }

    console.log(`Buscando título para ${animeTitle}, episódio ${episodeNumber} no MyAnimeList...`);

    // Usar diretamente a API do Jikan para evitar problemas com URLs relativas
    try {
      // Esperar antes de fazer a primeira requisição
      await waitForJikanRateLimit();

      // Buscar o ID do anime diretamente do Jikan
      const searchUrl = `https://api.jikan.moe/v4/anime?q=${encodeURIComponent(animeTitle)}&limit=5`;
      console.log(`Buscando anime no Jikan: ${searchUrl}`);

      // Adicionar um timeout para a requisição
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos de timeout

      try {
        const searchResponse = await fetch(searchUrl, {
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'AnimeZera/1.0 (https://animes-zera.vercel.app/)'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!searchResponse.ok) {
          throw new Error(`Erro ao buscar anime: ${searchResponse.status} ${searchResponse.statusText}`);
        }
      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw new Error(`Erro na requisição: ${fetchError.message}`);
      }

      const searchData = await searchResponse.json();

      if (!searchData.data || searchData.data.length === 0) {
        console.log(`Nenhum resultado encontrado para ${animeTitle}`);
        return null;
      }

      // Pegar o primeiro resultado
      const animeData = searchData.data[0];
      const animeId = animeData.mal_id;

      console.log(`ID do anime encontrado: ${animeId}, título: ${animeData.title}`);

      // Esperar antes de fazer a segunda requisição
      await waitForJikanRateLimit();

      // Buscar informações dos episódios diretamente do Jikan
      const episodesUrl = `https://api.jikan.moe/v4/anime/${animeId}/episodes`;
      console.log(`Buscando episódios no Jikan: ${episodesUrl}`);

      // Adicionar um timeout para a requisição
      const episodesController = new AbortController();
      const episodesTimeoutId = setTimeout(() => episodesController.abort(), 10000); // 10 segundos de timeout

      try {
        const episodesResponse = await fetch(episodesUrl, {
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'AnimeZera/1.0 (https://animes-zera.vercel.app/)'
          },
          signal: episodesController.signal
        });

        clearTimeout(episodesTimeoutId);

        if (!episodesResponse.ok) {
          throw new Error(`Erro ao buscar episódios: ${episodesResponse.status} ${episodesResponse.statusText}`);
        }
      } catch (fetchError) {
        clearTimeout(episodesTimeoutId);
        throw new Error(`Erro na requisição de episódios: ${fetchError.message}`);
      }

      const episodesData = await episodesResponse.json();

      if (!episodesData.data || episodesData.data.length === 0) {
        console.log(`Nenhum episódio encontrado para ${animeTitle}`);
        return null;
      }

      // Armazenar todos os episódios no cache
      if (!episodeTitleCache[cacheKey]) {
        episodeTitleCache[cacheKey] = {};
      }

      console.log(`Total de episódios encontrados: ${episodesData.data.length}`);

      // Imprimir alguns episódios para debug
      if (episodesData.data.length > 0) {
        console.log(`Exemplo de episódio: ${JSON.stringify(episodesData.data[0])}`);
      }

      // Preencher o cache com todos os títulos de episódios
      for (const ep of episodesData.data) {
        if (ep.episode && ep.title) {
          const epNumber = parseInt(ep.episode);
          episodeTitleCache[cacheKey][epNumber] = ep.title;
          console.log(`Armazenando título para episódio ${epNumber}: ${ep.title}`);
        }
      }

      console.log(`Armazenados ${Object.keys(episodeTitleCache[cacheKey]).length} títulos de episódios no cache para ${animeTitle}`);

      // Buscar o episódio específico
      const episode = episodesData.data.find((ep: any) => parseInt(ep.episode) === episodeNumber);

      if (!episode) {
        console.log(`Episódio ${episodeNumber} não encontrado para ${animeTitle}`);
        return null;
      }

      // Usar o título original em inglês
      console.log(`Usando título original: ${episode.title}`);
      return episode.title;
    } catch (error) {
      console.error('Erro ao buscar diretamente do Jikan:', error);

      // Se falhar, tentar usar a API interna como fallback
      console.log('Tentando usar API interna como fallback...');

      // Determinar a URL base
      // Usar diretamente a URL de produção para evitar problemas com URLs relativas
      const baseUrl = 'https://animes-zera.vercel.app';

      console.log(`Usando URL base para fallback: ${baseUrl}`);

      // Primeiro, buscar o ID do anime usando a API interna
      const searchResponse = await fetch(`${baseUrl}/api/mal/search?q=${encodeURIComponent(animeTitle)}`);

      if (!searchResponse.ok) {
        console.error(`Erro ao buscar anime: ${searchResponse.status} ${searchResponse.statusText}`);
        return null;
      }

      const searchResults = await searchResponse.json();

      if (!searchResults || searchResults.length === 0) {
        console.log(`Nenhum resultado encontrado para ${animeTitle}`);
        return null;
      }

      // Pegar o primeiro resultado
      const animeData = searchResults[0];
      const animeId = animeData.node.id;

      console.log(`ID do anime encontrado: ${animeId}, título: ${animeData.node.title}`);

      // Buscar informações dos episódios usando a API interna
      console.log(`Buscando episódios via API interna: ${baseUrl}/api/mal/anime/${animeId}`);
      const episodesResponse = await fetch(`${baseUrl}/api/mal/anime/${animeId}`);

      if (!episodesResponse.ok) {
        console.error(`Erro ao buscar episódios: ${episodesResponse.status} ${episodesResponse.statusText}`);
        return null;
      }

      const episodesData = await episodesResponse.json();

      if (!episodesData.episodes || episodesData.episodes.length === 0) {
        console.log(`Nenhum episódio encontrado para ${animeTitle}`);
        return null;
      }

      // Armazenar todos os episódios no cache
      if (!episodeTitleCache[cacheKey]) {
        episodeTitleCache[cacheKey] = {};
      }

      for (const ep of episodesData.episodes) {
        episodeTitleCache[cacheKey][ep.number] = ep.title;
      }

      console.log(`Armazenados ${episodesData.episodes.length} títulos de episódios no cache para ${animeTitle}`);

      // Buscar o episódio específico
      const episode = episodesData.episodes.find((ep: any) => ep.number === episodeNumber);

      if (!episode) {
        console.log(`Episódio ${episodeNumber} não encontrado para ${animeTitle}`);
        return null;
      }

      // Usar o título original em inglês
      console.log(`Usando título original: ${episode.title}`);
      return episode.title;
    }
  } catch (error) {
    console.error('Erro ao buscar títulos de episódios:', error);
    return null;
  }
}

interface AnimeFireResponse {
  anime_slug: string
  anime_title: string
  anime_title1: string
  anime_image: string
  anime_info: string
  anime_synopsis: string
  anime_score: string
  anime_votes: string
  youtube_trailer: string | null
  anime_studios: string | null
  anime_audio: string | null
  anime_status: string | null
  anime_release_day: string | null
  anime_year: string | null
  anime_season: string | null
  anime_episode_count: string | null
  episodes: {
    episode: number
    title?: string
    publish_date?: string
    data: {
      url: string
      resolution: string
      status: string
    }[]
  }[]
  metadata: {
    op_start: string | null
    op_end: string | null
  }
  response: {
    status: string
    text: string
  }
}

export async function POST(request: Request) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions)
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    // Obter dados do corpo da requisição
    const body = await request.json()
    const { animeUrl } = body

    if (!animeUrl) {
      return NextResponse.json(
        { error: 'URL do anime é obrigatória' },
        { status: 400 }
      )
    }

    // Chave da API
    const apiKey = '0sYiHJHn/2uoVdlwgqcoTgzL3VqSIVw76XTeEVa7DfA='

    // Sempre usar HTTP para evitar problemas com certificados autoassinados
    const apiUrl = `http://15.229.117.80/api?api_key=${encodeURIComponent(apiKey)}&anime_link=${encodeURIComponent(animeUrl)}`

    console.log('Chamando API externa:', apiUrl)

    // Variável para armazenar os dados da API
    let data: AnimeFireResponse;

    try {
      // Adicionar timeout e opções avançadas para o fetch
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 segundos de timeout

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        signal: controller.signal,
        cache: 'no-store',
        next: { revalidate: 0 }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error('Erro na resposta da API:', response.status, response.statusText)
        return NextResponse.json(
          { error: `Erro ao buscar dados do anime: ${response.status} ${response.statusText}` },
          { status: response.status }
        )
      }

      // Verificar se a resposta é JSON válido
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        console.error('Resposta não é JSON:', contentType)
        return NextResponse.json(
          { error: 'A API externa não retornou JSON válido' },
          { status: 500 }
        )
      }

      data = await response.json()

      // Verificar se a resposta contém os campos necessários
      if (!data || !data.anime_title || !data.anime_synopsis || !data.episodes) {
        console.error('Dados incompletos na resposta:', data)
        return NextResponse.json(
          { error: 'Dados incompletos na resposta da API externa' },
          { status: 400 }
        )
      }

      if (data.response && data.response.status !== '200') {
        return NextResponse.json(
          { error: `Erro na API externa: ${data.response.text}` },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error('Erro ao chamar API externa diretamente:', error)

      // Tentar método alternativo usando um proxy diferente
      try {
        console.log('Tentando método alternativo com proxy Vercel...')

        // Usar um proxy Vercel que não verifica certificados SSL
        const proxyUrl = `https://cors-anywhere.herokuapp.com/${apiUrl}`
        console.log('Usando proxy URL:', proxyUrl)

        const proxyResponse = await fetch(proxyUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Origin': 'https://animes-zera.vercel.app'
          },
          cache: 'no-store'
        });

        if (!proxyResponse.ok) {
          throw new Error(`Proxy retornou erro: ${proxyResponse.status} ${proxyResponse.statusText}`);
        }

        // Verificar se a resposta é JSON válido
        const contentType = proxyResponse.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Proxy não retornou JSON válido');
        }

        data = await proxyResponse.json();

        // Verificar se os dados são válidos
        if (!data || !data.anime_title || !data.anime_synopsis || !data.episodes) {
          throw new Error('Dados incompletos na resposta do proxy');
        }

        console.log('Método alternativo com proxy bem-sucedido!');
      } catch (proxyError) {
        console.error('Erro ao usar proxy:', proxyError);

        // Método alternativo: usar dados mockados para teste
        try {
          console.log('Tentando método com dados mockados para teste...');

          // Extrair informações básicas da URL do anime
          const animeNameMatch = animeUrl.match(/\/animes\/([^\/]+)/);
          if (!animeNameMatch) {
            throw new Error('Não foi possível extrair o nome do anime da URL');
          }

          const animeName = animeNameMatch[1].replace(/-/g, ' ').replace(/todos-os-episodios$/, '').trim();
          const formattedAnimeName = animeName.split(' ').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

          // Criar dados mockados para teste
          data = {
            anime_slug: animeNameMatch[1].replace(/-todos-os-episodios$/, ''),
            anime_title: formattedAnimeName,
            anime_title1: formattedAnimeName,
            anime_image: 'https://animefire.plus/img/animes/default-large.webp',
            anime_info: 'Anime, Ação, Aventura',
            anime_synopsis: `Este é um anime importado manualmente. A importação automática falhou, mas você pode editar os detalhes deste anime posteriormente.`,
            anime_score: '0',
            anime_votes: '0 votos',
            youtube_trailer: null,
            anime_studios: 'Desconhecido',
            anime_audio: 'Legendado',
            anime_status: 'Em Lançamento',
            anime_release_day: 'Sábado',
            anime_year: new Date().getFullYear().toString(),
            anime_season: 'Atual',
            anime_episode_count: '1',
            episodes: [
              {
                episode: 1,
                data: [
                  {
                    url: '',
                    resolution: '720p',
                    status: 'ONLINE'
                  }
                ]
              }
            ],
            metadata: {
              op_start: null,
              op_end: null
            },
            response: {
              status: '200',
              text: 'OK'
            }
          };

          console.log('Método com dados mockados bem-sucedido!');

          // Retornar um aviso para o usuário
          return NextResponse.json({
            success: true,
            warning: true,
            message: 'Não foi possível acessar a API externa. Um anime básico foi criado, mas você precisará adicionar os episódios manualmente.',
            data: data
          });
        } catch (mockError) {
          console.error('Erro ao criar dados mockados:', mockError);
          return NextResponse.json(
            {
              error: 'Não foi possível acessar a API externa após múltiplas tentativas. ' +
                    'Erro direto: ' + (error instanceof Error ? error.message : String(error)) +
                    '. Erro proxy: ' + (proxyError instanceof Error ? proxyError.message : String(proxyError))
            },
            { status: 500 }
          );
        }
      }
    }

    // Extrair gêneros do campo anime_info
    const genres = data.anime_info.split(', ').filter(g => g.length > 0 && !g.match(/^[A-Z]\d+$/))

    // Extrair ano (se disponível)
    const yearMatch = data.anime_info.match(/\b(19|20)\d{2}\b/)
    // Garantir que o ano esteja entre 1950 e o ano atual + 1
    let year = yearMatch ? parseInt(yearMatch[0]) : new Date().getFullYear()
    const currentYear = new Date().getFullYear()
    if (year < 1950 || year > currentYear + 1) {
      year = currentYear
    }

    // Função para gerar slug a partir do título
    function generateSlug(title: string): string {
      return title
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove acentos
        .replace(/[^a-z0-9]+/g, '-')     // Substitui caracteres não alfanuméricos por hífens
        .replace(/(^-|-$)/g, '')         // Remove hífens no início e fim
        .replace(/--+/g, '-')            // Substitui múltiplos hífens por um único
        .substring(0, 100)               // Limita o tamanho do slug
    }

    // Usar o slug da API se disponível, ou gerar um novo
    let slug = data.anime_slug || generateSlug(data.anime_title)

    // Garantir que o slug não está vazio
    if (!slug || slug.trim() === '') {
      slug = 'anime-' + Date.now().toString()
    }

    console.log('Usando slug:', slug)

    try {
      // Verificar se estamos usando dados mockados
      const isMocked = data.anime_synopsis && data.anime_synopsis.includes('importado manualmente');

      // Mapear o status do anime
      const statusMap: Record<string, string> = {
        'Completo': 'Concluído',
        'Em Lançamento': 'Em Lançamento',
        'Lançando': 'Em Lançamento',
        'Finalizado': 'Concluído'
      };

      // Determinar o status do anime
      const animeStatus = data.anime_status
        ? statusMap[data.anime_status] || data.anime_status
        : 'Em Lançamento';

      // Determinar o número total de episódios
      // Se não houver informação, usar 0 (será exibido como "??" na interface)
      const totalEpisodes = isMocked
        ? 0
        : data.anime_episode_count
          ? parseInt(data.anime_episode_count)
          : data.episodes.length;

      // Determinar o ano
      let animeYear = data.anime_year
        ? parseInt(data.anime_year.replace(/\D/g, ''))
        : year;

      // Garantir que o ano esteja entre 1950 e o ano atual + 1
      const currentYear = new Date().getFullYear();
      if (animeYear < 1950 || animeYear > currentYear + 1) {
        animeYear = currentYear;
      }

      // Usar diretamente os campos da API para dia de lançamento e áudio
      // Se não estiverem disponíveis, usar valores padrão

      // Determinar o dia de lançamento
      const releaseDay = data.anime_release_day || (animeStatus === 'Concluído' ? null : 'Domingo');

      // Determinar o tipo de áudio
      const audioType = data.anime_audio || 'Legendado';

      // Criar o anime no banco de dados com todos os campos
      const anime = await prisma.anime.create({
        data: {
          title: data.anime_title,
          description: data.anime_synopsis || 'Sem descrição disponível',
          image: data.anime_image || '',
          status: animeStatus,
          totalEpisodes: totalEpisodes,
          studio: data.anime_studios || 'Desconhecido',
          year: animeYear,
          genres: genres,
          slug: slug,
          audio: audioType,
          releaseDay: releaseDay,
        }
      })

      // Campos adicionais já foram incluídos na criação do anime
      console.log('Anime criado com campos adicionais:', {
        id: anime.id,
        title: anime.title,
        studio: anime.studio,
        audio: audioType,
        status: anime.status,
        releaseDay: releaseDay,
        year: anime.year
      });

    console.log('Anime criado:', anime)

      // Verificar se precisamos criar episódios
      if (isMocked) {
        console.log('Usando dados mockados - não criando episódios');

        return NextResponse.json({
          success: true,
          warning: true,
          message: `Anime "${data.anime_title}" importado com sucesso, mas sem episódios. Você precisará adicionar os episódios manualmente.`,
          anime: {
            ...anime,
            episodeCount: 0
          }
        });
      }

      // Criar os episódios
      let createdEpisodes = 0;

      // Inicializar o objeto global se não existir
      if (!global.animeImportProgress) {
        global.animeImportProgress = {};
        console.log('Inicializando objeto global animeImportProgress');
      }

      // Criar um objeto para armazenar o progresso da importação
      const importProgress = {
        totalSteps: 3, // 1. Pré-carregamento, 2. Processamento de episódios, 3. Finalização
        currentStep: 1,
        stepProgress: 0,
        totalProgress: 0,
        message: 'Iniciando importação...'
      };

      // Função para atualizar o progresso no cache global
      const updateProgress = (step: number, progress: number, message: string) => {
        importProgress.currentStep = step;
        importProgress.stepProgress = progress;
        importProgress.totalProgress = Math.floor(((step - 1) * 100 + progress) / importProgress.totalSteps);
        importProgress.message = message;

        // Armazenar o progresso em um cache global para ser acessado pela rota de progresso
        global.animeImportProgress[slug] = { ...importProgress };

        console.log(`Progresso da importação [${slug}]: ${importProgress.totalProgress}% - ${message}`);
        console.log('Estado atual do objeto global:', Object.keys(global.animeImportProgress));
      };

      // Inicializar o progresso
      updateProgress(1, 0, 'Iniciando importação...');

      try {
        // Pré-carregar todos os títulos de episódios de uma vez
        updateProgress(1, 10, 'Pré-carregando títulos de episódios...');
        console.log(`Pré-carregando títulos de episódios para ${data.anime_title}...`);

        // Definir um timeout mais longo para o pré-carregamento (30 segundos)
        const preloadTimeout = 30000;

        try {
          // Tentar carregar os títulos dos episódios com timeout
          const preloadPromise = getEpisodeTitles(data.anime_title, 1); // Isso vai carregar todos os episódios no cache
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Timeout no pré-carregamento')), preloadTimeout)
          );

          updateProgress(1, 50, 'Buscando títulos de episódios do MyAnimeList...');
          await Promise.race([preloadPromise, timeoutPromise]);
          updateProgress(1, 100, 'Pré-carregamento de títulos concluído com sucesso!');
          console.log('Pré-carregamento de títulos concluído com sucesso!');
        } catch (preloadError) {
          console.error('Erro no pré-carregamento de títulos:', preloadError);
          console.log('Continuando com a importação mesmo sem os títulos pré-carregados...');
          updateProgress(1, 100, 'Continuando sem títulos do MyAnimeList...');
        }

        // Processar episódios em lotes para evitar sobrecarga
        const batchSize = 5;
        const totalEpisodes = data.episodes.length;

        // Atualizar progresso para o passo 2
        updateProgress(2, 0, `Processando ${totalEpisodes} episódios...`);

        for (let i = 0; i < totalEpisodes; i += batchSize) {
          const batch = data.episodes.slice(i, i + batchSize);

          // Calcular o progresso atual
          const currentProgress = Math.floor((i / totalEpisodes) * 100);
          updateProgress(2, currentProgress, `Processando episódios ${i+1} a ${Math.min(i+batchSize, totalEpisodes)} de ${totalEpisodes}...`);

          const episodePromises = batch.map(async (ep) => {
            try {
              // Escolher a melhor qualidade disponível (preferência: 720p)
              const hdVideo = ep.data.find(d => d.resolution === '720p' && d.status === 'ONLINE')
              const sdVideo = ep.data.find(d => d.resolution === '360p' && d.status === 'ONLINE')
              const fhdVideo = ep.data.find(d => d.resolution === '1080p' && d.status === 'ONLINE')

              const videoUrl = hdVideo?.url || sdVideo?.url || fhdVideo?.url || ''

              // Usar a data de publicação do episódio, se disponível
              let airDate = new Date();

              // Verificar se o episódio tem uma data de publicação
              if (ep.publish_date) {
                try {
                  // Usar o utilitário para analisar a data em qualquer formato
                  const parsedDate = parseDate(ep.publish_date);

                  if (parsedDate) {
                    airDate = parsedDate;
                    // Garantir que a data tenha o horário definido para meio-dia
                    airDate.setHours(12, 0, 0, 0);
                    console.log(`Usando data de publicação para episódio ${ep.episode}: ${ep.publish_date} -> ${airDate.toISOString()} (local: ${airDate.toString()})`);
                  } else {
                    throw new Error('Data inválida após processamento');
                  }
                } catch (error) {
                  console.error(`Erro ao processar data de publicação para episódio ${ep.episode}:`, error);

                  // Fallback para o método anterior
                  if (data.anime_release_day) {
                    const dayMap: Record<string, number> = {
                      'Domingo': 0,
                      'Segunda': 1,
                      'Segunda-feira': 1,
                      'Terça': 2,
                      'Terça-feira': 2,
                      'Quarta': 3,
                      'Quarta-feira': 3,
                      'Quinta': 4,
                      'Quinta-feira': 4,
                      'Sexta': 5,
                      'Sexta-feira': 5,
                      'Sábado': 6
                    };

                    const dayNumber = dayMap[data.anime_release_day] || 0;
                    const today = new Date();

                    // Se o anime for antigo, calcular a data baseada no número do episódio
                    if (data.anime_status === 'Completo') {
                      const releaseDate = new Date();
                      // Subtrai uma semana para cada episódio
                      releaseDate.setDate(releaseDate.getDate() - ((data.episodes.length - ep.episode) * 7));
                      airDate = releaseDate;
                    } else {
                      // Se estiver em lançamento, ajusta para o dia da semana correto
                      airDate = new Date();
                      airDate.setDate(today.getDate() - today.getDay() + dayNumber);

                      // Se o dia calculado for no futuro, volta uma semana
                      if (airDate > today) {
                        airDate.setDate(airDate.getDate() - 7);
                      }
                    }
                  }
                }
              } else if (data.anime_release_day) {
                // Fallback para o método anterior se não houver data de publicação
                const dayMap: Record<string, number> = {
                  'Domingo': 0,
                  'Segunda': 1,
                  'Segunda-feira': 1,
                  'Terça': 2,
                  'Terça-feira': 2,
                  'Quarta': 3,
                  'Quarta-feira': 3,
                  'Quinta': 4,
                  'Quinta-feira': 4,
                  'Sexta': 5,
                  'Sexta-feira': 5,
                  'Sábado': 6
                };

                const dayNumber = dayMap[data.anime_release_day] || 0;
                const today = new Date();

                // Se o anime for antigo, calcular a data baseada no número do episódio
                if (data.anime_status === 'Completo') {
                  const releaseDate = new Date();
                  // Subtrai uma semana para cada episódio
                  releaseDate.setDate(releaseDate.getDate() - ((data.episodes.length - ep.episode) * 7));
                  airDate = releaseDate;
                } else {
                  // Se estiver em lançamento, ajusta para o dia da semana correto
                  airDate = new Date();
                  airDate.setDate(today.getDate() - today.getDay() + dayNumber);

                  // Se o dia calculado for no futuro, volta uma semana
                  if (airDate > today) {
                    airDate.setDate(airDate.getDate() - 7);
                  }
                }
              }

              // Definir título padrão do episódio
              let episodeTitle = `Episódio ${ep.episode}`;

              // Verificar se já temos o título no cache
              const cacheKey = data.anime_title.toLowerCase();
              if (episodeTitleCache[cacheKey] && episodeTitleCache[cacheKey][ep.episode]) {
                const cachedTitle = episodeTitleCache[cacheKey][ep.episode];
                episodeTitle = `Episódio ${ep.episode} - ${cachedTitle}`;
                console.log(`Título encontrado no cache para episódio ${ep.episode}: ${cachedTitle}`);
              } else {
                try {
                  // Tentar buscar o título com um timeout para evitar bloqueios
                  const titlePromise = getEpisodeTitles(data.anime_title, ep.episode);

                  // Adicionar um timeout de 5 segundos
                  const timeoutPromise = new Promise<null>((_, reject) =>
                    setTimeout(() => reject(new Error('Timeout ao buscar título')), 5000)
                  );

                  // Usar o que vier primeiro: o título ou o timeout
                  const customTitle = await Promise.race([titlePromise, timeoutPromise]);

                  if (customTitle) {
                    episodeTitle = `Episódio ${ep.episode} - ${customTitle}`;
                    console.log(`Título do episódio ${ep.episode} encontrado no MyAnimeList: ${customTitle}`);
                  }
                  // Se não encontrar no MyAnimeList, usar o título da API AnimeFireAPI como fallback
                  else if (ep.title) {
                    episodeTitle = `Episódio ${ep.episode} - ${ep.title}`;
                    console.log(`Usando título da API AnimeFireAPI para episódio ${ep.episode}: ${ep.title}`);
                  }
                } catch (titleError) {
                  console.error(`Erro ao buscar título para o episódio ${ep.episode}:`, titleError);

                  // Em caso de erro, tentar usar o título da API AnimeFireAPI como fallback
                  if (ep.title) {
                    episodeTitle = `Episódio ${ep.episode} - ${ep.title}`;
                    console.log(`Usando título da API AnimeFireAPI como fallback para episódio ${ep.episode}: ${ep.title}`);
                  }
                }
              }

              const episode = await prisma.episode.create({
                data: {
                  animeId: anime.id,
                  number: ep.episode,
                  title: episodeTitle,
                  airDate: airDate,
                  frame: data.anime_image || '',
                  videoUrl: videoUrl,
                  sourceType: 'direct_mp4'
                }
              });

              createdEpisodes++;
              return episode;
            } catch (error) {
              console.error(`Erro ao criar episódio ${ep.episode}:`, error);
              return null;
            }
          });

          await Promise.all(episodePromises);

          // Pequena pausa entre os lotes para evitar sobrecarga
          if (i + batchSize < totalEpisodes) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        console.log(`${createdEpisodes} episódios criados`);

        // Atualizar progresso para o passo 3 (finalização)
        updateProgress(3, 50, `Finalizando importação...`);

        // Limpar o progresso após a conclusão
        setTimeout(() => {
          if (global.animeImportProgress && global.animeImportProgress[slug]) {
            delete global.animeImportProgress[slug];
          }
        }, 60000); // Manter o progresso por 1 minuto após a conclusão

        // Atualizar progresso para 100%
        updateProgress(3, 100, `Importação concluída com sucesso! ${createdEpisodes} episódios criados.`);

        // Gerar sitemap.xml atualizado
        try {
          console.log('Gerando sitemap.xml atualizado...');
          const sitemapResult = await generateSitemap();
          console.log('Resultado da geração do sitemap:', sitemapResult);
        } catch (sitemapError) {
          console.error('Erro ao gerar sitemap:', sitemapError);
          // Não interromper o fluxo se houver erro na geração do sitemap
        }

        // Garantir que o ID da importação seja retornado
        console.log(`Retornando resposta com ID de importação: ${slug}`);

        return NextResponse.json({
          success: true,
          message: `Anime "${data.anime_title}" importado com sucesso com ${createdEpisodes} episódios`,
          anime: {
            ...anime,
            episodeCount: createdEpisodes
          },
          importId: slug // Retornar o ID da importação para consultar o progresso
        });
      } catch (error) {
        console.error('Erro ao criar episódios:', error);

        // Mesmo se houver erro nos episódios, retornamos sucesso parcial
        // já que o anime foi criado
        return NextResponse.json({
          success: true,
          message: `Anime "${data.anime_title}" importado com ${createdEpisodes} episódios. Alguns episódios podem não ter sido importados.`,
          anime: {
            ...anime,
            episodeCount: createdEpisodes
          },
          warning: 'Alguns episódios podem não ter sido importados devido a erros.'
        });
      }
    } catch (error) {
      console.error('Erro ao criar anime:', error);
      return NextResponse.json(
        { error: 'Erro ao criar anime: ' + (error instanceof Error ? error.message : String(error)) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Erro ao importar anime:', error)
    return NextResponse.json(
      { error: 'Erro ao importar anime: ' + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    )
  }
}
