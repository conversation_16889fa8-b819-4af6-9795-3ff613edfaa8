'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface Episode {
  id: string
  number: number
  title: string
  airDate: string
  frame: string
  videoUrl: string
  anime: {
    id: string
    slug: string
    title: string
    image: string
    audio: string
  }
}

export default function EpisodeCarousel() {
  const [episodes, setEpisodes] = useState<Episode[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [visibleEpisodes, setVisibleEpisodes] = useState(5)

  useEffect(() => {
    const fetchEpisodes = async () => {
      try {
        const response = await fetch('/api/episodes/latest')
        if (!response.ok) throw new Error('Failed to fetch episodes')
        const data = await response.json()
        setEpisodes(data)
      } catch (error) {
        console.error('Error fetching episodes:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchEpisodes()

    // Update visible episodes based on screen size
    const updateVisibleEpisodes = () => {
      if (window.innerWidth >= 1280) setVisibleEpisodes(5)
      else if (window.innerWidth >= 1024) setVisibleEpisodes(4)
      else if (window.innerWidth >= 768) setVisibleEpisodes(3)
      else if (window.innerWidth >= 640) setVisibleEpisodes(2)
      else setVisibleEpisodes(1)
    }

    updateVisibleEpisodes()
    window.addEventListener('resize', updateVisibleEpisodes)
    return () => window.removeEventListener('resize', updateVisibleEpisodes)
  }, [])

  const nextSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex + visibleEpisodes >= episodes.length ? 0 : prevIndex + visibleEpisodes
    )
  }

  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex - visibleEpisodes < 0
        ? Math.max(0, episodes.length - visibleEpisodes)
        : prevIndex - visibleEpisodes
    )
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="relative">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">Últimos Episódios</h2>
        <div className="flex space-x-2">
          <button
            onClick={prevSlide}
            className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            aria-label="Previous episodes"
          >
            <ChevronLeftIcon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          </button>
          <button
            onClick={nextSlide}
            className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            aria-label="Next episodes"
          >
            <ChevronRightIcon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          </button>
        </div>
      </div>

      <div className="relative overflow-hidden">
        <div
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * (100 / visibleEpisodes)}%)` }}
        >
          {episodes.map((episode) => (
            <div
              key={episode.id}
              className="flex-shrink-0 w-full"
              style={{ width: `${100 / visibleEpisodes}%` }}
            >
              <Link
                href={`/animes/${episode.anime.slug}?episode=${episode.number}&autoplay=true`}
                className="block mx-2 group"
              >
                <div className="relative aspect-video rounded-lg overflow-hidden">
                  <Image
                    src={episode.frame || episode.anime.image || '/placeholder.png'}
                    alt={`${episode.anime.title} - Episódio ${episode.number}`}
                    fill
                    className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                    unoptimized
                    sizes="(max-width: 768px) 100vw, 300px"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute top-2 right-2 bg-purple-600 px-2 py-1 rounded-md">
                    <span className="text-sm font-bold text-white">
                      EP {episode.number}
                    </span>
                  </div>
                  <div className="absolute top-2 left-2">
                    <span className={`text-xs font-medium px-2 py-1 rounded-md ${episode.anime.audio === 'Dublado' ? 'bg-blue-600' : 'bg-yellow-600'}`}>
                      {episode.anime.audio || 'Legendado'}
                    </span>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 p-3">
                    <span className="text-sm font-medium text-white drop-shadow-lg">
                      {episode.anime.title}
                    </span>
                  </div>
                </div>
                <div className="mt-2">
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">
                    {episode.title}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {new Date(episode.airDate).toLocaleDateString('pt-BR')}
                  </p>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}