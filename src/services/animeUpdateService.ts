/**
 * Serviço para verificar e atualizar episódios de animes em lançamento
 * Utiliza a API do AnimeFireAPI para buscar informações atualizadas
 */

interface AnimeFireResponse {
  anime_slug: string
  anime_title: string
  anime_title1: string
  anime_image: string
  anime_info: string
  anime_synopsis: string
  anime_score: string
  anime_votes: string
  youtube_trailer: string | null
  episodes: {
    episode: number
    data: {
      url: string
      resolution: string
      status: string
    }[]
  }[]
  metadata: {
    op_start: string | null
    op_end: string | null
  }
  response: {
    status: string
    text: string
  }
}

interface UpdateResult {
  animeId: string
  animeTitle: string
  animeSlug: string
  currentEpisodes: number
  newEpisodes: number
  episodesAdded: number
  success: boolean
  error?: string
}

/**
 * Verifica atualizações para um anime específico
 * @param animeId ID do anime a ser verificado
 * @param animeFireUrl URL do anime no AnimeFirePlus
 * @returns Resultado da atualização
 */
export async function checkAnimeUpdates(animeId: string, animeTitle: string, animeSlug: string, currentEpisodeCount: number, animeFireUrl?: string): Promise<UpdateResult> {
  try {
    // Se não tiver URL do AnimeFirePlus, tenta construir uma
    if (!animeFireUrl) {
      // Tenta construir a URL baseada no slug
      animeFireUrl = `https://animefire.plus/animes/${animeSlug}-todos-os-episodios`;
    }

    // Chave da API
    const apiKey = '0sYiHJHn/2uoVdlwgqcoTgzL3VqSIVw76XTeEVa7DfA=';

    // URL da API
    const apiUrl = `http://15.229.117.80/api?api_key=${encodeURIComponent(apiKey)}&anime_link=${encodeURIComponent(animeFireUrl)}`;

    console.log(`Verificando atualizações para ${animeTitle} (${animeSlug}) na URL: ${animeFireUrl}`);

    // Adicionar timeout e opções avançadas para o fetch
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 segundos de timeout

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
      signal: controller.signal,
      cache: 'no-store',
      next: { revalidate: 0 }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API retornou status ${response.status}: ${response.statusText}`);
    }

    const data: AnimeFireResponse = await response.json();

    if (data.response.status !== '200') {
      throw new Error(`API retornou erro: ${data.response.text}`);
    }

    // Obter os episódios existentes no banco de dados
    const existingEpisodes = await getExistingEpisodes(animeId, animeSlug);
    const existingEpisodeNumbers = existingEpisodes.map(ep => ep.number);

    console.log(`Anime ${animeTitle}: ${existingEpisodes.length} episódios existentes, números: [${existingEpisodeNumbers.join(', ')}]`);

    // Verificar quais episódios estão faltando ou precisam ser adicionados
    const episodesToAdd = [];

    for (const ep of data.episodes) {
      // Se o episódio não existe no banco de dados, adicionar à lista
      if (!existingEpisodeNumbers.includes(ep.episode)) {
        episodesToAdd.push(ep);
      }
    }

    // Ordenar por número do episódio
    episodesToAdd.sort((a, b) => a.episode - b.episode);

    console.log(`Anime ${animeTitle}: ${existingEpisodes.length} episódios existentes, ${data.episodes.length} episódios encontrados, ${episodesToAdd.length} episódios para adicionar`);

    if (episodesToAdd.length === 0) {
      return {
        animeId,
        animeTitle,
        animeSlug,
        currentEpisodes: existingEpisodes.length,
        newEpisodes: 0,
        episodesAdded: 0,
        success: true
      };
    }

    // Adicionar os novos episódios
    const addedEpisodes = await addNewEpisodes(animeId, episodesToAdd, data.anime_image);

    return {
      animeId,
      animeTitle,
      animeSlug,
      currentEpisodes: existingEpisodes.length,
      newEpisodes: episodesToAdd.length,
      episodesAdded: addedEpisodes,
      success: true
    };
  } catch (error) {
    console.error(`Erro ao verificar atualizações para ${animeTitle}:`, error);
    return {
      animeId,
      animeTitle,
      animeSlug,
      currentEpisodes: currentEpisodeCount,
      newEpisodes: 0,
      episodesAdded: 0,
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Adiciona novos episódios ao banco de dados
 * @param animeId ID do anime
 * @param episodes Lista de episódios a adicionar
 * @param animeImage Imagem do anime para usar como frame
 * @returns Número de episódios adicionados com sucesso
 */
async function addNewEpisodes(
  animeId: string,
  episodes: {
    episode: number
    data: {
      url: string
      resolution: string
      status: string
    }[]
  }[],
  animeImage: string
): Promise<number> {
  let addedCount = 0;

  // Processar episódios em lotes para evitar sobrecarga
  const batchSize = 3;

  for (let i = 0; i < episodes.length; i += batchSize) {
    const batch = episodes.slice(i, i + batchSize);

    const episodePromises = batch.map(async (ep) => {
      try {
        // Escolher a melhor qualidade disponível (preferência: 720p)
        const hdVideo = ep.data.find(d => d.resolution === '720p' && d.status === 'ONLINE')
        const sdVideo = ep.data.find(d => d.resolution === '360p' && d.status === 'ONLINE')
        const fhdVideo = ep.data.find(d => d.resolution === '1080p' && d.status === 'ONLINE')

        const videoUrl = hdVideo?.url || sdVideo?.url || fhdVideo?.url || ''

        // Adicionar o episódio usando a API
        // Verificar se estamos no ambiente do navegador ou do servidor
        let apiUrl = `/api/animes/${animeId}/episodes`

        // No ambiente do navegador, usar URL absoluta
        if (typeof window !== 'undefined') {
          apiUrl = `${window.location.origin}${apiUrl}`
        }

        const response = await fetch(apiUrl, {
          method: 'POST',
          credentials: 'include', // Importante: inclui cookies de autenticação
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
          body: JSON.stringify({
            number: ep.episode,
            title: `Episódio ${ep.episode}`,
            airDate: new Date().toISOString(),
            frame: animeImage || '',
            videoUrl: videoUrl,
            sourceType: 'direct_mp4'
          }),
        });

        if (response.ok) {
          addedCount++;
          return true;
        } else {
          console.error(`Erro ao adicionar episódio ${ep.episode}: ${response.status} ${response.statusText}`);
          return false;
        }
      } catch (error) {
        console.error(`Erro ao adicionar episódio ${ep.episode}:`, error);
        return false;
      }
    });

    await Promise.all(episodePromises);

    // Pequena pausa entre os lotes para evitar sobrecarga
    if (i + batchSize < episodes.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return addedCount;
}

/**
 * Obtém os episódios existentes de um anime
 * @param animeId ID do anime
 * @param animeSlug Slug do anime
 * @returns Lista de episódios existentes
 */
async function getExistingEpisodes(animeId: string, animeSlug: string): Promise<{ id: string, number: number }[]> {
  try {
    // Buscar os episódios do anime usando a API
    // Verificar se estamos no ambiente do navegador ou do servidor
    let apiUrl = `/api/animes/${animeSlug}/episodes-list`

    // No ambiente do navegador, usar URL absoluta
    if (typeof window !== 'undefined') {
      apiUrl = `${window.location.origin}${apiUrl}`
    }

    const response = await fetch(apiUrl, {
      method: 'GET',
      credentials: 'include', // Importante: inclui cookies de autenticação
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

    if (!response.ok) {
      console.error(`Erro ao buscar episódios do anime ${animeId}: ${response.status} ${response.statusText}`);
      return [];
    }

    const episodes = await response.json();
    return episodes;
  } catch (error) {
    console.error(`Erro ao buscar episódios do anime ${animeId}:`, error);
    return [];
  }
}
