import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/jwt'

/**
 * Middleware para verificar autenticação de admin
 */
export function verifyAdminAuth(request: NextRequest): boolean {
  try {
    const token = request.cookies.get('admin_token')?.value

    if (!token) {
      console.log('Token de admin não encontrado')
      return false
    }

    const payload = verifyToken(token)
    
    if (!payload || payload.role !== 'admin') {
      console.log('Token de admin inválido ou role incorreta')
      return false
    }

    console.log(`Admin autenticado: ${payload.email}`)
    return true
  } catch (error) {
    console.error('Erro na verificação de autenticação admin:', error)
    return false
  }
}

/**
 * Middleware para proteger rotas de admin
 */
export function protectAdminRoute(request: NextRequest) {
  const isAuthenticated = verifyAdminAuth(request)
  
  if (!isAuthenticated) {
    // Redirecionar para página de login
    const loginUrl = new URL('/auth/login', request.url)
    return NextResponse.redirect(loginUrl)
  }
  
  return NextResponse.next()
}

/**
 * Verificar se as credenciais de admin estão configuradas
 */
export function checkAdminConfig(): { isConfigured: boolean; message?: string } {
  const adminEmail = process.env.ADMIN_EMAIL
  const adminPassword = process.env.ADMIN_PASSWORD
  
  if (!adminEmail || !adminPassword) {
    return {
      isConfigured: false,
      message: 'Credenciais de admin não configuradas nas variáveis de ambiente'
    }
  }
  
  if (adminEmail === '<EMAIL>' && adminPassword === 'admin123') {
    return {
      isConfigured: false,
      message: 'Usando credenciais padrão. Configure ADMIN_EMAIL e ADMIN_PASSWORD para maior segurança'
    }
  }
  
  return { isConfigured: true }
}
