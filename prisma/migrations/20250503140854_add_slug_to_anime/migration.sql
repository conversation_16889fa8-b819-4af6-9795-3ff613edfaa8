/*
  Warnings:

  - A unique constraint covering the columns `[slug]` on the table `Anime` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `slug` to the `Anime` table without a default value. This is not possible if the table is not empty.

*/
-- <PERSON><PERSON>, adicionamos a coluna slug como nullable
ALTER TABLE "Anime" ADD COLUMN "slug" TEXT;

-- Atualizamos os slugs existentes baseados nos títulos
UPDATE "Anime"
SET "slug" = LOWER(
  REGEXP_REPLACE(
    REGEXP_REPLACE(
      "title",
      '[^a-zA-Z0-9]+',
      '-',
      'g'
    ),
    '^-|-$',
    '',
    'g'
  )
);

-- <PERSON><PERSON><PERSON> tornamos a coluna slug obrigatória e única
ALTER TABLE "Anime" ALTER COLUMN "slug" SET NOT NULL;
ALTER TABLE "Anime" ADD CONSTRAINT "Anime_slug_key" UNIQUE ("slug");
