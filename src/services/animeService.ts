export interface Anime {
  mal_id: number;
  title: string;
  images: {
    jpg: {
      image_url: string;
      large_image_url: string;
      small_image_url: string;
    };
  };
  synopsis: string;
  score: number;
  status: string;
  episodes: number;
  genres: Array<{
    name: string;
  }>;
  studios: Array<{
    name: string;
  }>;
  year: number;
  aired: {
    from: string;
  };
}

export interface Episode {
  mal_id: number;
  title: string;
  episode: string;
  url: string;
  aired: string;
  image_url: string;
  streaming_links?: {
    name: string;
    url: string;
    quality?: string;
  }[];
}

const JIKAN_API_URL = 'https://api.jikan.moe/v4';

export async function getTopAnimes(page = 1): Promise<Anime[]> {
  try {
    const response = await fetch(`${JIKAN_API_URL}/top/anime?page=${page}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Top Animes Response:', data); // Debug log
    
    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid response format');
    }

    return data.data.map((anime: any) => ({
      mal_id: anime.mal_id,
      title: anime.title,
      images: {
        jpg: {
          image_url: anime.images?.jpg?.image_url || '',
          large_image_url: anime.images?.jpg?.large_image_url || '',
          small_image_url: anime.images?.jpg?.small_image_url || '',
        }
      },
      synopsis: anime.synopsis || '',
      score: anime.score || 0,
      status: anime.status || 'Unknown',
      episodes: anime.episodes || 0,
      genres: anime.genres || [],
      studios: anime.studios || [],
      year: anime.aired?.from ? new Date(anime.aired.from).getFullYear() : 0,
      aired: anime.aired || { from: '' },
    }));
  } catch (error) {
    console.error('Error fetching top animes:', error);
    return [];
  }
}

export async function getRecentEpisodes(page = 1): Promise<Episode[]> {
  try {
    const response = await fetch(`${JIKAN_API_URL}/watch/episodes?page=${page}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    
    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid response format');
    }

    return data.data.map((item: any) => {
      if (!item.entry || !item.episodes?.[0]) {
        return null;
      }

      const episode = item.episodes[0];
      const entry = item.entry;

      // Extrai o número do episódio
      let episodeNumber = '';
      
      if (episode.episode) {
        episodeNumber = episode.episode.toString();
      } else if (episode.title) {
        const patterns = [
          /Episódio (\d+)/i,
          /Episode (\d+)/i,
          /Ep\.? (\d+)/i,
          /#(\d+)/i
        ];
        
        for (const pattern of patterns) {
          const match = episode.title.match(pattern);
          if (match) {
            episodeNumber = match[1];
            break;
          }
        }
      }

      if (!episodeNumber) {
        episodeNumber = '1';
      }

      // Tratamento da data
      let airedDate = '';
      if (episode.aired) {
        try {
          if (typeof episode.aired === 'string') {
            const cleanDate = episode.aired.split('+')[0].split('T')[0];
            const dateFormats = [
              episode.aired,
              cleanDate,
              `${cleanDate}T00:00:00`
            ];

            for (const format of dateFormats) {
              const date = new Date(format);
              if (!isNaN(date.getTime())) {
                airedDate = date.toISOString();
                break;
              }
            }
          }
        } catch (error) {
          console.error('Error parsing date:', error);
        }
      }

      // Gera links de streaming baseados no título
      const searchQuery = encodeURIComponent(`${entry.title} Episódio ${episodeNumber}`);
      const streamingLinks = [
        {
          name: 'AniTube',
          url: `https://anitube.site/?s=${searchQuery}`,
          quality: 'HD'
        },
        {
          name: 'AnimeFire',
          url: `https://animefire.net/pesquisar/${searchQuery}`,
          quality: 'HD'
        },
        {
          name: 'AnimeOnline',
          url: `https://animeonline.ninja/?s=${searchQuery}`,
          quality: 'HD'
        }
      ];

      return {
        mal_id: entry.mal_id,
        title: entry.title,
        episode: episodeNumber,
        url: episode.url || '',
        aired: airedDate,
        image_url: entry.images?.jpg?.large_image_url || 
                  entry.images?.jpg?.image_url || 
                  episode.images?.jpg?.large_image_url || 
                  episode.images?.jpg?.image_url || '',
        streaming_links: streamingLinks
      };
    }).filter(Boolean);
  } catch (error) {
    console.error('Error fetching recent episodes:', error);
    return [];
  }
}

export async function getAnimeDetails(id: number): Promise<Anime | null> {
  try {
    const response = await fetch(`${JIKAN_API_URL}/anime/${id}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Anime Details Response:', data); // Debug log
    
    if (!data.data) {
      throw new Error('Invalid response format');
    }

    const anime = data.data;
    return {
      mal_id: anime.mal_id,
      title: anime.title,
      images: {
        jpg: {
          image_url: anime.images?.jpg?.image_url || '',
          large_image_url: anime.images?.jpg?.large_image_url || '',
          small_image_url: anime.images?.jpg?.small_image_url || '',
        }
      },
      synopsis: anime.synopsis || '',
      score: anime.score || 0,
      status: anime.status || 'Unknown',
      episodes: anime.episodes || 0,
      genres: anime.genres || [],
      studios: anime.studios || [],
      year: anime.aired?.from ? new Date(anime.aired.from).getFullYear() : 0,
      aired: anime.aired || { from: '' },
    };
  } catch (error) {
    console.error('Error fetching anime details:', error);
    return null;
  }
}

export async function searchAnimes(query: string, page = 1): Promise<Anime[]> {
  try {
    const response = await fetch(`${JIKAN_API_URL}/anime?q=${encodeURIComponent(query)}&page=${page}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Search Response:', data); // Debug log
    
    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid response format');
    }

    return data.data.map((anime: any) => ({
      mal_id: anime.mal_id,
      title: anime.title,
      images: {
        jpg: {
          image_url: anime.images?.jpg?.image_url || '',
          large_image_url: anime.images?.jpg?.large_image_url || '',
          small_image_url: anime.images?.jpg?.small_image_url || '',
        }
      },
      synopsis: anime.synopsis || '',
      score: anime.score || 0,
      status: anime.status || 'Unknown',
      episodes: anime.episodes || 0,
      genres: anime.genres || [],
      studios: anime.studios || [],
      year: anime.aired?.from ? new Date(anime.aired.from).getFullYear() : 0,
      aired: anime.aired || { from: '' },
    }));
  } catch (error) {
    console.error('Error searching animes:', error);
    return [];
  }
} 