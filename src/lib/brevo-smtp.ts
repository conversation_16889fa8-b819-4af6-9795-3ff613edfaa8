import nodemailer from 'nodemailer';

// Configuração do transporte SMTP do Brevo
export const getBrevoTransporter = () => {
  const user = process.env.BREVO_SMTP_USER || process.env.EMAIL_FROM;
  const pass = process.env.BREVO_SMTP_PASSWORD || process.env.BREVO_API_KEY;

  console.log('Configurando transporte SMTP do Brevo:');
  console.log('- Host: smtp-relay.brevo.com');
  console.log('- Porta: 587');
  console.log('- Usu<PERSON><PERSON>:', user);
  console.log('- <PERSON>ha (primeiros 4 caracteres):', pass?.substring(0, 4) + '...');

  return nodemailer.createTransport({
    host: 'smtp-relay.brevo.com',
    port: 587,
    secure: false, // true para 465, false para outras portas
    auth: {
      user,
      pass,
    },
  });
};

// Função para enviar email usando SMTP
export const sendEmailSmtp = async ({
  to,
  subject,
  html,
  from = process.env.EMAIL_FROM || '<EMAIL>',
  fromName = 'AnimesZera',
}: {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
  fromName?: string;
}) => {
  try {
    console.log('Enviando email via SMTP do Brevo');
    console.log('- De:', `"${fromName}" <${from}>`);
    console.log('- Para:', Array.isArray(to) ? to.join(', ') : to);
    console.log('- Assunto:', subject);

    const transporter = getBrevoTransporter();

    const mailOptions = {
      from: `"${fromName}" <${from}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
    };

    const info = await transporter.sendMail(mailOptions);

    console.log('- Email enviado:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error: any) {
    console.error('Erro ao enviar email via SMTP do Brevo:', error.message);
    if (error.response) {
      console.error('- Resposta de erro:', error.response);
    }
    throw error;
  }
};
