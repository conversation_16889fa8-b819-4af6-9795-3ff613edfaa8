'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useSession } from 'next-auth/react'
import FavoriteButton from '@/components/FavoriteButton'
import { XMarkIcon, FunnelIcon } from '@heroicons/react/24/outline'
import AdBanner from '@/components/AdBanner'

// Function to debounce input
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

interface FilterState {
  searchQuery: string
  selectedGenre: string | null
  selectedStatus: string | null
  selectedYear: number | null
  sortBy: 'title' | 'year' | 'episodes'
  sortOrder: 'asc' | 'desc'
}

interface Anime {
  id: string
  title: string
  description: string
  image: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
  slug: string
  audio?: string
  releaseDay?: string
}

export default function FavoritesPage() {
  const { data: session } = useSession()
  const [animes, setAnimes] = useState<Anime[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedGenre, setSelectedGenre] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null)
  const [selectedYear, setSelectedYear] = useState<number | null>(null)
  const [sortBy, setSortBy] = useState<'title' | 'year' | 'episodes'>('title')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [showFilters, setShowFilters] = useState(false)

  // Debounce search query to avoid excessive filtering
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Load saved filters from localStorage on initial load
  useEffect(() => {
    const savedFilters = localStorage.getItem('favoriteFilters')
    if (savedFilters) {
      try {
        const parsedFilters = JSON.parse(savedFilters) as FilterState
        setSearchQuery(parsedFilters.searchQuery || '')
        setSelectedGenre(parsedFilters.selectedGenre)
        setSelectedStatus(parsedFilters.selectedStatus)
        setSelectedYear(parsedFilters.selectedYear)
        setSortBy(parsedFilters.sortBy || 'title')
        setSortOrder(parsedFilters.sortOrder || 'asc')
      } catch (error) {
        console.error('Error parsing saved filters:', error)
        // If there's an error, clear the saved filters
        localStorage.removeItem('favoriteFilters')
      }
    }
  }, [])

  // Save filters to localStorage whenever they change
  useEffect(() => {
    const filtersToSave: FilterState = {
      searchQuery,
      selectedGenre,
      selectedStatus,
      selectedYear,
      sortBy,
      sortOrder
    }
    localStorage.setItem('favoriteFilters', JSON.stringify(filtersToSave))
  }, [
    searchQuery,
    selectedGenre,
    selectedStatus,
    selectedYear,
    sortBy,
    sortOrder
  ])

  useEffect(() => {
    if (session) {
      fetchFavorites()
    }
  }, [session])

  const fetchFavorites = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/favorites')
      if (!response.ok) {
        throw new Error('Failed to fetch favorites')
      }
      const data = await response.json()
      setAnimes(data)
    } catch (error) {
      console.error('Error fetching favorites:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFavoriteToggle = async (animeId: string) => {
    try {
      const response = await fetch('/api/favorites', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ animeId }),
      })

      if (!response.ok) {
        throw new Error('Failed to remove favorite')
      }

      setAnimes(prev => prev.filter(anime => anime.id !== animeId))
    } catch (error) {
      console.error('Error removing favorite:', error)
      fetchFavorites()
    }
  }

  // Function to clear all filters
  const clearFilters = useCallback(() => {
    setSearchQuery('')
    setSelectedGenre(null)
    setSelectedStatus(null)
    setSelectedYear(null)
    setSortBy('title')
    setSortOrder('asc')
  }, [])

  // Count active filters for UI indicator
  const activeFilterCount = [
    selectedGenre,
    selectedStatus,
    selectedYear
  ].filter(Boolean).length

  const filteredAnimes = animes.filter(anime => {
    const matchesSearch = anime.title.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
    const matchesGenre = !selectedGenre || anime.genres.includes(selectedGenre)
    const matchesStatus = !selectedStatus || anime.status === selectedStatus
    const matchesYear = !selectedYear || anime.year === selectedYear
    return matchesSearch && matchesGenre && matchesStatus && matchesYear
  })

  const sortedAnimes = [...filteredAnimes].sort((a, b) => {
    if (sortBy === 'title') {
      return sortOrder === 'asc'
        ? a.title.localeCompare(b.title)
        : b.title.localeCompare(a.title)
    } else if (sortBy === 'year') {
      return sortOrder === 'asc'
        ? a.year - b.year
        : b.year - a.year
    } else {
      return sortOrder === 'asc'
        ? a.totalEpisodes - b.totalEpisodes
        : b.totalEpisodes - a.totalEpisodes
    }
  })

  const allGenres = Array.from(new Set(animes.flatMap(anime => anime.genres)))
  const allYears = Array.from(new Set(animes.map(anime => anime.year))).sort((a, b) => b - a)
  const allStatuses = Array.from(new Set(animes.map(anime => anime.status)))

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Faça login para ver seus favoritos</h1>
          <Link
            href="/login"
            className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Fazer Login
          </Link>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900 text-white">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Banner Topo */}
        <div className="mb-6">
          <AdBanner position="top" />
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 space-y-4 md:space-y-0">
          <h1 className="text-3xl font-bold">Meus Favoritos</h1>

          {/* Search and filter toggle */}
          <div className="flex w-full md:w-auto gap-2">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Buscar anime..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              )}
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-1 px-4 py-2 rounded-lg transition-colors ${
                activeFilterCount > 0
                  ? 'bg-purple-600 hover:bg-purple-700'
                  : 'bg-gray-800 hover:bg-gray-700'
              }`}
            >
              <FunnelIcon className="h-5 w-5" />
              <span className="hidden sm:inline">Filtros</span>
              {activeFilterCount > 0 && (
                <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-bold rounded-full bg-white text-purple-800">
                  {activeFilterCount}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Expanded filter section */}
        {showFilters && (
          <>
            <div className="bg-gray-800 rounded-lg p-4 mb-6 animate-fadeIn">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Filtros</h2>
                <div className="flex gap-2">
                  <button
                    onClick={clearFilters}
                    className="px-3 py-1 text-sm bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    Limpar Filtros
                  </button>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="p-1 rounded-full hover:bg-gray-700 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {/* Genre filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Gênero</label>
                  <select
                    value={selectedGenre || ''}
                    onChange={(e) => setSelectedGenre(e.target.value || null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Todos os Gêneros</option>
                    {allGenres.map(genre => (
                      <option key={genre} value={genre}>{genre}</option>
                    ))}
                  </select>
                </div>

                {/* Status filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
                  <select
                    value={selectedStatus || ''}
                    onChange={(e) => setSelectedStatus(e.target.value || null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Todos os Status</option>
                    {allStatuses.map(status => (
                      <option key={status} value={status}>{status}</option>
                    ))}
                  </select>
                </div>

                {/* Year filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Ano</label>
                  <select
                    value={selectedYear || ''}
                    onChange={(e) => setSelectedYear(e.target.value ? parseInt(e.target.value) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Todos os Anos</option>
                    {allYears.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>

                {/* Sort options */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Ordenar por</label>
                  <select
                    value={`${sortBy}-${sortOrder}`}
                    onChange={(e) => {
                      const [by, order] = e.target.value.split('-')
                      setSortBy(by as 'title' | 'year' | 'episodes')
                      setSortOrder(order as 'asc' | 'desc')
                    }}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="title-asc">Título (A-Z)</option>
                    <option value="title-desc">Título (Z-A)</option>
                    <option value="year-desc">Ano (Mais Recente)</option>
                    <option value="year-asc">Ano (Mais Antigo)</option>
                    <option value="episodes-desc">Episódios (Mais)</option>
                    <option value="episodes-asc">Episódios (Menos)</option>
                  </select>
                </div>
              </div>
            </div>
            {/* Banner In-Content após filtros */}
            <div className="mb-6">
              <AdBanner position="in-content" />
            </div>
          </>
        )}

        {/* Results count */}
        <div className="flex justify-between items-center mb-4">
          <p className="text-gray-400">
            {sortedAnimes.length} {sortedAnimes.length === 1 ? 'anime encontrado' : 'animes encontrados'}
          </p>
        </div>

        {sortedAnimes.length === 0 ? (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-4">Nenhum anime favorito encontrado</h2>
            <Link
              href="/animes"
              className="text-purple-400 hover:text-purple-300"
            >
              Explorar animes
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {sortedAnimes.map(anime => (
              <div
                key={anime.id}
                className="group relative bg-gray-800 rounded-lg overflow-hidden shadow-lg transition-transform duration-300 hover:scale-105"
              >
                <Link href={`/animes/${anime.slug}`} className="block">
                  <div className="relative aspect-[2/3] w-full">
                    <Image
                      src={anime.image}
                      alt={anime.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute top-2 right-2 z-10">
                      <FavoriteButton
                        animeId={anime.id}
                        isFavorite={true}
                        onToggle={() => handleFavoriteToggle(anime.id)}
                      />
                    </div>
                    <div className="absolute top-2 left-2 bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                      Favorito
                    </div>
                  </div>
                  <div className="p-4">
                    <h2 className="text-lg font-semibold mb-2 line-clamp-2">{anime.title}</h2>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {anime.genres.slice(0, 3).map((genre, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-purple-900 text-purple-200 rounded-full text-xs"
                        >
                          {genre}
                        </span>
                      ))}
                      {anime.genres.length > 3 && (
                        <span className="px-2 py-1 bg-gray-700 text-gray-300 rounded-full text-xs">
                          +{anime.genres.length - 3}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>{anime.year}</span>
                      <span>{anime.totalEpisodes === 0 ? "??" : anime.totalEpisodes} episódios</span>
                    </div>
                    {anime.audio && (
                      <div className="mt-2 flex items-center">
                        <span className={`px-2 py-0.5 rounded-full text-xs ${
                          anime.audio === 'Dublado'
                            ? 'bg-red-900/50 text-red-200'
                            : anime.audio === 'Dual Áudio'
                              ? 'bg-yellow-900/50 text-yellow-200'
                              : 'bg-blue-900/50 text-blue-200'
                        }`}>
                          {anime.audio}
                        </span>
                      </div>
                    )}
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}

        {/* Banner Rodapé */}
        <div className="mt-8">
          <AdBanner position="bottom" />
        </div>
      </div>
    </div>
  )
}