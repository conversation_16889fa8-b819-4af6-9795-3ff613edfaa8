name: Update Episode Titles

on:
  # Executar diariamente às 03:00 UTC (00:00 BRT)
  schedule:
    - cron: '0 3 * * *'
  
  # Permitir execução manual
  workflow_dispatch:
    inputs:
      force_update:
        description: 'Forçar atualização de todos os episódios'
        required: false
        default: 'false'
        type: choice
        options:
          - 'false'
          - 'true'
      anime_id:
        description: 'ID específico do anime (opcional)'
        required: false
        type: string

jobs:
  update-titles:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Update Episode Titles
        run: |
          echo "🔄 Iniciando atualização de títulos de episódios..."
          
          # Preparar dados para a requisição
          FORCE_UPDATE="${{ github.event.inputs.force_update || 'false' }}"
          ANIME_ID="${{ github.event.inputs.anime_id || '' }}"
          
          # Construir JSON payload
          if [ -n "$ANIME_ID" ]; then
            PAYLOAD="{\"animeId\":\"$ANIME_ID\",\"forceUpdate\":$FORCE_UPDATE}"
          else
            PAYLOAD="{\"forceUpdate\":$FORCE_UPDATE}"
          fi
          
          echo "📊 Payload: $PAYLOAD"
          
          # Fazer requisição para a API
          RESPONSE=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer github-action" \
            -H "X-API-Key: ${{ secrets.CRON_API_KEY }}" \
            -d "$PAYLOAD" \
            "${{ secrets.NEXT_PUBLIC_APP_URL }}/api/episodes/update-titles")
          
          # Separar corpo da resposta e código HTTP
          HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
          BODY=$(echo "$RESPONSE" | head -n -1)
          
          echo "📈 Código HTTP: $HTTP_CODE"
          echo "📋 Resposta: $BODY"
          
          # Verificar se a requisição foi bem-sucedida
          if [ "$HTTP_CODE" -eq 200 ]; then
            echo "✅ Atualização de títulos concluída com sucesso!"
            
            # Extrair informações do resumo (se disponível)
            UPDATED=$(echo "$BODY" | grep -o '"updated":[0-9]*' | cut -d':' -f2 || echo "0")
            ERRORS=$(echo "$BODY" | grep -o '"errors":[0-9]*' | cut -d':' -f2 || echo "0")
            TOTAL=$(echo "$BODY" | grep -o '"totalChecked":[0-9]*' | cut -d':' -f2 || echo "0")
            
            echo "📊 Resumo:"
            echo "   • Total verificado: $TOTAL episódios"
            echo "   • Títulos atualizados: $UPDATED"
            echo "   • Erros: $ERRORS"
            
            # Criar arquivo de log para artifacts
            echo "# Relatório de Atualização de Títulos - $(date)" > update-report.md
            echo "" >> update-report.md
            echo "## Resumo" >> update-report.md
            echo "- **Data/Hora**: $(date)" >> update-report.md
            echo "- **Total verificado**: $TOTAL episódios" >> update-report.md
            echo "- **Títulos atualizados**: $UPDATED" >> update-report.md
            echo "- **Erros**: $ERRORS" >> update-report.md
            echo "- **Forçar atualização**: $FORCE_UPDATE" >> update-report.md
            if [ -n "$ANIME_ID" ]; then
              echo "- **Anime específico**: $ANIME_ID" >> update-report.md
            fi
            echo "" >> update-report.md
            echo "## Resposta Completa" >> update-report.md
            echo '```json' >> update-report.md
            echo "$BODY" >> update-report.md
            echo '```' >> update-report.md
            
          else
            echo "❌ Erro na atualização de títulos!"
            echo "Código HTTP: $HTTP_CODE"
            echo "Resposta: $BODY"
            
            # Criar arquivo de erro
            echo "# Erro na Atualização de Títulos - $(date)" > error-report.md
            echo "" >> error-report.md
            echo "**Código HTTP**: $HTTP_CODE" >> error-report.md
            echo "" >> error-report.md
            echo "**Resposta**:" >> error-report.md
            echo '```' >> error-report.md
            echo "$BODY" >> error-report.md
            echo '```' >> error-report.md
            
            exit 1
          fi

      - name: Upload Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: episode-titles-update-report-${{ github.run_number }}
          path: |
            update-report.md
            error-report.md
          retention-days: 30

      - name: Notify on Failure
        if: failure()
        run: |
          echo "🚨 A atualização de títulos falhou!"
          echo "Verifique os logs e o relatório de erro nos artifacts."
          
          # Aqui você pode adicionar notificações adicionais:
          # - Webhook para Discord/Slack
          # - Email
          # - Telegram, etc.

  # Job opcional para notificações de sucesso (apenas em execução manual)
  notify-success:
    runs-on: ubuntu-latest
    needs: update-titles
    if: github.event_name == 'workflow_dispatch' && success()
    
    steps:
      - name: Success Notification
        run: |
          echo "🎉 Atualização manual de títulos concluída com sucesso!"
          echo "Verifique o relatório nos artifacts para detalhes."
