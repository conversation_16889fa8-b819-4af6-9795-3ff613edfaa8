'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { toast } from 'react-hot-toast'
import Anime<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/AnimeUpdateChecker'
import ImportProgressBar from '@/components/ImportProgressBar'
import AdminConfigStatus from '@/components/AdminConfigStatus'
import EpisodeTitleUpdater from '@/components/EpisodeTitleUpdater'
import EpisodeTitleTranslator from '@/components/EpisodeTitleTranslator'

interface Anime {
  id: string
  title: string
  description: string
  image: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
  slug: string
  audio?: string
  releaseDay?: string
  episodes: {
    id: string
    number: number
    title: string
    airDate: string
    frame: string
    videoUrl: string
  }[]
}

interface MALAnime {
  node: {
    id: number
    title: string
    main_picture: {
      medium: string
      large: string
    }
    synopsis: string
    status: string
    num_episodes: number
    start_date: string
    studios: {
      name: string
    }[]
    genres: {
      name: string
    }[]
  }
}

interface MALEpisode {
  number: number
  title: string
  airDate: string
  frame: string
}

export default function AdminAnimesPage() {
  const [animes, setAnimes] = useState<Anime[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAnimeModal, setShowAnimeModal] = useState(false)
  const [showEpisodeModal, setShowEpisodeModal] = useState(false)
  const [showBulkUrlModal, setShowBulkUrlModal] = useState(false)
  const [showImportAnimeModal, setShowImportAnimeModal] = useState(false)
  const [editingAnime, setEditingAnime] = useState<Anime | null>(null)
  const [editingEpisode, setEditingEpisode] = useState<Anime['episodes'][0] | null>(null)
  const [selectedAnime, setSelectedAnime] = useState<Anime | null>(null)
  const [expandedAnimeId, setExpandedAnimeId] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<MALAnime[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null)
  const [malEpisodes, setMalEpisodes] = useState<MALEpisode[]>([])
  const [isLoadingEpisodes, setIsLoadingEpisodes] = useState(false)
  const [isImportingEpisodes, setIsImportingEpisodes] = useState(false)
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [baseUrl, setBaseUrl] = useState('')
  const [urlFormat, setUrlFormat] = useState('{episode}/480p.mp4')
  const [isUpdatingUrls, setIsUpdatingUrls] = useState(false)
  const [importAnimeUrl, setImportAnimeUrl] = useState('')
  const [isImportingAnime, setIsImportingAnime] = useState(false)
  const [showAnimeUpdateModal, setShowAnimeUpdateModal] = useState(false)
  const [selectedAnimeForUpdate, setSelectedAnimeForUpdate] = useState<Anime | null>(null)

  useEffect(() => {
    fetchAnimes()
  }, [])

  const fetchAnimes = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/animes')
      if (!response.ok) {
        throw new Error('Failed to fetch animes')
      }
      const data = await response.json()
      setAnimes(data)
    } catch (error) {
      console.error('Error fetching animes:', error)
      alert('Erro ao carregar animes')
    } finally {
      setIsLoading(false)
    }
  }

  const searchAnime = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      return
    }

    try {
      setIsSearching(true)
      const response = await fetch(`/api/mal/search?q=${encodeURIComponent(query)}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Erro ao buscar anime')
      }

      const data = await response.json()
      if (!Array.isArray(data)) {
        throw new Error('Formato de resposta inválido')
      }

      setSearchResults(data)
    } catch (error) {
      console.error('Error searching anime:', error)
      if (error instanceof Error) {
        if (error.message.includes('Rate limit exceeded')) {
          alert('Muitas buscas em pouco tempo. Por favor, aguarde um momento.')
        } else {
          alert(error.message)
        }
      } else {
        alert('Erro ao buscar anime')
      }
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)

    // Limpa o timeout anterior
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }

    // Cria um novo timeout para debounce
    const timeout = setTimeout(() => {
      searchAnime(query)
    }, 500) // 500ms de delay

    setSearchTimeout(timeout)
  }

  // Limpa o timeout quando o componente é desmontado
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
    }
  }, [searchTimeout])

  const handleSelectAnime = async (malAnime: MALAnime) => {
    // Traduz o status
    const statusMap: { [key: string]: string } = {
      'currently_airing': 'Em Andamento',
      'finished_airing': 'Concluído',
      'not_yet_aired': 'Não Lançado'
    }

    // Traduz os gêneros
    const genreMap: { [key: string]: string } = {
      'Action': 'Ação',
      'Adventure': 'Aventura',
      'Comedy': 'Comédia',
      'Drama': 'Drama',
      'Fantasy': 'Fantasia',
      'Horror': 'Terror',
      'Mystery': 'Mistério',
      'Romance': 'Romance',
      'Sci-Fi': 'Ficção Científica',
      'Slice of Life': 'Slice of Life',
      'Sports': 'Esportes',
      'Supernatural': 'Sobrenatural',
      'Psychological': 'Psicológico',
      'Thriller': 'Suspense',
      'Military': 'Militar',
      'Historical': 'Histórico',
      'Martial Arts': 'Artes Marciais',
      'School': 'Escolar',
      'Music': 'Música',
      'Parody': 'Paródia',
      'Samurai': 'Samurai',
      'Shoujo': 'Shoujo',
      'Shounen': 'Shounen',
      'Seinen': 'Seinen',
      'Josei': 'Josei',
      'Ecchi': 'Ecchi',
      'Harem': 'Harem',
      'Mecha': 'Mecha',
      'Space': 'Espacial',
      'Vampire': 'Vampiros',
      'Yaoi': 'Yaoi',
      'Yuri': 'Yuri',
      'Hentai': 'Hentai',
      'Demons': 'Demônios',
      'Magic': 'Magia',
      'Police': 'Policial',
      'Super Power': 'Super Poderes',
      'Game': 'Jogos',
      'Cars': 'Carros',
      'Kids': 'Infantil',
      'Shoujo Ai': 'Shoujo Ai',
      'Shounen Ai': 'Shounen Ai',
      'Doujinshi': 'Doujinshi',
      'Gender Bender': 'Mudança de Gênero',
      'Gourmet': 'Gastronomia',
      'Work Life': 'Vida Profissional',
      'Erotica': 'Erótico',
      'Adult Cast': 'Elenco Adulto',
      'Anthropomorphic': 'Antropomórfico',
      'CGDCT': 'Garotas Fazendo Coisas Fofas',
      'Childcare': 'Cuidado Infantil',
      'Combat Sports': 'Esportes de Combate',
      'Crossdressing': 'Crossdressing',
      'Delinquents': 'Delinquentes',
      'Educational': 'Educacional',
      'Gag Humor': 'Humor Pastelão',
      'Gore': 'Gore',
      'High Stakes Game': 'Jogo de Alto Risco',
      'Idols (Female)': 'Idols (Feminino)',
      'Idols (Male)': 'Idols (Masculino)',
      'Isekai': 'Isekai',
      'Iyashikei': 'Iyashikei',
      'Love Polygon': 'Polígono Amoroso',
      'Otaku Culture': 'Cultura Otaku',
      'Performing Arts': 'Artes Performáticas',
      'Pets': 'Animais de Estimação',
      'Reincarnation': 'Reencarnação',
      'Reverse Harem': 'Reverse Harem',
      'Romantic Subtext': 'Subtexto Romântico',
      'Showbiz': 'Showbiz',
      'Survival': 'Sobrevivência',
      'Team Sports': 'Esportes em Equipe',
      'Time Travel': 'Viagem no Tempo',
      'Video Game': 'Videogame',
      'Visual Arts': 'Artes Visuais',
      'Workplace': 'Local de Trabalho'
    }

    const animeData = {
      title: malAnime.node.title,
      description: malAnime.node.synopsis,
      image: malAnime.node.main_picture.large,
      status: statusMap[malAnime.node.status] || 'Desconhecido',
      totalEpisodes: malAnime.node.num_episodes,
      studio: malAnime.node.studios[0]?.name || 'Desconhecido',
      year: new Date(malAnime.node.start_date).getFullYear(),
      genres: malAnime.node.genres.map(g => genreMap[g.name] || g.name),
    }

    setEditingAnime(animeData as any)
    setShowAnimeModal(true)
    setSearchQuery('')
    setSearchResults([])

    // Busca informações dos episódios
    try {
      setIsLoadingEpisodes(true)
      setLoadingProgress(0)
      setMalEpisodes([]) // Limpa os episódios anteriores

      // Cria um EventSource para receber atualizações de progresso
      const eventSource = new EventSource(`/api/mal/anime/${malAnime.node.id}/progress`)

      eventSource.onmessage = (event) => {
        const progress = parseInt(event.data)
        console.log('Progresso atual:', progress)
        setLoadingProgress(progress)
      }

      eventSource.onerror = (error) => {
        console.error('Erro no EventSource:', error)
        eventSource.close()
      }

      const response = await fetch(`/api/mal/anime/${malAnime.node.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch episodes')
      }
      const data = await response.json()
      console.log('Episódios carregados:', data.episodes.length)
      setMalEpisodes(data.episodes)
      setLoadingProgress(100)

      // Fecha o EventSource
      eventSource.close()
    } catch (error) {
      console.error('Error fetching episodes:', error)
      setLoadingProgress(0)
      alert('Erro ao carregar episódios. Por favor, tente novamente.')
    } finally {
      setIsLoadingEpisodes(false)
    }
  }

  const handleSubmitAnime = async (e: React.FormEvent) => {
    e.preventDefault()

    const formData = new FormData(e.target as HTMLFormElement)
    const genresString = formData.get('genres') as string
    const genres = genresString.split(',').map(genre => genre.trim()).filter(genre => genre !== '')

    // Processar o valor de totalEpisodes
    const totalEpisodesValue = formData.get('totalEpisodes') as string;
    let totalEpisodes = 0;

    // Se o valor for "??", definir como 0 (será exibido como "??" na interface)
    if (totalEpisodesValue !== "??") {
      totalEpisodes = parseInt(totalEpisodesValue) || 0;
    }

    const animeData = {
      title: formData.get('title'),
      description: formData.get('description'),
      image: formData.get('image'),
      status: formData.get('status'),
      totalEpisodes: totalEpisodes,
      studio: formData.get('studio'),
      year: parseInt(formData.get('year') as string),
      genres: genres,
      audio: formData.get('audio'),
      releaseDay: formData.get('releaseDay'),
    }

    try {
      if (editingAnime?.slug) {
        const response = await fetch(`/api/animes/${editingAnime.slug}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(animeData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Erro ao atualizar anime')
        }
      } else {
        const response = await fetch('/api/animes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(animeData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Erro ao criar anime')
        }
      }

      setShowAnimeModal(false)
      fetchAnimes()
    } catch (error) {
      console.error('Error saving anime:', error)
      alert(error instanceof Error ? error.message : 'Erro ao salvar anime')
    }
  }

  const handleSubmitEpisode = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedAnime) return

    const formData = new FormData(e.target as HTMLFormElement)
    const episodeData = {
      number: parseInt(formData.get('number') as string),
      title: formData.get('title'),
      airDate: formData.get('airDate'),
      frame: formData.get('frame'),
      videoUrl: formData.get('videoUrl'),
    }

    try {
      if (editingEpisode) {
        const response = await fetch(`/api/animes/${selectedAnime.slug}/episodes/${editingEpisode.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(episodeData),
        })

        if (!response.ok) {
          throw new Error('Failed to update episode')
        }
      } else {
        const response = await fetch(`/api/animes/${selectedAnime.slug}/episodes`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(episodeData),
        })

        if (!response.ok) {
          throw new Error('Failed to create episode')
        }
      }

      setShowEpisodeModal(false)
      fetchAnimes()
    } catch (error) {
      console.error('Error saving episode:', error)
      alert('Erro ao salvar episódio')
    }
  }

  const handleDeleteAnime = async (animeId: string) => {
    if (!confirm('Tem certeza que deseja excluir este anime?')) {
      return
    }

    try {
      // Find the anime to get its slug
      const animeToDelete = animes.find(anime => anime.id === animeId)
      if (!animeToDelete) {
        throw new Error('Anime não encontrado')
      }

      console.log('Deletando anime:', animeToDelete.slug)
      const response = await fetch(`/api/animes/${animeToDelete.slug}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
        cache: 'no-store',
        credentials: 'same-origin',
      })

      console.log('Resposta da API:', response.status, response.statusText)

      if (!response.ok) {
        // Tenta ler o corpo da resposta apenas se houver um erro
        const errorData = await response.json().catch(() => ({ error: 'Erro ao excluir anime' }))
        throw new Error(errorData.error || 'Erro ao excluir anime')
      }

      // Se a resposta foi bem-sucedida (204 No Content), não tenta ler o corpo
      setAnimes(animes.filter(anime => anime.id !== animeId))
    } catch (error) {
      console.error('Error deleting anime:', error)
      alert(error instanceof Error ? error.message : 'Erro ao excluir anime')
    }
  }

  const handleDeleteEpisode = async (animeSlug: string, episodeId: string) => {
    if (!confirm('Tem certeza que deseja excluir este episódio?')) return

    try {
      const response = await fetch(`/api/animes/${animeSlug}/episodes/${episodeId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete episode')
      }

      fetchAnimes()
    } catch (error) {
      console.error('Error deleting episode:', error)
      alert('Erro ao excluir episódio')
    }
  }

  const handleImportAllEpisodes = async () => {
    if (!selectedAnime || malEpisodes.length === 0) return

    try {
      setIsImportingEpisodes(true)
      const promises = malEpisodes.map(episode => {
        const episodeData = {
          number: episode.number,
          title: episode.title,
          airDate: episode.airDate,
          frame: selectedAnime.image,
          videoUrl: '',
        }

        return fetch(`/api/animes/${selectedAnime.slug}/episodes`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(episodeData),
        })
      })

      await Promise.all(promises)
      setShowEpisodeModal(false)
      fetchAnimes()
      alert('Episódios importados com sucesso!')
    } catch (error) {
      console.error('Error importing episodes:', error)
      alert('Erro ao importar episódios')
    } finally {
      setIsImportingEpisodes(false)
    }
  }

  const handleBulkUpdateUrls = async () => {
    if (!selectedAnime || !baseUrl) return

    try {
      setIsUpdatingUrls(true)
      const promises = selectedAnime.episodes.map(episode => {
        const episodeUrl = `${baseUrl}${urlFormat.replace('{episode}', episode.number.toString())}`
        return fetch(`/api/animes/${selectedAnime.slug}/episodes/${episode.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...episode,
            videoUrl: episodeUrl,
          }),
        })
      })

      await Promise.all(promises)
      setShowBulkUrlModal(false)
      fetchAnimes()
      alert('URLs atualizadas com sucesso!')
    } catch (error) {
      console.error('Error updating URLs:', error)
      alert('Erro ao atualizar URLs')
    } finally {
      setIsUpdatingUrls(false)
    }
  }

  const [manualImportMode, setManualImportMode] = useState(false);
  const [animeData, setAnimeData] = useState({
    title: '',
    description: '',
    image: '',
    totalEpisodes: 0,
    episodes: [] as {number: number, videoUrl: string}[]
  });
  const [isExtractingData, setIsExtractingData] = useState(false);
  const [importId, setImportId] = useState<string | null>(null);

  // Estados para pesquisa de animes disponíveis
  const [animeSearchQuery, setAnimeSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [genreFilter, setGenreFilter] = useState('')

  // Função para filtrar animes
  const filteredAnimes = animes.filter(anime => {
    const matchesSearch = anime.title.toLowerCase().includes(animeSearchQuery.toLowerCase()) ||
                         anime.studio.toLowerCase().includes(animeSearchQuery.toLowerCase()) ||
                         anime.genres.some(genre => genre.toLowerCase().includes(animeSearchQuery.toLowerCase()))

    const matchesStatus = !statusFilter || anime.status === statusFilter
    const matchesGenre = !genreFilter || anime.genres.some(genre =>
      genre.toLowerCase().includes(genreFilter.toLowerCase())
    )

    return matchesSearch && matchesStatus && matchesGenre
  })

  // Função para lidar com a conclusão da importação
  const handleImportComplete = () => {
    setImportId(null);
    setShowImportAnimeModal(false);
    setImportAnimeUrl('');
    fetchAnimes();
    toast.success('Anime importado com sucesso!');
  };

  const handleImportAnime = async () => {
    if (!importAnimeUrl) {
      alert('Por favor, insira a URL do anime')
      return
    }

    // Resetar o ID de importação
    setImportId(null);

    try {
      setIsImportingAnime(true)

      // Primeiro, tentar a importação via API do servidor
      const response = await fetch('/api/import-anime', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          animeUrl: importAnimeUrl,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        // Verificar se é um erro de anime duplicado
        if (data.error === 'DUPLICATE_ANIME') {
          console.log('Anime duplicado detectado:', data.existingAnime);

          const continueImport = confirm(
            `⚠️ ANIME DUPLICADO DETECTADO!\n\n` +
            `O anime "${data.existingAnime.title}" já existe no banco de dados.\n\n` +
            `Informações do anime existente:\n` +
            `• Status: ${data.existingAnime.status}\n` +
            `• Total de episódios: ${data.existingAnime.totalEpisodes}\n\n` +
            `Deseja continuar mesmo assim? Isso pode criar duplicatas.`
          );

          if (!continueImport) {
            return; // Cancelar a importação
          }

          // Se o usuário escolher continuar, tentar importar mesmo assim
          // (você pode implementar uma lógica diferente aqui se necessário)
          alert('Importação cancelada para evitar duplicatas.');
          return;
        }

        // Se falhar por outro motivo, mostrar erro e oferecer modo manual
        console.error('Erro na importação automática:', data.error);

        const useManualMode = confirm(
          `Erro ao importar anime automaticamente: ${data.error}\n\n` +
          'Deseja tentar extrair os dados manualmente? ' +
          'Isso abrirá um formulário para você preencher com os dados do anime.'
        );

        if (useManualMode) {
          setManualImportMode(true);
          await extractAnimeDataManually();
        } else {
          throw new Error(data.error || 'Erro ao importar anime');
        }
      } else {
        // Se for bem-sucedido, mostrar barra de progresso
        if (data.importId) {
          console.log('ID de importação recebido:', data.importId);
          setImportId(data.importId);
          // Manter o modal aberto para mostrar o progresso
        } else {
          // Se não houver ID de importação, apenas mostrar mensagem de sucesso
          console.log('Nenhum ID de importação recebido');
          setShowImportAnimeModal(false);
          setImportAnimeUrl('');
          fetchAnimes();
          alert(data.message || 'Anime importado com sucesso!');
        }
      }
    } catch (error) {
      console.error('Error importing anime:', error)
      alert(error instanceof Error ? error.message : 'Erro ao importar anime')
    } finally {
      setIsImportingAnime(false)
    }
  }

  // Função para extrair dados do anime manualmente
  const extractAnimeDataManually = async () => {
    try {
      setIsExtractingData(true);

      // Extrair o nome do anime da URL
      const urlParts = importAnimeUrl.split('/');
      const animeName = urlParts[urlParts.length - 1].replace('-todos-os-episodios', '');

      // Tentar acessar a página do anime diretamente para extrair informações básicas
      alert(
        'Vamos tentar extrair algumas informações básicas do anime.\n' +
        'Por favor, aguarde enquanto processamos a página do anime.'
      );

      // Aqui você pode implementar uma lógica para extrair dados da página
      // Por enquanto, vamos apenas preencher com dados básicos
      setAnimeData({
        title: animeName.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
        description: 'Insira a descrição do anime',
        image: '',
        totalEpisodes: 0,
        episodes: []
      });

    } catch (error) {
      console.error('Erro ao extrair dados manualmente:', error);
    } finally {
      setIsExtractingData(false);
    }
  }

  // Função para salvar anime manualmente
  const handleSaveManualAnime = async () => {
    try {
      setIsImportingAnime(true);

      // Validar dados mínimos
      if (!animeData.title || animeData.episodes.length === 0) {
        alert('Por favor, preencha pelo menos o título e adicione alguns episódios');
        return;
      }

      // Criar o anime manualmente via API
      const response = await fetch('/api/animes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: animeData.title,
          description: animeData.description || 'Sem descrição disponível',
          image: animeData.image || 'https://via.placeholder.com/300x450?text=Sem+Imagem',
          status: 'Em Lançamento',
          totalEpisodes: animeData.episodes.length,
          studio: 'Desconhecido',
          year: new Date().getFullYear(),
          genres: ['Anime'],
          audio: 'Legendado',
          releaseDay: 'Sábado',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao criar anime');
      }

      const createdAnime = await response.json();

      // Adicionar episódios
      for (const episode of animeData.episodes) {
        await fetch(`/api/animes/${createdAnime.slug}/episodes`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            number: episode.number,
            title: `Episódio ${episode.number}`,
            airDate: new Date().toISOString(),
            frame: animeData.image || 'https://via.placeholder.com/300x450?text=Sem+Imagem',
            videoUrl: episode.videoUrl,
          }),
        });
      }

      setShowImportAnimeModal(false);
      setManualImportMode(false);
      setImportAnimeUrl('');
      setAnimeData({
        title: '',
        description: '',
        image: '',
        totalEpisodes: 0,
        episodes: []
      });

      fetchAnimes();
      alert(`Anime "${animeData.title}" importado manualmente com sucesso com ${animeData.episodes.length} episódios!`);
    } catch (error) {
      console.error('Erro ao salvar anime manualmente:', error);
      alert(error instanceof Error ? error.message : 'Erro ao salvar anime manualmente');
    } finally {
      setIsImportingAnime(false);
    }
  }

  // Função para adicionar um episódio manualmente
  const handleAddEpisode = () => {
    const number = prompt('Número do episódio:');
    if (!number) return;

    const videoUrl = prompt('URL do vídeo:');
    if (!videoUrl) return;

    setAnimeData(prev => ({
      ...prev,
      episodes: [...prev.episodes, {
        number: parseInt(number),
        videoUrl
      }]
    }));
  }

  // Função para remover um episódio
  const handleRemoveEpisode = (index: number) => {
    setAnimeData(prev => ({
      ...prev,
      episodes: prev.episodes.filter((_, i) => i !== index)
    }));
  }





  if (isLoading) {
    return <div className="flex justify-center items-center min-h-screen bg-gray-900 text-white">Carregando...</div>
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 md:gap-0 mb-8">
          <h1 className="text-3xl font-bold">Gerenciar Animes</h1>
          <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
            <div className="relative w-full sm:w-96">
              <input
                type="text"
                placeholder="Buscar anime no MyAnimeList..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {isSearching && (
                <div className="absolute right-3 top-2.5">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                </div>
              )}
              {searchResults.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-gray-800 rounded-lg shadow-lg max-h-96 overflow-y-auto">
                  {searchResults.map((result) => (
                    <div
                      key={result.node.id}
                      onClick={() => handleSelectAnime(result)}
                      className="p-3 hover:bg-gray-700 cursor-pointer flex items-center space-x-3"
                    >
                      <div className="relative w-16 h-24 flex-shrink-0">
                        <Image
                          src={result.node.main_picture.medium}
                          alt={result.node.title}
                          fill
                          className="object-cover rounded"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold truncate">{result.node.title}</h3>
                        <div className="flex flex-wrap gap-2 text-sm text-gray-400">
                          <span>{result.node.num_episodes} episódios</span>
                          <span className="hidden sm:inline">{result.node.studios[0]?.name || 'Desconhecido'}</span>
                          <span>{new Date(result.node.start_date).getFullYear()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 w-full sm:w-auto">
              <button
                onClick={() => setShowImportAnimeModal(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                </svg>
                <span className="whitespace-nowrap">Importar Anime</span>
              </button>
              <button
                onClick={() => {
                  setEditingAnime(null)
                  setShowAnimeModal(true)
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                <span className="whitespace-nowrap">Adicionar Anime</span>
              </button>
            </div>
          </div>
      </div>

        {/* Status da configuração de admin */}
        <AdminConfigStatus />

        {/* Sistema de pesquisa e filtros para animes disponíveis */}
        <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-6">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Pesquisar Animes Disponíveis</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Pesquisar</label>
                <input
                  type="text"
                  placeholder="Título, estúdio ou gênero..."
                  value={animeSearchQuery}
                  onChange={(e) => setAnimeSearchQuery(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Todos os status</option>
                  <option value="Em Andamento">Em Andamento</option>
                  <option value="Em Lançamento">Em Lançamento</option>
                  <option value="Concluído">Concluído</option>
                  <option value="Não Lançado">Não Lançado</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Gênero</label>
                <input
                  type="text"
                  placeholder="Ex: Ação, Romance..."
                  value={genreFilter}
                  onChange={(e) => setGenreFilter(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="mt-4 flex flex-wrap gap-2 text-sm text-gray-400">
              <span>Total: {animes.length} animes</span>
              <span>•</span>
              <span>Filtrados: {filteredAnimes.length} animes</span>
              {animeSearchQuery && (
                <>
                  <span>•</span>
                  <button
                    onClick={() => {
                      setAnimeSearchQuery('')
                      setStatusFilter('')
                      setGenreFilter('')
                    }}
                    className="text-blue-400 hover:text-blue-300"
                  >
                    Limpar filtros
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Componente de atualização de títulos de episódios */}
          <div id="episode-title-updater" className="mb-6">
            <EpisodeTitleUpdater />
          </div>

          {/* Componente de tradução de títulos de episódios */}
          <div id="episode-title-translator" className="mb-6">
            <EpisodeTitleTranslator />
          </div>

          {/* Componente de verificação de atualizações */}
          <div id="anime-update-checker" className="bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-6">
            <div className="p-6">
              <AnimeUpdateChecker onUpdateComplete={fetchAnimes} />
            </div>
          </div>

          {filteredAnimes.length === 0 ? (
            <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              <div className="p-6 text-center">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.881-6.08 2.33M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <h3 className="text-lg font-semibold mb-2">Nenhum anime encontrado</h3>
                  <p>Tente ajustar os filtros de pesquisa ou adicionar novos animes.</p>
                </div>
              </div>
            </div>
          ) : (
            filteredAnimes.map(anime => (
            <div key={anime.id} className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              <div className="p-6">
                <div className="flex flex-col sm:flex-row items-start justify-between gap-4">
                  <div className="flex flex-col sm:flex-row items-start gap-4 w-full">
                    <div className="relative w-24 h-32 flex-shrink-0 mx-auto sm:mx-0">
                      <Image
                        src={anime.image}
                        alt={anime.title}
                        fill
                        className="object-cover rounded-lg"
                      />
                    </div>
                    <div className="w-full">
                      <h2 className="text-xl font-bold mb-2 text-center sm:text-left">{anime.title}</h2>
                      <div className="flex flex-wrap gap-2 mb-2 justify-center sm:justify-start">
                        {anime.genres.slice(0, 5).map((genre, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-purple-900 text-purple-200 rounded-full text-xs"
                          >
                            {genre.trim()}
                          </span>
                        ))}
                        {anime.genres.length > 5 && (
                          <span className="px-2 py-1 bg-gray-700 text-gray-300 rounded-full text-xs">
                            +{anime.genres.length - 5}
                          </span>
                        )}
                      </div>
                      <div className="flex flex-wrap gap-2 text-sm text-gray-300 justify-center sm:justify-start">
                        <span>{anime.totalEpisodes === 0 ? "??" : anime.totalEpisodes} episódios</span>
                        <span className="hidden sm:inline">{anime.studio}</span>
                        <span>{anime.year}</span>
                        <span className="flex items-center">
                          <svg className="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          {anime.status}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-center sm:justify-end space-x-2 mt-2 sm:mt-0">
                  {anime.status === 'Em Lançamento' && (
                    <button
                      onClick={() => {
                        const animeUpdateChecker = document.getElementById('anime-update-checker');
                        if (animeUpdateChecker) {
                          animeUpdateChecker.scrollIntoView({ behavior: 'smooth' });
                        }

                        // Abrir o componente de verificação individual para este anime
                        setShowAnimeUpdateModal(true);
                        setSelectedAnimeForUpdate(anime);
                      }}
                      className="p-2 text-purple-400 hover:text-purple-300"
                      title="Verificar episódios faltantes"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </button>
                  )}
                  <button
                      onClick={() => {
                        setEditingAnime(anime)
                        setShowAnimeModal(true)
                      }}
                      className="p-2 text-blue-400 hover:text-blue-300"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                  </button>
                  <button
                    onClick={() => handleDeleteAnime(anime.id)}
                      className="p-2 text-red-400 hover:text-red-300"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setExpandedAnimeId(expandedAnimeId === anime.id ? null : anime.id)}
                      className="p-2 text-gray-400 hover:text-gray-300"
                    >
                      <svg
                        className={`w-5 h-5 transform transition-transform ${
                          expandedAnimeId === anime.id ? 'rotate-180' : ''
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                  </button>
                  </div>
                </div>

                {expandedAnimeId === anime.id && (
                  <div className="mt-6">
                    <div className="flex flex-col sm:flex-row justify-between items-center gap-2 mb-4">
                      <h3 className="text-lg font-semibold">Episódios</h3>
                      <div className="flex flex-col xs:flex-row gap-2 w-full sm:w-auto">
                        <button
                          onClick={() => {
                            setSelectedAnime(anime)
                            setShowBulkUrlModal(true)
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-sm flex items-center justify-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          Editar URLs
                        </button>
                        <button
                          onClick={() => {
                            setSelectedAnime(anime)
                            setEditingEpisode(null)
                            setShowEpisodeModal(true)
                          }}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-sm flex items-center justify-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                          </svg>
                          Adicionar Episódio
                        </button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {anime.episodes.map(episode => (
                        <div key={episode.id} className="bg-gray-700 rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-semibold">Episódio {episode.number}</h4>
                              <p className="text-sm text-gray-300">{episode.title}</p>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => {
                                  setSelectedAnime(anime)
                                  setEditingEpisode(episode)
                                  setShowEpisodeModal(true)
                                }}
                                className="text-blue-400 hover:text-blue-300"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button
                                onClick={() => handleDeleteEpisode(anime.slug, episode.id)}
                                className="text-red-400 hover:text-red-300"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                          <div className="text-xs text-gray-400">
                            <p>Data: {new Date(episode.airDate).toLocaleDateString()}</p>
                            <p className="truncate">Frame: {episode.frame}</p>
                            <p className="truncate">Vídeo: {episode.videoUrl}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))
          )}
          </div>
      </div>

      {/* Anime Modal */}
      {showAnimeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h2 className="text-2xl font-bold mb-6">
                {editingAnime ? 'Editar Anime' : 'Adicionar Anime'}
            </h2>
              <form onSubmit={handleSubmitAnime} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Título</label>
                  <input
                    type="text"
                    name="title"
                    defaultValue={editingAnime?.title}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Descrição</label>
                  <textarea
                    name="description"
                    defaultValue={editingAnime?.description}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">URL da Imagem</label>
                  <input
                    type="url"
                    name="image"
                    defaultValue={editingAnime?.image}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Status</label>
                    <select
                      name="status"
                      defaultValue={editingAnime?.status || "Em Andamento"}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="Em Andamento">Em Andamento</option>
                      <option value="Concluído">Concluído</option>
                      <option value="Não Lançado">Não Lançado</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Total de Episódios</label>
                    <input
                      type="text"
                      name="totalEpisodes"
                      defaultValue={editingAnime?.totalEpisodes === 0 ? "??" : editingAnime?.totalEpisodes || "??"}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Estúdio</label>
                    <input
                      type="text"
                      name="studio"
                      defaultValue={editingAnime?.studio}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Ano</label>
                    <input
                      type="number"
                      name="year"
                      defaultValue={editingAnime?.year || new Date().getFullYear()}
                      min="1900"
                      max={new Date().getFullYear() + 1}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Áudio</label>
                    <select
                      name="audio"
                      defaultValue={editingAnime?.audio || "Legendado"}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="Legendado">Legendado</option>
                      <option value="Dublado">Dublado</option>
                      <option value="Dual Áudio">Dual Áudio</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Dia de Lançamento</label>
                    <select
                      name="releaseDay"
                      defaultValue={editingAnime?.releaseDay || ""}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Selecione...</option>
                      <option value="Domingo">Domingo</option>
                      <option value="Segunda-feira">Segunda-feira</option>
                      <option value="Terça-feira">Terça-feira</option>
                      <option value="Quarta-feira">Quarta-feira</option>
                      <option value="Quinta-feira">Quinta-feira</option>
                      <option value="Sexta-feira">Sexta-feira</option>
                      <option value="Sábado">Sábado</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Gêneros (separados por vírgula)</label>
                  <input
                    type="text"
                    name="genres"
                    defaultValue={editingAnime?.genres?.join(', ')}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    placeholder="Ex: Ação, Aventura, Fantasia"
                  />
                </div>
                <div className="flex justify-end space-x-4 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAnimeModal(false)}
                    className="px-4 py-2 text-gray-300 hover:text-white"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingAnime ? 'Salvar' : 'Adicionar'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Episode Modal */}
      {showEpisodeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full">
            <div className="p-6">
              <h2 className="text-2xl font-bold mb-6">
                {editingEpisode ? 'Editar Episódio' : 'Adicionar Episódio'}
              </h2>
              <form onSubmit={handleSubmitEpisode} data-onsubmit="handleSubmitEpisode" className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Número do Episódio</label>
                  <input
                    type="number"
                    name="number"
                    defaultValue={editingEpisode?.number}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Título</label>
                  <input
                    type="text"
                    name="title"
                    defaultValue={editingEpisode?.title}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Data de Exibição</label>
                  <input
                    type="date"
                    name="airDate"
                    defaultValue={editingEpisode?.airDate?.split('T')[0]}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">URL do Vídeo</label>
                  <input
                    type="text"
                    name="videoUrl"
                    defaultValue={editingEpisode?.videoUrl}
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="URL do vídeo do episódio"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">URL do Frame</label>
                  <input
                    type="text"
                    name="frame"
                    defaultValue={editingEpisode?.frame}
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="URL da imagem do episódio"
                  />
                </div>
                {malEpisodes.length > 0 && !editingEpisode && (
                  <div className="mt-4">
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-gray-300">
                        Episódios do MyAnimeList ({malEpisodes.length} episódios)
                      </label>
                      <button
                        type="button"
                        onClick={handleImportAllEpisodes}
                        disabled={isImportingEpisodes}
                        className="px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                      >
                        {isImportingEpisodes ? 'Importando...' : 'Importar Todos'}
                      </button>
                    </div>
                    {isLoadingEpisodes && (
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-400 mb-1">
                          <span>Carregando episódios...</span>
                          <span>{loadingProgress}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-3">
                          <div
                            className="bg-blue-500 h-3 rounded-full transition-all duration-300 ease-in-out"
                            style={{ width: `${loadingProgress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                    <div className="max-h-48 overflow-y-auto bg-gray-700 rounded-lg p-2 custom-scrollbar">
                      {malEpisodes.map((episode) => (
                        <button
                          key={episode.number}
                          type="button"
                          onClick={() => {
                            const form = document.querySelector('form[data-onsubmit="handleSubmitEpisode"]') as HTMLFormElement
                            const numberInput = form.elements.namedItem('number') as HTMLInputElement
                            const titleInput = form.elements.namedItem('title') as HTMLInputElement
                            const airDateInput = form.elements.namedItem('airDate') as HTMLInputElement
                            const frameInput = form.elements.namedItem('frame') as HTMLInputElement

                            numberInput.value = episode.number.toString()
                            titleInput.value = episode.title
                            airDateInput.value = new Date(episode.airDate).toISOString().split('T')[0]
                            frameInput.value = episode.frame
                          }}
                          className="w-full text-left p-2 hover:bg-gray-600 rounded-lg mb-1"
                        >
                          <span className="font-medium">Episódio {episode.number}:</span> {episode.title}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
                <div className="flex justify-end space-x-4 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowEpisodeModal(false)}
                    className="px-4 py-2 text-gray-300 hover:text-white"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingEpisode ? 'Salvar' : 'Adicionar'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Bulk URL Modal */}
      {showBulkUrlModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full">
            <div className="p-6">
              <h2 className="text-2xl font-bold mb-6">Editar URLs em Massa</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">URL Base</label>
                  <input
                    type="text"
                    value={baseUrl}
                    onChange={(e) => setBaseUrl(e.target.value)}
                    placeholder="Ex: https://lightspeedst.net/s5/mp4_temp/teogonia/"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Formato da URL</label>
                  <input
                    type="text"
                    value={urlFormat}
                    onChange={(e) => setUrlFormat(e.target.value)}
                    placeholder="Ex: {episode}/480p.mp4"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-sm text-gray-400 mt-1">
                    Use {"{episode}"} para o número do episódio
                  </p>
                </div>
                <div className="flex justify-end space-x-4 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowBulkUrlModal(false)}
                    className="px-4 py-2 text-gray-300 hover:text-white"
                  >
                    Cancelar
                  </button>
                  <button
                    type="button"
                    onClick={handleBulkUpdateUrls}
                    disabled={isUpdatingUrls || !baseUrl}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpdatingUrls ? 'Atualizando...' : 'Atualizar URLs'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Anime Update Modal */}
      {showAnimeUpdateModal && selectedAnimeForUpdate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">Verificar Episódios Faltantes</h2>
                <button
                  onClick={() => setShowAnimeUpdateModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-200 mb-2">{selectedAnimeForUpdate.title}</h3>
                <p className="text-gray-400">
                  Status: <span className="text-purple-400">{selectedAnimeForUpdate.status}</span> •
                  Episódios: <span className="text-purple-400">{selectedAnimeForUpdate.episodes.length}</span>
                </p>
              </div>

              <AnimeUpdateChecker
                animeId={selectedAnimeForUpdate.id}
                onUpdateComplete={() => {
                  fetchAnimes();
                  setShowAnimeUpdateModal(false);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Import Anime Modal */}
      {showImportAnimeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">Importar Anime Completo</h2>
                <button
                  onClick={() => {
                    setShowImportAnimeModal(false);
                    setManualImportMode(false);
                  }}
                  className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {!manualImportMode ? (
                // Modo de importação automática
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      URL do Anime (Fonte Externa)
                    </label>
                    <input
                      type="text"
                      value={importAnimeUrl}
                      onChange={(e) => setImportAnimeUrl(e.target.value)}
                      placeholder="https://source.domain/animes/nome-do-anime-todos-os-episodios"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="mt-2 text-sm text-gray-400">
                      Cole a URL da página do anime na fonte externa. O sistema irá importar todas as informações e episódios automaticamente.
                    </p>
                  </div>

                  {/* Barra de Progresso */}
                  <div className="mb-6">
                    {importId ? (
                      <>
                        <p className="text-green-400 mb-2">ID de importação: {importId}</p>
                        <ImportProgressBar
                          importId={importId}
                          onComplete={handleImportComplete}
                        />
                      </>
                    ) : (
                      <p className="text-gray-400">Aguardando início da importação...</p>
                    )}
                  </div>

                  <div className="flex justify-between space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={() => setManualImportMode(true)}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                      disabled={!!importId}
                    >
                      Modo Manual
                    </button>

                    <div className="flex space-x-4">
                      <button
                        type="button"
                        onClick={() => {
                          if (!importId) {
                            setShowImportAnimeModal(false);
                          }
                        }}
                        className="px-4 py-2 text-gray-300 hover:text-white"
                        disabled={!!importId}
                      >
                        {importId ? 'Aguarde...' : 'Cancelar'}
                      </button>
                      <button
                        type="button"
                        onClick={handleImportAnime}
                        disabled={isImportingAnime || !importAnimeUrl || !!importId}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                      >
                        {isImportingAnime ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Importando...
                          </>
                        ) : (
                          <>
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                            </svg>
                            Importar Anime
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                // Modo de importação manual
                <div className="space-y-6">
                  <div className="bg-blue-900/30 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold text-blue-300 mb-2">Modo de Importação Manual</h3>
                    <p className="text-sm text-blue-200">
                      Preencha os dados do anime manualmente e adicione os episódios um a um.
                      Este modo é útil quando a importação automática falha.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Título do Anime
                      </label>
                      <input
                        type="text"
                        value={animeData.title}
                        onChange={(e) => setAnimeData({...animeData, title: e.target.value})}
                        placeholder="Título do anime"
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        URL da Imagem
                      </label>
                      <input
                        type="text"
                        value={animeData.image}
                        onChange={(e) => setAnimeData({...animeData, image: e.target.value})}
                        placeholder="https://exemplo.com/imagem.jpg"
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Descrição
                    </label>
                    <textarea
                      value={animeData.description}
                      onChange={(e) => setAnimeData({...animeData, description: e.target.value})}
                      placeholder="Descrição do anime"
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-lg font-semibold">Episódios ({animeData.episodes.length})</h3>
                      <button
                        onClick={handleAddEpisode}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-sm flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Adicionar Episódio
                      </button>
                    </div>

                    {animeData.episodes.length > 0 ? (
                      <div className="bg-gray-700 rounded-lg p-2 max-h-60 overflow-y-auto">
                        <table className="w-full text-sm">
                          <thead className="text-xs text-gray-400 uppercase">
                            <tr>
                              <th className="px-2 py-1 text-left">Episódio</th>
                              <th className="px-2 py-1 text-left">URL</th>
                              <th className="px-2 py-1 w-16"></th>
                            </tr>
                          </thead>
                          <tbody>
                            {animeData.episodes.map((episode, index) => (
                              <tr key={index} className="border-t border-gray-600">
                                <td className="px-2 py-2">{episode.number}</td>
                                <td className="px-2 py-2 truncate max-w-xs" title={episode.videoUrl}>
                                  {episode.videoUrl.substring(0, 40)}...
                                </td>
                                <td className="px-2 py-2 text-right">
                                  <button
                                    onClick={() => handleRemoveEpisode(index)}
                                    className="text-red-400 hover:text-red-300"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="bg-gray-700 rounded-lg p-4 text-center text-gray-400">
                        Nenhum episódio adicionado. Clique em "Adicionar Episódio" para começar.
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={() => setManualImportMode(false)}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                    >
                      Voltar ao Modo Automático
                    </button>

                    <div className="flex space-x-4">
                      <button
                        type="button"
                        onClick={() => {
                          setShowImportAnimeModal(false);
                          setManualImportMode(false);
                        }}
                        className="px-4 py-2 text-gray-300 hover:text-white"
                      >
                        Cancelar
                      </button>
                      <button
                        type="button"
                        onClick={handleSaveManualAnime}
                        disabled={isImportingAnime || !animeData.title || animeData.episodes.length === 0}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                      >
                        {isImportingAnime ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Salvando...
                          </>
                        ) : (
                          <>
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            Salvar Anime
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

    </div>
  )
}