'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import { toast } from 'react-hot-toast'

export default function ProfileContent() {
  const { data: session, update } = useSession()
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState('')
  const [username, setUsername] = useState(session?.user?.name || '')
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null)

  // Inicializa o nome de usuário com o valor da sessão
  useEffect(() => {
    if (session?.user?.name) {
      setUsername(session.user.name)
    }
  }, [session?.user?.name])

  // Atualiza a sessão periodicamente
  useEffect(() => {
    const interval = setInterval(() => {
      update()
    }, 1000 * 60) // Atualiza a cada minuto

    return () => clearInterval(interval)
  }, [update])

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    try {
      setIsUpdating(true)
      setError('')

      const formData = new FormData()
      formData.append('avatar', file)
      if (username) {
        formData.append('name', username)
      }

      const response = await fetch('/api/user/update', {
        method: 'PUT',
        body: formData,
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao atualizar avatar')
      }

      // Atualiza a sessão com os novos dados
      await update({
        ...session,
        user: {
          ...session?.user,
          name: data.name,
          image: data.image,
        },
      })

      toast.success('Avatar atualizado com sucesso!')
    } catch (error) {
      console.error('Erro ao atualizar avatar:', error)
      setError(error instanceof Error ? error.message : 'Erro ao atualizar avatar')
      toast.error('Erro ao atualizar avatar')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value
    setUsername(newName)
    setError('')

    // Cancela o timeout anterior se existir
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // Cria um novo timeout para atualizar o nome
    const newTimeoutId = setTimeout(async () => {
      if (newName.trim().length < 2) {
        if (newName.trim().length > 0) {
          setError('O nome deve ter pelo menos 2 caracteres')
        }
        return
      }

      try {
        setIsUpdating(true)
        const formData = new FormData()
        formData.append('name', newName.trim())

        const response = await fetch('/api/user/update', {
          method: 'PUT',
          body: formData,
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Erro ao atualizar nome')
        }

        // Atualiza a sessão com os novos dados
        await update({
          ...session,
          user: {
            ...session?.user,
            name: data.name,
          },
        })

        // Atualiza o estado local com o novo nome
        setUsername(data.name)
        toast.success('Nome atualizado com sucesso!')
      } catch (error) {
        console.error('Erro ao atualizar nome:', error)
        setError(error instanceof Error ? error.message : 'Erro ao atualizar nome')
        toast.error('Erro ao atualizar nome')
      } finally {
        setIsUpdating(false)
      }
    }, 1000) // Espera 1 segundo após o usuário parar de digitar

    setTimeoutId(newTimeoutId)
  }

  if (!session?.user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Faça login para ver seu perfil</p>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex flex-col items-center space-y-4">
          <div className="relative w-32 h-32">
            {session.user.image ? (
              <Image
                src={session.user.image}
                alt="Avatar"
                fill
                className="rounded-full object-cover"
                unoptimized
              />
            ) : (
              <div className="w-full h-full rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center">
                <span className="text-4xl text-white">
                  {username?.[0]?.toUpperCase() || '?'}
                </span>
              </div>
            )}
          </div>

          <div className="w-full space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nome de Usuário
              </label>
              <input
                type="text"
                value={username}
                onChange={handleNameChange}
                disabled={isUpdating}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder={session.user.name || "Seu nome de usuário"}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                E-mail
              </label>
              <input
                type="email"
                value={session.user.email || ''}
                disabled
                className="w-full px-4 py-2 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-400"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Avatar
              </label>
              <div className="flex items-center space-x-4">
                <label className={`cursor-pointer inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors ${isUpdating ? 'opacity-50 cursor-not-allowed' : ''}`}>
                  <span className="mr-2">{isUpdating ? 'Enviando...' : 'Alterar Avatar'}</span>
                  <input
                    type="file"
                    accept="image/jpeg,image/png,image/gif,image/webp"
                    onChange={handleFileChange}
                    className="hidden"
                    disabled={isUpdating}
                  />
                </label>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  JPG, PNG, GIF ou WEBP (máx. 5MB)
                </p>
              </div>
            </div>

            {error && (
              <p className="text-red-500 text-sm mt-2">{error}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 