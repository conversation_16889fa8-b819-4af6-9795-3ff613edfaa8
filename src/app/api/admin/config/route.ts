import { NextResponse } from 'next/server'
import { checkAdminConfig } from '@/lib/adminAuth'

/**
 * API para verificar o status da configuração de admin
 * Útil para debugging e verificação de segurança
 */
export async function GET() {
  try {
    const config = checkAdminConfig()
    
    return NextResponse.json({
      isConfigured: config.isConfigured,
      message: config.message,
      hasEnvironmentVars: {
        ADMIN_EMAIL: !!process.env.ADMIN_EMAIL,
        ADMIN_PASSWORD: !!process.env.ADMIN_PASSWORD,
        JWT_SECRET: !!process.env.JWT_SECRET
      },
      // Não retornar os valores reais por segurança
      emailConfigured: process.env.ADMIN_EMAIL ? 
        `${process.env.ADMIN_EMAIL.substring(0, 3)}***@${process.env.ADMIN_EMAIL.split('@')[1]}` : 
        'Não configurado'
    })
  } catch (error) {
    console.error('Erro ao verificar configuração de admin:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
