import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    const anime = await prisma.anime.findUnique({
      where: {
        slug,
      },
      include: {
        episodes: {
          orderBy: {
            number: 'asc',
          },
        },
      },
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }
    return NextResponse.json(anime.episodes)
  } catch (error) {
    console.error('Error fetching episodes:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar episódios' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    const data = await request.json()

    // Verifica se o anime existe
    const anime = await prisma.anime.findUnique({
      where: { slug },
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Formata a data para o formato ISO
    const formattedData = {
      ...data,
      airDate: new Date(data.airDate).toISOString(),
      animeId: anime.id,
    }

    // Cria o episódio
    const episode = await prisma.episode.create({
      data: formattedData,
    })

    return NextResponse.json(episode)
  } catch (error) {
    console.error('Error creating episode:', error)
    return NextResponse.json(
      { error: 'Erro ao criar episódio' },
      { status: 500 }
    )
  }
}