generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                String              @id @default(cuid())
  email             String?             @unique
  password          String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  role              String              @default("user")
  name              String?
  emailVerified     DateTime?
  image             String?
  resetToken        String?
  resetTokenExpiry  DateTime?
  accounts          Account[]
  comments          Comment[]
  favorites         Favorite[]
  sessions          Session[]
  votes             Vote[]
  watchedEpisodes   WatchedEpisode[]
  watchProgress     WatchProgress[]
  animeSubscriptions AnimeSubscription[]
  pushSubscription  PushSubscription?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Anime {
  id                String              @id @default(cuid())
  title             String
  description       String
  image             String?
  status            String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  genres            String[]
  studio            String?
  totalEpisodes     Int?
  year              Int?
  slug              String?             @unique @default("")
  audio             String?             // Tipo de áudio (Legendado, Dublado, etc)
  releaseDay        String?             // Dia da semana de lançamento
  malId             Int?                // ID do anime no MyAnimeList
  comments          Comment[]
  episodes          Episode[]
  favorites         Favorite[]
  votes             Vote[]
  animeSubscriptions AnimeSubscription[]
}

model Episode {
  id                String           @id @default(cuid())
  number            Int
  title             String
  originalTitle     String?          // Título original (inglês/japonês)
  translatedTitle   String?          // Título traduzido para português
  detectedLanguage  String?          // Idioma detectado do título original
  lastTitleUpdate   DateTime?        // Última atualização do título
  airDate           DateTime?
  frame             String?
  videoUrl          String?
  animeId           String
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  sourceType        String?
  comments          Comment[]
  anime             Anime            @relation(fields: [animeId], references: [id], onDelete: Cascade)
  watchedBy         WatchedEpisode[]
  watchProgress     WatchProgress[]
}

model Favorite {
  id        String   @id @default(cuid())
  userId    String
  animeId   String
  createdAt DateTime @default(now())
  anime     Anime    @relation(fields: [animeId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, animeId])
}

model Vote {
  id        String   @id @default(cuid())
  userId    String
  animeId   String
  isLike    Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  anime     Anime    @relation(fields: [animeId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, animeId])
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  userId    String
  animeId   String
  episodeId String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  anime     Anime    @relation(fields: [animeId], references: [id], onDelete: Cascade)
  episode   Episode? @relation(fields: [episodeId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model WatchedEpisode {
  id        String   @id @default(cuid())
  userId    String
  episodeId String
  watchedAt DateTime @default(now())
  episode   Episode  @relation(fields: [episodeId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, episodeId])
}

model WatchProgress {
  id           String   @id @default(cuid())
  userId       String
  episodeId    String
  currentTime  Float    @default(0) // Tempo atual em segundos
  duration     Float    @default(0) // Duração total em segundos
  percentage   Float    @default(0) // Porcentagem assistida (0-100)
  updatedAt    DateTime @default(now()) @updatedAt
  episode      Episode  @relation(fields: [episodeId], references: [id], onDelete: Cascade)
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, episodeId])
}

model AnimeSubscription {
  id        String   @id @default(cuid())
  userId    String
  animeId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  anime     Anime    @relation(fields: [animeId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, animeId])
}

model PushSubscription {
  id             String   @id @default(cuid())
  userId         String   @unique
  endpoint       String   @db.Text
  expirationTime String?
  p256dh         String   @db.Text
  auth           String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model EpisodeTitleTranslation {
  id                String   @id @default(cuid())
  originalTitle     String   // Título original
  translatedTitle   String   // Título traduzido
  sourceLanguage    String   // Idioma de origem (en, ja, etc.)
  targetLanguage    String   // Idioma de destino (pt, etc.)
  isManual          Boolean  @default(false) // Se foi traduzido manualmente
  confidence        Float?   // Confiança da tradução (0-1)
  translationSource String?  // Fonte da tradução (google, manual, etc.)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([originalTitle, sourceLanguage, targetLanguage])
  @@index([originalTitle])
}
