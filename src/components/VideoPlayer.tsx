'use client'

import { useRef, useState, useEffect, useCallback, SyntheticEvent } from 'react'
import { useSession } from 'next-auth/react'

interface VideoPlayerProps {
  src: string
  poster?: string
  episodeId: string
  animeId: string
  isBloggerVideo?: boolean
}

export default function VideoPlayer({ src, poster, episodeId, animeId, isBloggerVideo = false }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [isLoading, setIsLoading] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [touchStartX, setTouchStartX] = useState(0)
  const [touchStartY, setTouchStartY] = useState(0)
  const [showVolumeIndicator, setShowVolumeIndicator] = useState(false)
  const [showSeekIndicator, setShowSeekIndicator] = useState(false)
  const [volumeChange, setVolumeChange] = useState(0)
  const [seekChange, setSeekChange] = useState(0)
  const controlsTimeoutRef = useRef<NodeJS.Timeout>()
  const { data: session } = useSession()
  const saveProgressTimeoutRef = useRef<NodeJS.Timeout>()
  const lastSaveTime = useRef(0)
  const SAVE_INTERVAL = 5000 // Save every 5 seconds

  const saveProgress = useCallback(async (time: number) => {
    if (!session?.user) return

    if (saveProgressTimeoutRef.current) {
      clearTimeout(saveProgressTimeoutRef.current)
    }

    saveProgressTimeoutRef.current = setTimeout(async () => {
      const currentTime = Date.now()
      if (currentTime - lastSaveTime.current < SAVE_INTERVAL) return

      try {
        await fetch('/api/watch-progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            episodeId,
            animeId,
            progress: time,
          }),
        })
        lastSaveTime.current = currentTime
      } catch (error) {
        console.error('Error saving watch progress:', error)
      }
    }, 5000) // Debounce save requests
  }, [episodeId, animeId, session?.user])

  const handleTimeUpdate = useCallback(() => {
    const video = videoRef.current
    if (!video || isDragging) return

    setCurrentTime(video.currentTime)
    localStorage.setItem(`video-${episodeId}`, video.currentTime.toString())
    saveProgress(video.currentTime)
  }, [episodeId, isDragging, saveProgress])

  // Load saved state when component mounts
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    // Load saved position
    const savedTime = localStorage.getItem(`video-${episodeId}`)
    if (savedTime) {
      const time = parseFloat(savedTime)
      video.currentTime = time
      setCurrentTime(time)
    }

    // Load saved volume
    const savedVolume = localStorage.getItem(`video-volume-${episodeId}`)
    if (savedVolume) {
      const vol = parseFloat(savedVolume)
      video.volume = vol
      setVolume(vol)
    }

    // Load muted state
    const savedMuted = localStorage.getItem(`video-muted-${episodeId}`)
    if (savedMuted) {
      const muted = savedMuted === 'true'
      video.muted = muted
      setIsMuted(muted)
    }

    // Load watch progress from server if user is logged in
    if (session?.user) {
      fetch(`/api/watch-progress?episodeId=${episodeId}`)
        .then(res => res.json())
        .then(data => {
          if (data.progress > 0) {
            video.currentTime = data.progress
            setCurrentTime(data.progress)
          }
        })
        .catch(error => console.error('Error loading watch progress:', error))
    }

    // Add fullscreen change listener
    const handleFullscreenChange = () => {
      setIsFullscreen(document.fullscreenElement !== null)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [episodeId, session])

  // Save progress periodically
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      if (isDragging) return
      setCurrentTime(video.currentTime)
      localStorage.setItem(`video-${episodeId}`, video.currentTime.toString())
      saveProgress(video.currentTime)
    }

    video.addEventListener('timeupdate', handleTimeUpdate)
    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      if (saveProgressTimeoutRef.current) {
        clearTimeout(saveProgressTimeoutRef.current)
      }
    }
  }, [episodeId, isDragging, saveProgress])

  // Esconde os controles após inatividade
  useEffect(() => {
    const handleMouseMove = () => {
      setShowControls(true)
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 3000)
    }

    document.addEventListener('mousemove', handleMouseMove)
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [])

  const handleLoadedMetadata = () => {
    const video = videoRef.current
    if (!video) return

    setDuration(video.duration)
    setIsLoading(false)

    // Carrega a posição salva quando o vídeo é carregado
    const savedTime = localStorage.getItem(`video-${episodeId}`)
    if (savedTime) {
      const time = parseFloat(savedTime)
      video.currentTime = time
      setCurrentTime(time)
    }
  }

  const handlePlayPause = () => {
    const video = videoRef.current
    if (!video) return

    if (video.paused) {
      video.play()
      setIsPlaying(true)
    } else {
      video.pause()
      setIsPlaying(false)
    }
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current
    if (!video) return

    const newVolume = parseFloat(e.target.value)
    video.volume = newVolume
    setVolume(newVolume)
    localStorage.setItem(`video-volume-${episodeId}`, newVolume.toString())
  }

  const handleMute = () => {
    const video = videoRef.current
    if (!video) return

    video.muted = !isMuted
    setIsMuted(!isMuted)
    localStorage.setItem(`video-muted-${episodeId}`, (!isMuted).toString())
  }

  const handleSeekStart = () => {
    setIsDragging(true)
  }

  const handleSeekEnd = () => {
    setIsDragging(false)
    const video = videoRef.current
    if (video) {
      video.currentTime = currentTime
      localStorage.setItem(`video-${episodeId}`, currentTime.toString())
    }
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = parseFloat(e.target.value)
    setCurrentTime(time)
  }

  const handleFullscreen = () => {
    if (!containerRef.current) return

    if (!document.fullscreenElement) {
      containerRef.current.requestFullscreen().catch(err => {
        console.error(`Erro ao entrar em tela cheia: ${err.message}`)
      })
    } else {
      document.exitFullscreen()
    }
  }

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX)
    setTouchStartY(e.touches[0].clientY)
    setShowVolumeIndicator(false)
    setShowSeekIndicator(false)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!videoRef.current) return

    const touchX = e.touches[0].clientX
    const touchY = e.touches[0].clientY
    const deltaX = touchX - touchStartX
    const deltaY = touchY - touchStartY

    // Se o movimento for mais horizontal que vertical
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // Ajusta o tempo do vídeo
      const seekTime = (deltaX / window.innerWidth) * duration
      const newTime = Math.max(0, Math.min(duration, currentTime + seekTime))
      setCurrentTime(newTime)
      videoRef.current.currentTime = newTime
      setShowSeekIndicator(true)
      setSeekChange(seekTime)
    } else {
      // Ajusta o volume
      const volumeChange = -deltaY / window.innerHeight
      const newVolume = Math.max(0, Math.min(1, volume + volumeChange))
      setVolume(newVolume)
      videoRef.current.volume = newVolume
      setShowVolumeIndicator(true)
      setVolumeChange(volumeChange)
    }
  }

  const handleTouchEnd = () => {
    setShowVolumeIndicator(false)
    setShowSeekIndicator(false)
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    const video = videoRef.current
    if (!video) return

    switch (e.key) {
      case ' ':
      case 'k':
        handlePlayPause()
        break
      case 'ArrowLeft':
        video.currentTime = Math.max(0, video.currentTime - 10)
        break
      case 'ArrowRight':
        video.currentTime = Math.min(video.duration, video.currentTime + 10)
        break
      case 'ArrowUp':
        video.volume = Math.min(1, video.volume + 0.1)
        setVolume(video.volume)
        break
      case 'ArrowDown':
        video.volume = Math.max(0, video.volume - 0.1)
        setVolume(video.volume)
        break
      case 'm':
        handleMute()
        break
      case 'f':
        handleFullscreen()
        break
    }
  }

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  return (
    <div
      ref={containerRef}
      className={`video-player-container ${isFullscreen ? 'fullscreen' : ''}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {isLoading && (
        <div className="loading-spinner" />
      )}

      {isBloggerVideo ? (
        // Render iframe for Blogger videos
        <iframe
          ref={iframeRef}
          src={src}
          className="w-full h-full"
          allowFullScreen
          frameBorder="0"
          scrolling="no"
          title="Anime Episode"
          onLoad={() => setIsLoading(false)}
        />
      ) : (
        // Render video element for direct video URLs
        <video
          ref={videoRef}
          src={src}
          poster={poster}
          className="w-full h-full"
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onClick={handlePlayPause}
          playsInline
        >
          Seu navegador não suporta o elemento de vídeo.
        </video>
      )}

      {/* Indicadores de gestos - only for direct videos */}
      {!isBloggerVideo && showVolumeIndicator && (
        <div className="gesture-indicator volume-indicator">
          Volume: {Math.round((volume + volumeChange) * 100)}%
        </div>
      )}
      {!isBloggerVideo && showSeekIndicator && (
        <div className="gesture-indicator seek-indicator">
          {seekChange > 0 ? '+' : ''}{Math.round(seekChange)}s
        </div>
      )}

      {/* Controles personalizados - only for direct videos */}
      {!isBloggerVideo && (
        <div className={`controls absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
        {/* Barra de progresso */}
        <div className="flex items-center gap-2 mb-2">
          <input
            type="range"
            min={0}
            max={duration}
            value={currentTime}
            onChange={handleSeek}
            onMouseDown={handleSeekStart}
            onMouseUp={handleSeekEnd}
            onTouchStart={handleSeekStart}
            onTouchEnd={handleSeekEnd}
            className="flex-1 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-3 [&::-webkit-slider-thumb]:h-3 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-500"
          />
          <span className="text-white text-sm">
            {formatTime(currentTime)} / {formatTime(duration)}
          </span>
        </div>

        {/* Botões de controle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handlePlayPause}
              className="text-white hover:text-blue-500 transition-colors"
            >
              {isPlaying ? (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
            </button>

            <div className="flex items-center gap-2">
              <button
                onClick={handleMute}
                className="text-white hover:text-blue-500 transition-colors"
              >
                {isMuted ? (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                  </svg>
                )}
              </button>
              <input
                type="range"
                min={0}
                max={1}
                step={0.1}
                value={volume}
                onChange={handleVolumeChange}
                className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-3 [&::-webkit-slider-thumb]:h-3 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-500"
              />
            </div>
          </div>

          <button
            onClick={handleFullscreen}
            className="text-white hover:text-blue-500 transition-colors"
          >
            {isFullscreen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9V4.5M9 9H4.5M15 9h4.5M15 9V4.5M15 15v4.5M15 15h4.5M9 15H4.5M9 15v4.5" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
              </svg>
            )}
          </button>
        </div>
      </div>
      )}
    </div>
  )
}