'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Bars3Icon, XMarkIcon, PlayCircleIcon, HomeIcon, FilmIcon, HeartIcon } from '@heroicons/react/24/outline'
import { useSession, signOut } from 'next-auth/react'
import Logo from './Logo'
import Image from 'next/image'
import { createPortal } from 'react-dom'
import { usePathname } from 'next/navigation'

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 })
  const [mounted, setMounted] = useState(false)
  const { data: session } = useSession()
  const pathname = usePathname()

  useEffect(() => {
    setMounted(true)
  }, [])

  const navigation = [
    { name: '<PERSON><PERSON><PERSON>', href: '/', icon: <HomeIcon className="w-5 h-5" /> },
    { name: 'Animes', href: '/animes', icon: <FilmIcon className="w-5 h-5" /> },
    { name: 'Favoritos', href: '/favoritos', icon: <HeartIcon className="w-5 h-5" /> },
  ]

  // Item de navegação "Continue Assistindo" separado para destacá-lo
  const continueWatchingItem = {
    name: 'Continue Assistindo',
    href: '/continue-assistindo',
    icon: <PlayCircleIcon className="w-5 h-5" />
  }

  const getImageUrl = (imagePath: string | null | undefined) => {
    if (!imagePath) return null
    if (imagePath.startsWith('http')) return imagePath
    return `${window.location.origin}${imagePath}`
  }

  const handleDropdownClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    const button = event.currentTarget
    const rect = button.getBoundingClientRect()
    setDropdownPosition({
      top: rect.bottom + window.scrollY,
      left: rect.right - 192 // 192px is the width of the dropdown (w-48)
    })
    setIsDropdownOpen(!isDropdownOpen)
  }

  return (
    <nav className="bg-white/5 backdrop-blur-md border-b border-white/10 relative z-[9999]">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Logo />

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`flex items-center space-x-1 ${
                  pathname === item.href
                    ? 'text-purple-400'
                    : 'text-gray-300 hover:text-white'
                } transition-colors`}
              >
                {item.icon}
                <span>{item.name}</span>
              </Link>
            ))}

            {/* Botão Continue Assistindo destacado */}
            {session && (
              <Link
                href={continueWatchingItem.href}
                className={`flex items-center space-x-1 px-3 py-1.5 rounded-full ${
                  pathname === continueWatchingItem.href
                    ? 'bg-purple-600 text-white'
                    : 'bg-purple-600/20 text-purple-400 hover:bg-purple-600/30'
                } transition-colors`}
              >
                {continueWatchingItem.icon}
                <span>{continueWatchingItem.name}</span>
              </Link>
            )}
            {session ? (
              <div className="relative">
                <button
                  onClick={handleDropdownClick}
                  className="flex items-center space-x-2 group"
                >
                  <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-purple-500 group-hover:border-purple-400 transition-colors bg-white/10 flex items-center justify-center">
                    {session.user?.image ? (
                      <Image
                        src={getImageUrl(session.user.image) || ''}
                        alt="Avatar"
                        width={32}
                        height={32}
                        className="w-full h-full object-cover"
                        unoptimized
                      />
                    ) : (
                      <span className="text-white text-sm">
                        {session.user?.name?.[0]?.toUpperCase() || '?'}
                      </span>
                    )}
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="text-white font-medium group-hover:text-purple-400 transition-colors">
                      {session.user?.name || session.user?.email}
                    </span>
                  </div>
                </button>
                {mounted && isDropdownOpen && createPortal(
                  <div
                    className="fixed w-48 bg-white/5 backdrop-blur-md rounded-md shadow-lg py-1 z-[9999]"
                    style={{
                      top: `${dropdownPosition.top}px`,
                      left: `${dropdownPosition.left}px`
                    }}
                  >
                    <Link
                      href="/perfil"
                      className="block px-4 py-2 text-sm text-gray-300 hover:bg-purple-500 hover:text-white"
                    >
                      Meu Perfil
                    </Link>
                    <button
                      onClick={() => signOut()}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-purple-500 hover:text-white"
                    >
                      Sair
                    </button>
                  </div>,
                  document.body
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  href="/login"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Entrar
                </Link>
                <Link
                  href="/register"
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Criar Conta
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-300 hover:text-white transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 space-y-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`flex items-center space-x-2 px-2 py-2 ${
                  pathname === item.href
                    ? 'text-purple-400'
                    : 'text-gray-300 hover:text-white'
                } transition-colors`}
              >
                {item.icon}
                <span>{item.name}</span>
              </Link>
            ))}

            {/* Botão Continue Assistindo para mobile */}
            {session && (
              <Link
                href={continueWatchingItem.href}
                className={`flex items-center space-x-2 px-2 py-2 ${
                  pathname === continueWatchingItem.href
                    ? 'bg-purple-600 text-white rounded-lg'
                    : 'bg-purple-600/20 text-purple-400 hover:bg-purple-600/30 rounded-lg'
                } transition-colors`}
              >
                {continueWatchingItem.icon}
                <span>{continueWatchingItem.name}</span>
              </Link>
            )}
            {session ? (
              <div className="space-y-4">
                <div className="flex items-center space-x-3 px-4">
                  <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-purple-500 bg-white/10 flex items-center justify-center">
                    {session.user?.image ? (
                      <Image
                        src={getImageUrl(session.user.image) || ''}
                        alt="Avatar"
                        width={40}
                        height={40}
                        className="w-full h-full object-cover"
                        unoptimized
                      />
                    ) : (
                      <span className="text-white text-lg">
                        {session.user?.name?.[0]?.toUpperCase() || '?'}
                      </span>
                    )}
                  </div>
                  <div>
                    <div className="text-white font-medium">
                      {session.user?.name || session.user?.email}
                    </div>
                  </div>
                </div>
                <Link
                  href="/perfil"
                  className="block text-gray-300 hover:text-white transition-colors"
                >
                  Meu Perfil
                </Link>
                <button
                  onClick={() => signOut()}
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Sair
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <Link
                  href="/login"
                  className="block text-gray-300 hover:text-white transition-colors"
                >
                  Entrar
                </Link>
                <Link
                  href="/register"
                  className="block bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center"
                >
                  Criar Conta
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </nav>
  )
}