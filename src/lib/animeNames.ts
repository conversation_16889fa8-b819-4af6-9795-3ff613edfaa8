const animeCharacters = [
  // <PERSON><PERSON><PERSON>
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  // One <PERSON>
  '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
  // <PERSON> Ball
  'Goku', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  // Attack on Titan
  'Eren', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  // My Hero Academia
  'Deku', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON> Might', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  // <PERSON> Note
  'Light', '<PERSON>', '<PERSON><PERSON>', '<PERSON>yu<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  // Fullmetal Alchemist
  '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  // <PERSON> Slayer
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  // <PERSON>jutsu <PERSON>sen
  '<PERSON>ji', '<PERSON><PERSON>', '<PERSON>bara', '<PERSON>jo', 'Su<PERSON>na', '<PERSON>mi', 'Maki', 'Toge',
  // <PERSON> Ghoul
  '<PERSON>ki', 'Touka', 'Hide', 'Rize', 'Tsukiyama', 'Uta', 'Yomo', 'Eto'
]

export function generateAnimeUsername(): string {
  const randomIndex = Math.floor(Math.random() * animeCharacters.length)
  const randomNumber = Math.floor(Math.random() * 1000)
  return `${animeCharacters[randomIndex]}${randomNumber}`
} 