import { NextResponse } from 'next/server'
import { generateToken } from '@/lib/jwt'

interface LoginRequest {
  email: string
  password: string
}

// Simulação de banco de dados
const ADMIN_USER = {
  id: '1',
  email: '<EMAIL>',
  password: 'admin123', // Em produção, use hash de senha
  role: 'admin' as const,
}

export async function POST(request: Request) {
  try {
    const body: LoginRequest = await request.json()

    // Validação básica
    if (!body.email || !body.password) {
      return NextResponse.json(
        { error: 'Email e senha são obrigatórios' },
        { status: 400 }
      )
    }

    // Verifica credenciais
    if (
      body.email !== ADMIN_USER.email ||
      body.password !== ADMIN_USER.password
    ) {
      return NextResponse.json(
        { error: 'Email ou senha inválidos' },
        { status: 401 }
      )
    }

    // Gera token JWT
    const token = generateToken({
      id: ADMIN_USER.id,
      email: ADMIN_USER.email,
      role: ADMIN_USER.role,
    })

    // Retorna token
    return NextResponse.json(
      { token },
      {
        status: 200,
        headers: {
          'Set-Cookie': `admin_token=${token}; Path=/; HttpOnly; SameSite=Strict; Max-Age=86400`,
        },
      }
    )
  } catch (error) {
    console.error('Erro no login:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
} 