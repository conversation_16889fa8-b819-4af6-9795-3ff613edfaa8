const ffmpeg = require('fluent-ffmpeg');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configura o caminho do ffmpeg
ffmpeg.setFfmpegPath(ffmpegInstaller.path);

console.log('Caminho do ffmpeg:', ffmpegInstaller.path);

const videoUrl = 'https://lightspeedst.net/s2/mp4/one-piece/sd/1.mp4';
const tempVideoPath = path.join(process.cwd(), 'public', 'temp', 'video.mp4');
const outputPath = path.join(process.cwd(), 'public', 'frames', 'test-frame.jpg');

// Cria os diretórios necessários
const tempDir = path.dirname(tempVideoPath);
const framesDir = path.dirname(outputPath);
if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });
if (!fs.existsSync(framesDir)) fs.mkdirSync(framesDir, { recursive: true });

async function downloadVideo() {
  console.log('Baixando vídeo...');
  const response = await fetch(videoUrl);
  const buffer = await response.buffer();
  fs.writeFileSync(tempVideoPath, buffer);
  console.log('Vídeo baixado com sucesso!');
}

async function extractFrame() {
  console.log('Iniciando extração de frame...');
  console.log('Caminho do vídeo:', tempVideoPath);
  console.log('Caminho de saída:', outputPath);

  return new Promise((resolve, reject) => {
    ffmpeg()
      .input(tempVideoPath)
      .seekInput('00:00:01')
      .frames(1)
      .output(outputPath)
      .size('1280x720')
      .on('start', (commandLine) => {
        console.log('Comando iniciado:', commandLine);
      })
      .on('progress', (progress) => {
        console.log('Progresso:', progress);
      })
      .on('end', () => {
        console.log('Frame extraído com sucesso!');
        resolve();
      })
      .on('error', (err) => {
        console.error('Erro:', err);
        reject(err);
      })
      .run();
  });
}

async function main() {
  try {
    await downloadVideo();
    await extractFrame();
    // Limpa o arquivo temporário
    fs.unlinkSync(tempVideoPath);
  } catch (error) {
    console.error('Erro no processo:', error);
  }
}

main(); 