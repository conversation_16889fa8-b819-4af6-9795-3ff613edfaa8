import ffmpeg from 'fluent-ffmpeg'
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg'
import fetch from 'node-fetch'
import fs from 'fs'
import path from 'path'

// Configura o caminho do ffmpeg
ffmpeg.setFfmpegPath(ffmpegInstaller.path)

export async function extractFrame(videoUrl: string, outputPath: string): Promise<string> {
  console.log('Iniciando extração de frame para:', videoUrl)
  
  // Cria os diretórios necessários
  const tempDir = path.join(process.cwd(), 'public', 'temp')
  const outputDir = path.dirname(outputPath)
  if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true })
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true })

  // Gera um nome único para o arquivo temporário
  const tempVideoPath = path.join(tempDir, `temp-${Date.now()}.mp4`)

  try {
    // Baixa o vídeo
    console.log('Baixando vídeo...')
    const response = await fetch(videoUrl)
    const buffer = await response.buffer()
    fs.writeFileSync(tempVideoPath, buffer)
    console.log('Vídeo baixado com sucesso!')

    // Extrai o frame
    await new Promise<void>((resolve, reject) => {
      ffmpeg()
        .input(tempVideoPath)
        .seekInput('00:00:01')
        .frames(1)
        .output(outputPath)
        .size('1280x720')
        .on('start', (commandLine) => {
          console.log('Comando ffmpeg iniciado:', commandLine)
        })
        .on('progress', (progress) => {
          console.log('Progresso:', progress)
        })
        .on('end', () => {
          console.log('Frame extraído com sucesso:', outputPath)
          resolve()
        })
        .on('error', (err) => {
          console.error('Erro ao extrair frame:', err)
          reject(err)
        })
        .run()
    })

    return outputPath
  } catch (error) {
    console.error('Erro no processo:', error)
    throw error
  } finally {
    // Limpa o arquivo temporário
    if (fs.existsSync(tempVideoPath)) {
      fs.unlinkSync(tempVideoPath)
    }
  }
} 