import { prisma } from '@/lib/prisma'

// Interface para resultado de detecção de idioma
interface LanguageDetection {
  language: string
  confidence: number
}

// Interface para resultado de tradução
interface TranslationResult {
  translatedText: string
  sourceLanguage: string
  confidence: number
  source: 'google' | 'cache' | 'manual'
}

/**
 * Detecta o idioma de um texto
 */
export async function detectLanguage(text: string): Promise<LanguageDetection> {
  try {
    // Padrões simples para detecção de idioma
    const patterns = {
      japanese: /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/,
      english: /^[a-zA-Z0-9\s\-\.\!\?\,\'\"]+$/,
      portuguese: /\b(episódio|capítulo|parte|final|especial|ova)\b/i
    }

    // Verificar se é japonês
    if (patterns.japanese.test(text)) {
      return { language: 'ja', confidence: 0.9 }
    }

    // Verificar se é português
    if (patterns.portuguese.test(text)) {
      return { language: 'pt', confidence: 0.8 }
    }

    // Verificar se é inglês (padrão)
    if (patterns.english.test(text)) {
      return { language: 'en', confidence: 0.7 }
    }

    // Padrão: assumir inglês
    return { language: 'en', confidence: 0.5 }
  } catch (error) {
    console.error('Erro na detecção de idioma:', error)
    return { language: 'en', confidence: 0.3 }
  }
}

/**
 * Busca tradução no cache
 */
async function getCachedTranslation(
  originalTitle: string,
  sourceLanguage: string,
  targetLanguage: string = 'pt'
): Promise<TranslationResult | null> {
  try {
    const cached = await prisma.episodeTitleTranslation.findUnique({
      where: {
        originalTitle_sourceLanguage_targetLanguage: {
          originalTitle,
          sourceLanguage,
          targetLanguage
        }
      }
    })

    if (cached) {
      return {
        translatedText: cached.translatedTitle,
        sourceLanguage: cached.sourceLanguage,
        confidence: cached.confidence || 0.8,
        source: cached.isManual ? 'manual' : 'cache'
      }
    }

    return null
  } catch (error) {
    console.error('Erro ao buscar tradução em cache:', error)
    return null
  }
}

/**
 * Salva tradução no cache
 */
async function saveTranslationToCache(
  originalTitle: string,
  translatedTitle: string,
  sourceLanguage: string,
  targetLanguage: string = 'pt',
  isManual: boolean = false,
  confidence: number = 0.8,
  translationSource: string = 'google'
): Promise<void> {
  try {
    await prisma.episodeTitleTranslation.upsert({
      where: {
        originalTitle_sourceLanguage_targetLanguage: {
          originalTitle,
          sourceLanguage,
          targetLanguage
        }
      },
      update: {
        translatedTitle,
        isManual,
        confidence,
        translationSource,
        updatedAt: new Date()
      },
      create: {
        originalTitle,
        translatedTitle,
        sourceLanguage,
        targetLanguage,
        isManual,
        confidence,
        translationSource
      }
    })
  } catch (error) {
    console.error('Erro ao salvar tradução em cache:', error)
  }
}

/**
 * Traduz texto usando Google Translate (simulado)
 * Em produção, você implementaria a integração real com Google Translate API
 */
async function translateWithGoogle(
  text: string,
  sourceLanguage: string,
  targetLanguage: string = 'pt'
): Promise<TranslationResult> {
  try {
    // Simulação de tradução - em produção, use Google Translate API
    // const response = await fetch(`https://translate.googleapis.com/translate/v2?key=${process.env.GOOGLE_TRANSLATE_API_KEY}`, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     q: text,
    //     source: sourceLanguage,
    //     target: targetLanguage,
    //     format: 'text'
    //   })
    // })

    // Por enquanto, vamos usar traduções básicas simuladas
    const basicTranslations: Record<string, string> = {
      'Episode': 'Episódio',
      'Chapter': 'Capítulo',
      'Part': 'Parte',
      'Final': 'Final',
      'Special': 'Especial',
      'OVA': 'OVA',
      'Movie': 'Filme',
      'The Beginning': 'O Começo',
      'The End': 'O Fim',
      'First': 'Primeiro',
      'Last': 'Último',
      'New': 'Novo',
      'Old': 'Antigo'
    }

    let translatedText = text

    // Aplicar traduções básicas
    Object.entries(basicTranslations).forEach(([en, pt]) => {
      const regex = new RegExp(`\\b${en}\\b`, 'gi')
      translatedText = translatedText.replace(regex, pt)
    })

    // Se não houve mudança significativa, manter original
    if (translatedText === text && sourceLanguage === 'en') {
      // Para títulos em inglês que não foram traduzidos, manter original
      translatedText = text
    }

    return {
      translatedText,
      sourceLanguage,
      confidence: 0.7,
      source: 'google'
    }
  } catch (error) {
    console.error('Erro na tradução com Google:', error)
    return {
      translatedText: text, // Retornar original em caso de erro
      sourceLanguage,
      confidence: 0.3,
      source: 'google'
    }
  }
}

/**
 * Função principal para traduzir título de episódio
 */
export async function translateEpisodeTitle(
  title: string,
  targetLanguage: string = 'pt'
): Promise<TranslationResult> {
  try {
    // 1. Detectar idioma do título
    const detection = await detectLanguage(title)
    
    // Se já está no idioma alvo, retornar original
    if (detection.language === targetLanguage) {
      return {
        translatedText: title,
        sourceLanguage: detection.language,
        confidence: detection.confidence,
        source: 'cache'
      }
    }

    // 2. Verificar cache primeiro
    const cached = await getCachedTranslation(title, detection.language, targetLanguage)
    if (cached) {
      return cached
    }

    // 3. Traduzir usando Google Translate
    const translation = await translateWithGoogle(title, detection.language, targetLanguage)

    // 4. Salvar no cache
    await saveTranslationToCache(
      title,
      translation.translatedText,
      detection.language,
      targetLanguage,
      false,
      translation.confidence,
      'google'
    )

    return translation
  } catch (error) {
    console.error('Erro na tradução de título:', error)
    return {
      translatedText: title,
      sourceLanguage: 'unknown',
      confidence: 0.1,
      source: 'google'
    }
  }
}

/**
 * Tradução manual de título (para interface de admin)
 */
export async function saveManualTranslation(
  originalTitle: string,
  translatedTitle: string,
  sourceLanguage: string,
  targetLanguage: string = 'pt'
): Promise<void> {
  await saveTranslationToCache(
    originalTitle,
    translatedTitle,
    sourceLanguage,
    targetLanguage,
    true,
    1.0,
    'manual'
  )
}

/**
 * Buscar todas as traduções para um título
 */
export async function getTranslationsForTitle(originalTitle: string) {
  try {
    return await prisma.episodeTitleTranslation.findMany({
      where: { originalTitle },
      orderBy: [
        { isManual: 'desc' }, // Manuais primeiro
        { confidence: 'desc' }, // Maior confiança primeiro
        { createdAt: 'desc' } // Mais recentes primeiro
      ]
    })
  } catch (error) {
    console.error('Erro ao buscar traduções:', error)
    return []
  }
}
