import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const slug = params.slug;
  
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { isLike } = body;

    if (typeof isLike !== 'boolean') {
      return NextResponse.json(
        { error: 'isLike deve ser um booleano' },
        { status: 400 }
      );
    }

    // Busca o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: { slug }
    });

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      );
    }

    // Busca o usuário pelo email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      );
    }

    // Verifica se já existe um voto do usuário para este anime
    const existingVote = await prisma.vote.findUnique({
      where: {
        userId_animeId: {
          userId: user.id,
          animeId: anime.id
        }
      }
    });

    let vote;
    if (existingVote) {
      // Se o voto existir e for igual ao novo voto, remove o voto
      if (existingVote.isLike === isLike) {
        vote = await prisma.vote.delete({
          where: {
            id: existingVote.id
          }
        });
      } else {
        // Se for diferente, atualiza o voto
        vote = await prisma.vote.update({
          where: {
            id: existingVote.id
          },
          data: {
            isLike
          }
        });
      }
    } else {
      // Se não existir, cria um novo voto
      vote = await prisma.vote.create({
        data: {
          userId: user.id,
          animeId: anime.id,
          isLike
        }
      });
    }

    // Busca a contagem atualizada de votos
    const [likes, dislikes] = await Promise.all([
      prisma.vote.count({
        where: {
          animeId: anime.id,
          isLike: true
        }
      }),
      prisma.vote.count({
        where: {
          animeId: anime.id,
          isLike: false
        }
      })
    ]);

    return NextResponse.json({
      vote,
      likes,
      dislikes
    });
  } catch (error) {
    console.error('Error voting:', error);
    return NextResponse.json(
      { error: 'Erro ao processar voto' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const slug = params.slug;
  
  try {
    const session = await getServerSession(authOptions);
    
    // Busca o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: { slug }
    });

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      );
    }

    // Busca a contagem de votos
    const [likes, dislikes] = await Promise.all([
      prisma.vote.count({
        where: {
          animeId: anime.id,
          isLike: true
        }
      }),
      prisma.vote.count({
        where: {
          animeId: anime.id,
          isLike: false
        }
      })
    ]);

    // Se o usuário estiver logado, busca o voto dele
    let userVote = null;
    if (session?.user?.email) {
      const user = await prisma.user.findUnique({
        where: { email: session.user.email }
      });

      if (user) {
        const vote = await prisma.vote.findUnique({
          where: {
            userId_animeId: {
              userId: user.id,
              animeId: anime.id
            }
          }
        });

        if (vote) {
          userVote = vote.isLike;
        }
      }
    }

    return NextResponse.json({
      likes,
      dislikes,
      userVote
    });
  } catch (error) {
    console.error('Error fetching votes:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar votos' },
      { status: 500 }
    );
  }
} 