import { prisma } from '@/lib/prisma'
import { notFound } from 'next/navigation'
import DeleteEpisodeButton from './DeleteEpisodeButton'

interface Episode {
  id: string
  number: number
  title: string
  airDate: Date
  videoUrl: string
  animeId: string
}

interface Anime {
  id: string
  title: string
  malId: number
  totalEpisodes: number
  episodes: Episode[]
}

interface MalEpisode {
  aired: string
  title: string
  title_romanji: string
  title_japanese: string
}

interface PageProps {
  params: {
    slug: string
  }
}

export default async function EpisodesPage({ params }: PageProps) {
  const anime = await prisma.anime.findUnique({
    where: {
      slug: params.slug,
    },
    include: {
      episodes: {
        orderBy: {
          number: 'asc',
        },
      },
    },
  })

  if (!anime) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Episódios de {anime.title}</h1>
      
      <div className="grid gap-4">
        {anime.episodes.map((episode) => (
          <div
            key={episode.id}
            className="bg-gray-800 rounded-lg p-4 flex items-center justify-between"
          >
            <div>
              <h3 className="text-lg font-semibold">
                Episódio {episode.number}: {episode.title}
              </h3>
              <p className="text-gray-400">
                Data de exibição: {new Date(episode.airDate).toLocaleDateString()}
              </p>
            </div>
            <div className="flex gap-2">
              <a
                href={`/admin/animes/${anime.slug}/episodes/${episode.id}/edit`}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              >
                Editar
              </a>
              <DeleteEpisodeButton animeSlug={anime.slug} episodeId={episode.id} />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 