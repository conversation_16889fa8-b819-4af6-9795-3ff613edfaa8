'use client'

import { useEffect, useState } from 'react'
import { toast } from 'react-hot-toast'

export default function RegisterNotificationSW() {
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null)

  useEffect(() => {
    // Verificar se o navegador suporta service workers
    if (!('serviceWorker' in navigator)) {
      console.log('Service Workers não são suportados neste navegador')
      return
    }

    // Verificar se o navegador suporta notificações
    if (!('Notification' in window)) {
      console.log('Este navegador não suporta notificações')
      return
    }

    // Registrar o service worker
    const registerSW = async () => {
      try {
        // Verificar se já existe um service worker registrado
        const existingRegistration = await navigator.serviceWorker.getRegistration('/notification-sw.js')

        if (existingRegistration) {
          console.log('Notification Service Worker já registrado:', existingRegistration.scope)
          setSwRegistration(existingRegistration)
          return existingRegistration
        }

        // Registrar novo service worker
        const registration = await navigator.serviceWorker.register('/notification-sw.js', {
          scope: '/'
        })

        console.log('Notification Service Worker registrado com sucesso:', registration.scope)
        setSwRegistration(registration)
        return registration
      } catch (error) {
        console.error('Falha ao registrar o Notification Service Worker:', error)
        return null
      }
    }

    // Garantir que o service worker seja registrado quando a página carregar
    if (document.readyState === 'complete') {
      registerSW()
    } else {
      window.addEventListener('load', registerSW)
      return () => {
        window.removeEventListener('load', registerSW)
      }
    }
  }, [])

  return null
}
