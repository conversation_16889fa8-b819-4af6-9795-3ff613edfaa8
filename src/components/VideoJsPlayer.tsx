'use client'

import { useRef, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import dynamic from 'next/dynamic'
import 'video.js/dist/video-js.css'
import '@/styles/videojs-custom.css'

// Importar Video.js dinamicamente apenas no lado do cliente
const videojs = dynamic(() => import('video.js'), { ssr: false })

interface VideoJsPlayerProps {
  videoUrl: string
  poster?: string
  title?: string
  className?: string
  onNextEpisode?: () => void
  episodeId?: string
  animeSlug?: string
  onReady?: (player: any) => void
  onError?: () => void
}

export default function VideoJsPlayer({
  videoUrl,
  poster,
  title = 'Episódio de Anime',
  className = '',
  onNextEpisode,
  episodeId,
  animeSlug,
  onReady,
  onError
}: VideoJsPlayerProps) {
  const { data: session } = useSession()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const playerRef = useRef<any>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const [hasMarkedAsWatched, setHasMarkedAsWatched] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState(0)
  const saveInterval = 15000 // Salvar a cada 15 segundos

  // Configurações do Video.js
  const videoJsOptions = {
    autoplay: false,
    controls: true,
    responsive: true,
    fluid: true,
    playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
    poster: poster,
    preload: 'auto',
    sources: [{
      src: videoUrl,
      type: 'video/mp4'
    }],
    controlBar: {
      children: [
        'playToggle',
        'volumePanel',
        'currentTimeDisplay',
        'timeDivider',
        'durationDisplay',
        'progressControl',
        'remainingTimeDisplay',
        'playbackRateMenuButton',
        'pictureInPictureToggle',
        'fullscreenToggle'
      ]
    },
    userActions: {
      hotkeys: true
    }
  }

  // Inicializar o player Video.js
  useEffect(() => {
    if (!videoRef.current) return

    // Verificar se o Video.js está disponível
    if (!videojs) {
      console.warn('Video.js não está disponível')
      if (onError) onError()
      return
    }

    // Inicializar o player
    if (!playerRef.current) {
      try {
        const player = videojs(videoRef.current, videoJsOptions, () => {
          console.log('Player inicializado')

          // Carregar o progresso de visualização
          loadWatchProgress(player)

          if (onReady) onReady(player)
        })

        // Configurar eventos do player
        player.on('timeupdate', () => {
          handleTimeUpdate(player)
        })

        player.on('ended', () => {
          handleEnded()
        })

        player.on('error', () => {
          console.error('Erro no player Video.js')
          if (onError) onError()
        })

        // Adicionar hotkeys para controle do player
        player.ready(() => {
          player.on('keydown', (e: KeyboardEvent) => {
            // Setas esquerda/direita para avançar/retroceder
            if (e.key === 'ArrowLeft') {
              player.currentTime(Math.max(0, player.currentTime() - 10))
            } else if (e.key === 'ArrowRight') {
              player.currentTime(Math.min(player.duration(), player.currentTime() + 10))
            }
            // Espaço para play/pause
            else if (e.key === ' ' || e.key === 'Spacebar') {
              if (player.paused()) {
                player.play()
              } else {
                player.pause()
              }
              e.preventDefault()
            }
            // F para tela cheia
            else if (e.key === 'f' || e.key === 'F') {
              if (player.isFullscreen()) {
                player.exitFullscreen()
              } else {
                player.requestFullscreen()
              }
            }
            // M para mudo
            else if (e.key === 'm' || e.key === 'M') {
              player.muted(!player.muted())
            }
          })
        })

        playerRef.current = player
      } catch (error) {
        console.error('Erro ao inicializar o player Video.js:', error)
        if (onError) onError()
      }
    }

    // Limpar o player quando o componente for desmontado
    return () => {
      if (playerRef.current) {
        try {
          playerRef.current.dispose()
          playerRef.current = null
        } catch (error) {
          console.error('Erro ao destruir o player Video.js:', error)
        }
      }
    }
  }, [videoRef.current, videoUrl])

  // Carregar o progresso de visualização
  const loadWatchProgress = async (player: any) => {
    if (!session?.user || !episodeId || !player) return

    try {
      const response = await fetch(`/api/watch-progress?episodeId=${episodeId}`)

      if (response.ok) {
        const data = await response.json()

        if (data.currentTime) {
          // Só definir o tempo se for maior que 0 e menor que 98% da duração
          if (data.currentTime > 0 && (!data.duration || data.percentage < 98)) {
            player.currentTime(data.currentTime)
          }
        }
      }
    } catch (error) {
      console.error('Erro ao carregar progresso:', error)
    }
  }

  // Salvar o progresso de visualização
  const saveWatchProgress = async (currentTime: number, duration: number) => {
    if (!session?.user || !episodeId) return

    try {
      await fetch('/api/watch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          currentTime,
          duration,
        }),
      })
    } catch (error) {
      console.error('Erro ao salvar progresso:', error)
    }
  }

  // Atualizar o tempo atual e salvar o progresso
  const handleTimeUpdate = (player: any) => {
    if (!player) return

    const currentTime = player.currentTime()
    const duration = player.duration()

    // Marcar como assistido quando atingir 80% do vídeo
    if (!hasMarkedAsWatched && currentTime > duration * 0.8 && animeSlug && episodeId) {
      setHasMarkedAsWatched(true)
      updateWatchedStatus(episodeId, true)
    }

    // Salvar o progresso a cada intervalo
    const now = Date.now()
    if (now - lastSaveTime >= saveInterval) {
      setLastSaveTime(now)
      saveWatchProgress(currentTime, duration)
    }
  }

  // Atualizar o status de assistido
  const updateWatchedStatus = async (episodeId: string, watched: boolean) => {
    if (!animeSlug || !session?.user) return

    try {
      const response = await fetch(`/api/animes/${animeSlug}/watched-episodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          watched,
        }),
      })

      if (!response.ok) {
        throw new Error('Falha ao atualizar status de assistido')
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error)
    }
  }

  // Lidar com o fim do vídeo
  const handleEnded = () => {
    if (onNextEpisode) {
      // Pequeno atraso antes de ir para o próximo episódio
      setTimeout(() => {
        onNextEpisode()
      }, 1000)
    }
  }

  // Os estilos personalizados agora estão no arquivo CSS externo

  return (
    <div ref={containerRef} className={`relative aspect-video bg-black ${className}`}>
      <div data-vjs-player>
        <video
          ref={videoRef}
          className="video-js vjs-big-play-centered vjs-theme-dark"
        />
      </div>
    </div>
  )
}
