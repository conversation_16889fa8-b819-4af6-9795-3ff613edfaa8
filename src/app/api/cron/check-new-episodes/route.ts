import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendAnimeNotification } from '@/services/notificationService'

// Função para verificar se uma string de data é de hoje
function isToday(dateString: string): boolean {
  const today = new Date()
  const date = new Date(dateString)
  
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  )
}

// Função para obter o dia da semana atual (0 = Domingo, 1 = Segunda, etc.)
function getCurrentDayOfWeek(): string {
  const days = ['Domingo', 'Segunda', 'Ter<PERSON>', 'Quarta', 'Quin<PERSON>', 'Sexta', 'Sábado']
  const today = new Date()
  return days[today.getDay()]
}

export async function GET(request: NextRequest) {
  try {
    // Verificar a chave de API para autorização
    const apiKey = request.headers.get('x-api-key')
    const validApiKey = process.env.CRON_API_KEY

    if (!apiKey || apiKey !== validApiKey) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    // Obter o dia da semana atual
    const currentDay = getCurrentDayOfWeek()

    // Buscar animes em lançamento que são lançados no dia atual
    const ongoingAnimes = await prisma.anime.findMany({
      where: {
        OR: [
          { status: 'Em lançamento' },
          { status: 'Em Andamento' }
        ],
        releaseDay: currentDay
      },
      include: {
        episodes: {
          orderBy: {
            number: 'desc'
          },
          take: 1
        }
      }
    })

    console.log(`Verificando ${ongoingAnimes.length} animes em lançamento para o dia ${currentDay}`)

    // Para cada anime, verificar se há novos episódios
    const results = await Promise.all(
      ongoingAnimes.map(async (anime) => {
        try {
          // Verificar se o anime tem episódios
          if (anime.episodes.length === 0) {
            return {
              animeId: anime.id,
              title: anime.title,
              status: 'sem_episodios',
              message: 'Anime sem episódios'
            }
          }

          const latestEpisode = anime.episodes[0]
          
          // Verificar se o episódio mais recente foi lançado hoje
          if (latestEpisode.airDate && isToday(latestEpisode.airDate.toString())) {
            console.log(`Novo episódio encontrado para ${anime.title}: Episódio ${latestEpisode.number}`)
            
            // Enviar notificação para os usuários inscritos
            try {
              const notificationResult = await sendAnimeNotification(
                anime.id,
                latestEpisode.number,
                `Novo episódio de ${anime.title}`,
                `O episódio ${latestEpisode.number} de ${anime.title} acabou de ser lançado!`,
                anime.image || undefined
              )
              
              return {
                animeId: anime.id,
                title: anime.title,
                episodeNumber: latestEpisode.number,
                status: 'notificado',
                notificationsSent: notificationResult.results.filter(r => r.success).length,
                message: notificationResult.message
              }
            } catch (notificationError) {
              console.error(`Erro ao enviar notificação para ${anime.title}:`, notificationError)
              
              return {
                animeId: anime.id,
                title: anime.title,
                episodeNumber: latestEpisode.number,
                status: 'erro_notificacao',
                error: notificationError instanceof Error ? notificationError.message : 'Erro desconhecido'
              }
            }
          }
          
          return {
            animeId: anime.id,
            title: anime.title,
            status: 'sem_novos_episodios',
            message: 'Nenhum episódio novo hoje'
          }
        } catch (error) {
          console.error(`Erro ao processar anime ${anime.title}:`, error)
          
          return {
            animeId: anime.id,
            title: anime.title,
            status: 'erro',
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          }
        }
      })
    )

    // Contar resultados por status
    const statusCounts = results.reduce((acc, result) => {
      const status = result.status
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      success: true,
      day: currentDay,
      totalAnimes: ongoingAnimes.length,
      statusCounts,
      results
    })
  } catch (error) {
    console.error('Erro ao verificar novos episódios:', error)
    
    return NextResponse.json(
      { 
        error: 'Erro ao verificar novos episódios',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    )
  }
}
