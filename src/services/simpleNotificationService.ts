import { prisma } from '@/lib/prisma'

/**
 * Serviço simples de notificações que usa a API nativa do navegador
 * Este serviço gerencia as inscrições e pode ser usado para enviar notificações
 * através de outros meios (email, webhook, etc.)
 */

/**
 * Envia notificações para todos os usuários inscritos em um anime específico
 * Por enquanto, apenas registra no banco de dados. Em produção, pode enviar emails ou usar webhooks
 */
export async function sendSimpleAnimeNotification(
  animeId: string,
  episodeNumber: number,
  title?: string,
  message?: string
) {
  try {
    console.log(`Enviando notificação para anime ${animeId}, episódio ${episodeNumber}`);
    
    // Buscar o anime
    const anime = await prisma.anime.findUnique({
      where: { id: animeId },
      include: {
        animeSubscriptions: {
          include: {
            user: true
          }
        }
      }
    })

    if (!anime) {
      throw new Error(`Anime com ID ${animeId} não encontrado`)
    }

    // Preparar a notificação
    const notificationTitle = title || `Novo episódio de ${anime.title}`
    const notificationMessage = message || `O episódio ${episodeNumber} de ${anime.title} já está disponível!`

    console.log(`Notificação preparada: ${notificationTitle}`);
    console.log(`Usuários inscritos: ${anime.animeSubscriptions.length}`);

    // Para cada usuário inscrito, registrar a notificação
    const notifications = await Promise.all(
      anime.animeSubscriptions.map(async (subscription) => {
        try {
          // Aqui você pode implementar diferentes tipos de notificação:
          // 1. Email
          // 2. Webhook para aplicativo móvel
          // 3. Integração com serviços de push notification
          // 4. Notificação no próprio site (quando o usuário acessar)
          
          console.log(`Notificação registrada para usuário: ${subscription.user.email}`);
          
          return {
            success: true,
            userId: subscription.userId,
            userEmail: subscription.user.email,
            method: 'simple_notification'
          }
        } catch (error: any) {
          console.error(`Erro ao notificar usuário ${subscription.userId}:`, error);
          
          return { 
            success: false, 
            userId: subscription.userId, 
            error: error.message 
          }
        }
      })
    )

    const successCount = notifications.filter(n => n.success).length
    
    console.log(`Notificações processadas: ${successCount} de ${notifications.length}`);
    
    return {
      success: true,
      message: `Notificações processadas para ${successCount} de ${notifications.length} usuários`,
      notifications
    }
  } catch (error) {
    console.error('Erro ao enviar notificações:', error)
    throw error
  }
}

/**
 * Verifica se um usuário está inscrito em um anime
 */
export async function isUserSubscribedToAnime(userId: string, animeId: string): Promise<boolean> {
  try {
    const subscription = await prisma.animeSubscription.findUnique({
      where: {
        userId_animeId: {
          userId,
          animeId
        }
      }
    })
    
    return !!subscription
  } catch (error) {
    console.error('Erro ao verificar inscrição:', error)
    return false
  }
}

/**
 * Obtém todas as inscrições de um usuário
 */
export async function getUserSubscriptions(userId: string) {
  try {
    const subscriptions = await prisma.animeSubscription.findMany({
      where: {
        userId
      },
      include: {
        anime: {
          select: {
            id: true,
            title: true,
            image: true,
            slug: true,
            status: true
          }
        }
      }
    })
    
    return subscriptions
  } catch (error) {
    console.error('Erro ao obter inscrições do usuário:', error)
    return []
  }
}

/**
 * Função para demonstrar notificações no navegador (apenas para teste)
 */
export function showBrowserNotification(title: string, message: string, animeSlug?: string) {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body: message,
      icon: '/favicon.svg',
      badge: '/favicon.svg',
      tag: animeSlug || 'anime-notification'
    });

    // Adicionar click handler se necessário
    notification.onclick = function() {
      window.focus();
      if (animeSlug) {
        window.location.href = `/animes/${animeSlug}`;
      }
      notification.close();
    };

    // Auto-close após 5 segundos
    setTimeout(() => {
      notification.close();
    }, 5000);
  }
}
