'use client'

import { useState, useEffect } from 'react'

interface ImportProgressProps {
  importId: string | null
  onComplete?: () => void
}

interface ProgressData {
  progress: number
  message: string
  isComplete: boolean
  step: number
  totalSteps: number
  stepProgress: number
}

export default function ImportProgressBar({ importId, onComplete }: ImportProgressProps) {
  const [progress, setProgress] = useState<ProgressData | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Log para depuração
  console.log('ImportProgressBar renderizado com ID:', importId);

  useEffect(() => {
    console.log('ImportProgressBar useEffect executado com ID:', importId);
    if (!importId) return

    let intervalId: NodeJS.Timeout | null = null;

    const fetchProgress = async () => {
      console.log('Buscando progresso para ID:', importId);
      try {
        const url = `/api/import-anime/progress/${importId}`;
        console.log('URL da API de progresso:', url);
        const response = await fetch(url);
        console.log('Resposta da API de progresso:', response.status, response.statusText);

        if (!response.ok) {
          if (response.status === 404) {
            // Se a importação não for encontrada, considerar como concluída
            setProgress({
              progress: 100,
              message: 'Importação concluída',
              isComplete: true,
              step: 3,
              totalSteps: 3,
              stepProgress: 100
            })

            if (onComplete) {
              onComplete()
            }

            // Limpar o intervalo
            if (intervalId) {
              clearInterval(intervalId)
              intervalId = null
            }
            return
          }

          throw new Error(`Erro ao buscar progresso: ${response.status}`)
        }

        const data = await response.json()
        console.log('Dados de progresso recebidos:', data);
        setProgress(data)

        if (data.isComplete) {
          if (onComplete) {
            onComplete()
          }

          // Limpar o intervalo
          if (intervalId) {
            clearInterval(intervalId)
            intervalId = null
          }
        }
      } catch (error) {
        console.error('Erro ao buscar progresso:', error)
        setError('Erro ao buscar progresso da importação')

        // Limpar o intervalo em caso de erro
        if (intervalId) {
          clearInterval(intervalId)
          intervalId = null
        }
      }
    }

    // Fazer a primeira chamada imediatamente
    fetchProgress()

    // Configurar o intervalo para chamadas subsequentes
    intervalId = setInterval(fetchProgress, 1000) // Atualizar a cada segundo

    // Limpar o intervalo quando o componente for desmontado
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
    }
  }, [importId, onComplete]) // Remover isPolling das dependências

  if (!importId || !progress) {
    return null
  }

  return (
    <div className="w-full p-4 bg-gray-800 rounded-lg shadow-md">
      <div className="mb-2 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">Importando Anime</h3>
        <span className="text-sm text-gray-300">{progress.progress}%</span>
      </div>

      <div className="w-full bg-gray-700 rounded-full h-4 mb-2">
        <div
          className="bg-blue-600 h-4 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${progress.progress}%` }}
        ></div>
      </div>

      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-300">
          Etapa {progress.step} de {progress.totalSteps}
        </span>
        <span className="text-gray-300">{progress.message}</span>
      </div>

      {error && (
        <div className="mt-2 text-red-500 text-sm">
          {error}
        </div>
      )}
    </div>
  )
}
