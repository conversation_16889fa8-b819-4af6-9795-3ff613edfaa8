'use client'

import { useState, useEffect } from 'react'

interface AnimeEpisodeUrlFetcherProps {
  onAddEpisodeUrl: (animeSlug: string, episodeNumber: number, videoUrl: string) => void
}

interface Anime {
  id: string
  title: string
  slug: string
  episodes: {
    id: string
    number: number
    title: string
  }[]
}

interface EpisodeInfo {
  number: number;
  videoUrl: string;
  sourceType: string;
}

export default function AnimeEpisodeUrlFetcher({ onAddEpisodeUrl }: AnimeEpisodeUrlFetcherProps) {
  const [animes, setAnimes] = useState<Anime[]>([])
  const [selectedAnimeSlug, setSelectedAnimeSlug] = useState('')
  const [episodeNumber, setEpisodeNumber] = useState(1)
  const [episodeUrl, setEpisodeUrl] = useState('')
  const [extractedUrl, setExtractedUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [urlType, setUrlType] = useState<string | null>(null)
  const [extractAllEpisodes, setExtractAllEpisodes] = useState(false)
  const [extractedEpisodes, setExtractedEpisodes] = useState<EpisodeInfo[]>([])
  const [showExtractedEpisodes, setShowExtractedEpisodes] = useState(false)

  // Fetch animes on component mount
  useEffect(() => {
    fetchAnimes()
  }, [])

  const fetchAnimes = async () => {
    try {
      const response = await fetch('/api/animes')
      if (!response.ok) {
        throw new Error('Erro ao buscar animes')
      }
      const data = await response.json()
      setAnimes(data)
    } catch (error) {
      console.error('Error fetching animes:', error)
      setError('Erro ao buscar animes')
    }
  }

  const handleFetchUrl = async () => {
    if (!episodeUrl.trim()) {
      setError('Por favor, insira a URL do episódio')
      return
    }

    setIsLoading(true)
    setError('')
    setSuccess('')
    setExtractedUrl('')
    setUrlType(null)
    setExtractedEpisodes([])
    setShowExtractedEpisodes(false)

    try {
      // Check if it's a direct MP4 URL or LightSpeed URL
      const isDirectMp4 = episodeUrl.toLowerCase().endsWith('.mp4') ||
                          episodeUrl.includes('/mp4/') ||
                          episodeUrl.includes('/mp4_temp/')

      const isLightSpeed = episodeUrl.includes('lightspeedst.net')

      if ((isDirectMp4 || isLightSpeed) && !extractAllEpisodes) {
        // If it's a direct MP4 URL or LightSpeed URL, we can use it directly
        setExtractedUrl(episodeUrl)

        if (isLightSpeed) {
          setUrlType('LightSpeed')
          setSuccess('URL do LightSpeed detectada e pronta para uso!')
        } else {
          setUrlType('MP4 Direto')
          setSuccess('URL de MP4 direta detectada e pronta para uso!')
        }
      } else {
        // Otherwise, use the API to extract the URL
        let apiUrl;

        if (extractAllEpisodes) {
          // Usar a API de extração de múltiplos episódios
          apiUrl = `/api/fetch-multiple-episodes?url=${encodeURIComponent(episodeUrl)}`
        } else {
          // Usar a API de extração de um único episódio
          apiUrl = `/api/fetch-episode-url?url=${encodeURIComponent(episodeUrl)}`
        }

        setSuccess('Extraindo URLs, por favor aguarde...')
        const response = await fetch(apiUrl)

        // First check if the response is JSON
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error(`Resposta inválida do servidor: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || `Erro ao buscar URL do vídeo: ${response.status} ${response.statusText}`)
        }

        if (extractAllEpisodes) {
          // Processar resposta para todos os episódios
          if (!data.episodes || data.episodes.length === 0) {
            throw new Error('Não foi possível extrair URLs dos episódios')
          }

          // Formatar os episódios para o formato esperado
          const formattedEpisodes = data.episodes.map((ep: any) => ({
            number: ep.episodeNumber,
            videoUrl: ep.videoUrl,
            sourceType: ep.sourceType
          }));

          setExtractedEpisodes(formattedEpisodes)
          setShowExtractedEpisodes(true)
          setSuccess(`Extraídas URLs de ${formattedEpisodes.length} episódios com sucesso! (${data.episodesFound} de ${data.totalEpisodes} disponíveis)`)
        } else {
          // Processar resposta para um único episódio
          if (!data.videoUrl) {
            throw new Error('Não foi possível extrair a URL do vídeo')
          }

          setExtractedUrl(data.videoUrl)
          setUrlType(data.sourceType || 'Desconhecido')
          setSuccess(`URL do vídeo extraída com sucesso!`)
        }
      }
    } catch (error) {
      console.error('Error fetching video URL:', error)
      setError(error instanceof Error ? error.message : 'Erro ao buscar URL do vídeo')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddEpisode = () => {
    if (!selectedAnimeSlug) {
      setError('Por favor, selecione um anime')
      return
    }

    if (!episodeNumber) {
      setError('Por favor, insira o número do episódio')
      return
    }

    if (!extractedUrl) {
      setError('Por favor, extraia a URL do vídeo primeiro')
      return
    }

    onAddEpisodeUrl(selectedAnimeSlug, episodeNumber, extractedUrl)
    setSuccess('URL do episódio adicionada com sucesso!')

    // Reset form
    setEpisodeUrl('')
    setExtractedUrl('')
  }

  const handleAddAllEpisodes = () => {
    if (!selectedAnimeSlug) {
      setError('Por favor, selecione um anime')
      return
    }

    if (extractedEpisodes.length === 0) {
      setError('Não há episódios extraídos para adicionar')
      return
    }

    // Adicionar cada episódio extraído
    let addedCount = 0
    for (const episode of extractedEpisodes) {
      try {
        onAddEpisodeUrl(selectedAnimeSlug, episode.number, episode.videoUrl)
        addedCount++
      } catch (error) {
        console.error(`Erro ao adicionar episódio ${episode.number}:`, error)
      }
    }

    setSuccess(`${addedCount} episódios adicionados com sucesso!`)

    // Reset form
    setEpisodeUrl('')
    setExtractedEpisodes([])
    setShowExtractedEpisodes(false)
  }

  // Função para testar a URL em um player de vídeo
  const handleTestInPlayer = () => {
    if (!extractedUrl) return

    // Criar uma URL temporária para o player de teste
    const testUrl = `/animes/player-test?url=${encodeURIComponent(extractedUrl)}`
    window.open(testUrl, '_blank')
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
      <h2 className="text-xl font-bold mb-4">Extrair URL de Episódio</h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Anime
          </label>
          <select
            value={selectedAnimeSlug}
            onChange={(e) => setSelectedAnimeSlug(e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Selecione um anime</option>
            {animes.map((anime) => (
              <option key={anime.id} value={anime.slug}>
                {anime.title}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Número do Episódio
          </label>
          <input
            type="number"
            value={episodeNumber}
            onChange={(e) => setEpisodeNumber(parseInt(e.target.value))}
            min={1}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            URL do Episódio (de qualquer site ou MP4 direto)
          </label>
          <div className="flex">
            <input
              type="text"
              value={episodeUrl}
              onChange={(e) => setEpisodeUrl(e.target.value)}
              placeholder="https://source.domain/animes/nome-do-anime/1"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={() => setEpisodeUrl('')}
              className="px-3 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-r-lg"
              title="Limpar"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="mt-2 text-xs text-gray-400">
            <p>Exemplos:</p>
            <ul className="list-disc pl-5 space-y-1 mt-1">
              <li>URL do Blogger: https://www.blogger.com/video.g?token=AD6v5dwZh-u5wauyaKFM6o48GcZp5PXdlM0F0rJEQDH_7QsJYVPzGiWXu2EijeSOTy65wODbCHVqmAU2dIdazFnUf62MAwevtfRhya2pUkcneltMNP2CRjestpb_CXuBltQexZUyVek</li>
              <li>URL do LightSpeed: https://lightspeedst.net/s5/mp4/jujutsu-kaisen-dublado/sd/1.mp4</li>
              <li>URL do LightSpeed (temp): https://lightspeedst.net/s5/mp4_temp/saikyou-no-ousama-nidome-no-jinsei-wa-nani-wo-suru/1/480p.mp4</li>
            </ul>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center mb-2">
            <input
              type="checkbox"
              id="extractAllEpisodes"
              checked={extractAllEpisodes}
              onChange={(e) => setExtractAllEpisodes(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="extractAllEpisodes" className="text-sm font-medium text-gray-300">
              Extrair URLs de todos os episódios
            </label>
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleFetchUrl}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Extraindo...
                </>
              ) : (
                extractAllEpisodes ? 'Extrair URLs de Todos os Episódios' : 'Extrair URL do Vídeo'
              )}
            </button>
          </div>

          <div>
            <p className="text-sm text-gray-300 mb-2">URLs de exemplo para teste rápido:</p>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setEpisodeUrl('https://www.blogger.com/video.g?token=AD6v5dwZh-u5wauyaKFM6o48GcZp5PXdlM0F0rJEQDH_7QsJYVPzGiWXu2EijeSOTy65wODbCHVqmAU2dIdazFnUf62MAwevtfRhya2pUkcneltMNP2CRjestpb_CXuBltQexZUyVek')}
                className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm"
              >
                Blogger
              </button>
              <button
                onClick={() => setEpisodeUrl('https://lightspeedst.net/s5/mp4/jujutsu-kaisen-dublado/sd/1.mp4')}
                className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm"
              >
                LightSpeed
              </button>
              <button
                onClick={() => setEpisodeUrl('https://lightspeedst.net/s5/mp4_temp/saikyou-no-ousama-nidome-no-jinsei-wa-nani-wo-suru/1/480p.mp4')}
                className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm"
              >
                LightSpeed Temp
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/50 text-red-200 p-3 rounded-lg">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-900/50 text-green-200 p-3 rounded-lg">
            {success}
          </div>
        )}

        {extractedUrl && (
          <div>
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-300">
                URL do Vídeo Extraída
              </label>
              {urlType && (
                <span className="text-xs px-2 py-1 bg-blue-900 text-blue-200 rounded-full">
                  Tipo: {urlType}
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <input
                type="text"
                value={extractedUrl}
                readOnly
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none"
              />
              <button
                onClick={() => navigator.clipboard.writeText(extractedUrl)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg"
                title="Copiar URL"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                </svg>
              </button>
              <button
                onClick={() => window.open(extractedUrl, '_blank')}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg"
                title="Testar URL"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {extractedUrl && (
          <div className="flex gap-2">
            <button
              onClick={handleAddEpisode}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex-1"
            >
              Adicionar URL ao Episódio
            </button>
            <button
              onClick={handleTestInPlayer}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              title="Testar no Player"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>
        )}

        {showExtractedEpisodes && extractedEpisodes.length > 0 && (
          <div className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">Episódios Extraídos ({extractedEpisodes.length})</h3>
              <button
                onClick={handleAddAllEpisodes}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
              >
                Adicionar Todos os Episódios
              </button>
            </div>

            <div className="bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
              <table className="w-full text-sm">
                <thead className="text-left text-gray-300 border-b border-gray-600">
                  <tr>
                    <th className="pb-2">Episódio</th>
                    <th className="pb-2">Tipo</th>
                    <th className="pb-2">URL</th>
                    <th className="pb-2 text-right">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  {extractedEpisodes.map((episode) => (
                    <tr key={episode.number} className="border-b border-gray-600 last:border-0">
                      <td className="py-2">{episode.number}</td>
                      <td className="py-2">
                        <span className="px-2 py-1 bg-blue-900/50 text-blue-200 rounded-full text-xs">
                          {episode.sourceType}
                        </span>
                      </td>
                      <td className="py-2 truncate max-w-xs" title={episode.videoUrl}>
                        {episode.videoUrl.substring(0, 40)}...
                      </td>
                      <td className="py-2 text-right">
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(episode.videoUrl);
                            setSuccess(`URL do episódio ${episode.number} copiada!`);
                          }}
                          className="text-blue-400 hover:text-blue-300 mr-2"
                          title="Copiar URL"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                          </svg>
                        </button>
                        <button
                          onClick={() => {
                            window.open(episode.videoUrl, '_blank');
                          }}
                          className="text-green-400 hover:text-green-300"
                          title="Testar URL"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
