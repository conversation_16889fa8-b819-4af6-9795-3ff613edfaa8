import { NextResponse } from 'next/server'
import { updateProgress } from './progress/route'

// Cache simples em memória
const cache = new Map<string, { data: any; timestamp: number }>()
const CACHE_DURATION = 1000 * 60 * 60 // 1 hora em milissegundos

// Limite de requisições
const RATE_LIMIT = 3 // requisições por segundo
const requestTimestamps: number[] = []

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const id = params.id

  if (!id) {
    return NextResponse.json({ error: 'Anime ID is required' }, { status: 400 })
  }

  // Verifica cache
  const cached = cache.get(id)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return NextResponse.json(cached.data)
  }

  // Implementa rate limiting
  const now = Date.now()
  requestTimestamps.push(now)
  
  // Remove timestamps antigos (mais de 1 segundo)
  while (requestTimestamps.length > 0 && now - requestTimestamps[0] > 1000) {
    requestTimestamps.shift()
  }

  // Verifica se excedeu o limite
  if (requestTimestamps.length > RATE_LIMIT) {
    return NextResponse.json(
      { error: 'Rate limit exceeded. Please try again in a moment.' },
      { status: 429 }
    )
  }

  try {
    // Busca informações do anime
    const animeUrl = `https://api.jikan.moe/v4/anime/${id}`
    const animeResponse = await fetch(animeUrl)

    if (!animeResponse.ok) {
      throw new Error(`Failed to fetch anime: ${animeResponse.status} ${animeResponse.statusText}`)
    }

    const animeData = await animeResponse.json()

    // Busca informações dos episódios
    const episodesUrl = `https://api.jikan.moe/v4/anime/${id}/episodes`
    const episodesResponse = await fetch(episodesUrl)

    if (!episodesResponse.ok) {
      throw new Error(`Failed to fetch episodes: ${episodesResponse.status} ${episodesResponse.statusText}`)
    }

    const episodesData = await episodesResponse.json()
    let allEpisodes = episodesData.data || []

    // Se houver mais páginas, busca todas
    if (episodesData.pagination?.has_next_page) {
      const totalPages = episodesData.pagination.last_visible_page
      console.log(`Total de páginas: ${totalPages}`)
      
      // Calcula o progresso inicial (primeira página já carregada)
      const initialProgress = Math.floor((1 / totalPages) * 100)
      updateProgress(id, initialProgress)

      // Busca as páginas em lotes para não sobrecarregar a API
      const BATCH_SIZE = 3 // Reduzido para evitar rate limiting
      for (let i = 0; i < totalPages - 1; i += BATCH_SIZE) {
        const batch = Array.from(
          { length: Math.min(BATCH_SIZE, totalPages - 1 - i) },
          (_, index) => i + index + 2
        )
        
        console.log(`Carregando lote ${i / BATCH_SIZE + 1}: páginas ${batch.join(', ')}`)
        
        const batchPromises = batch.map(page => 
          fetch(`${episodesUrl}?page=${page}`).then(res => res.json())
        )

        const batchResults = await Promise.all(batchPromises)
        const batchEpisodes = batchResults.flatMap(data => data.data || [])
        allEpisodes = [...allEpisodes, ...batchEpisodes]

        // Atualiza o progresso após cada lote
        const progress = Math.floor(((i + BATCH_SIZE) / (totalPages - 1)) * 100)
        updateProgress(id, Math.min(progress, 99)) // Mantém em 99% até terminar
        
        // Pequena pausa entre os lotes para evitar rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    console.log(`Total de episódios carregados: ${allEpisodes.length}`)

    // Marca o progresso como 100% ao finalizar
    updateProgress(id, 100)

    // Traduz a descrição para português
    let translatedSynopsis = animeData.data.synopsis
    try {
      const translateUrl = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=pt&dt=t&q=${encodeURIComponent(animeData.data.synopsis)}`
      const translateResponse = await fetch(translateUrl)
      if (translateResponse.ok) {
        const translateData = await translateResponse.json()
        translatedSynopsis = translateData[0][0][0]
      }
    } catch (error) {
      console.error('Error translating synopsis:', error)
    }

    // Transforma os dados para o formato que precisamos
    const transformedData = {
      anime: {
        id: animeData.data.mal_id,
        title: animeData.data.title,
        synopsis: translatedSynopsis,
        image: animeData.data.images?.jpg?.large_image_url || '',
        status: animeData.data.status === 'Currently Airing' ? 'Em Andamento' : 'Concluído',
        totalEpisodes: animeData.data.episodes || 0,
        studio: animeData.data.studios?.[0]?.name || 'Desconhecido',
        year: new Date(animeData.data.aired?.from || '').getFullYear(),
        genres: animeData.data.genres?.map((genre: any) => genre.name) || [],
      },
      episodes: allEpisodes.map((episode: any) => ({
        number: episode.episode_id || episode.mal_id,
        title: episode.title,
        airDate: episode.aired,
        frame: episode.images?.jpg?.image_url || '',
      })) || [],
    }

    // Salva no cache
    cache.set(id, {
      data: transformedData,
      timestamp: now
    })

    return NextResponse.json(transformedData)
  } catch (error) {
    console.error('Error fetching anime details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch anime details', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 