import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const slug = params.slug;
  
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { content, episodeId } = body;

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { error: 'Conteúdo do comentário é obrigatório' },
        { status: 400 }
      );
    }

    // Busca o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: { slug }
    });

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      );
    }

    // Busca o usuário pelo email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      );
    }

    // Se tiver episodeId, verifica se o episódio existe
    if (episodeId) {
      const episode = await prisma.episode.findUnique({
        where: { id: episodeId }
      });

      if (!episode) {
        return NextResponse.json(
          { error: 'Episódio não encontrado' },
          { status: 404 }
        );
      }
    }

    // Cria o comentário
    const comment = await prisma.comment.create({
      data: {
        content,
        userId: user.id,
        animeId: anime.id,
        episodeId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    });

    return NextResponse.json(comment);
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json(
      { error: 'Erro ao criar comentário' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const slug = params.slug;
  
  try {
    const { searchParams } = new URL(request.url);
    const episodeId = searchParams.get('episodeId');

    // Busca o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: { slug }
    });

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      );
    }

    // Busca os comentários
    const comments = await prisma.comment.findMany({
      where: {
        animeId: anime.id,
        ...(episodeId ? { episodeId } : { episodeId: null })
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(comments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar comentários' },
      { status: 500 }
    );
  }
} 