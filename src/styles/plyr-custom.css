/* Estilos personalizados para o Plyr */

.plyr {
  --plyr-color-main: #8b5cf6;
  --plyr-video-control-color: #ffffff;
  --plyr-video-control-color-hover: #d1d5db;
  --plyr-video-control-background-hover: rgba(139, 92, 246, 0.5);
  --plyr-audio-control-background-hover: rgba(139, 92, 246, 0.5);
  --plyr-video-control-radius: 4px;
  --plyr-range-thumb-background: #8b5cf6;
  --plyr-range-fill-background: #8b5cf6;
  --plyr-badge-background: #8b5cf6;
  --plyr-badge-text-color: #ffffff;
  --plyr-tooltip-background: rgba(0, 0, 0, 0.75);
  --plyr-tooltip-color: #ffffff;
  --plyr-tooltip-radius: 4px;
  --plyr-control-spacing: 10px;
  --plyr-control-icon-size: 18px;
  --plyr-control-radius: 4px;
  --plyr-menu-background: rgba(28, 28, 28, 0.9);
  --plyr-menu-color: #ffffff;
  --plyr-menu-radius: 4px;
  --plyr-menu-arrow-size: 6px;
  --plyr-control-toggle-checked-background: #8b5cf6;
  --plyr-video-progress-buffered-background: rgba(255, 255, 255, 0.25);
  --plyr-range-track-height: 4px;
  --plyr-range-thumb-height: 14px;
  --plyr-range-thumb-width: 14px;
  --plyr-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.plyr--video {
  border-radius: 8px;
  overflow: hidden;
}

.plyr__control--overlaid {
  background: rgba(139, 92, 246, 0.8);
  padding: 15px;
}

.plyr__control--overlaid:hover {
  background: rgba(139, 92, 246, 1);
}

.plyr--full-ui input[type=range] {
  color: #8b5cf6;
}

.plyr__menu__container {
  background: #1f2937;
  border-radius: 4px;
}

.plyr__menu__container .plyr__control {
  color: #f3f4f6;
}

.plyr__menu__container .plyr__control--forward {
  color: #f3f4f6;
}

.plyr__menu__container .plyr__control--back {
  color: #d1d5db;
  border-color: #374151;
}

.plyr__menu__container .plyr__menu__value {
  color: #8b5cf6;
}

.plyr__time {
  font-size: 12px;
}

.plyr__volume {
  min-width: 60px;
}

/* Estilos para telas menores (mobile) */
@media (max-width: 640px) {
  .plyr__control--overlaid {
    padding: 12px;
  }
  
  .plyr__time {
    display: none;
  }
  
  .plyr__volume {
    display: none;
  }
  
  .plyr__controls {
    padding: 8px;
  }
  
  .plyr__control {
    padding: 6px;
  }
}

/* Animação de carregamento */
.plyr--loading .plyr__controls {
  opacity: 0;
}

.plyr--loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid rgba(139, 92, 246, 0.3);
  border-radius: 50%;
  border-top-color: #8b5cf6;
  animation: plyr-spin 1s ease-in-out infinite;
}

@keyframes plyr-spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
