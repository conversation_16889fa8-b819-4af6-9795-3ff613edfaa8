// Notification Service Worker para AnimesZera
// Este service worker gerencia notificações push para novos episódios

self.addEventListener('install', (event) => {
  console.log('[Notification Service Worker] Instalando...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('[Notification Service Worker] Ativado!');
  return self.clients.claim();
});

// Evento para receber notificações push
self.addEventListener('push', (event) => {
  console.log('[Notification Service Worker] Notificação push recebida.');

  let notificationData = {};
  
  try {
    notificationData = event.data.json();
  } catch (e) {
    notificationData = {
      title: 'AnimesZera',
      body: 'Há novidades para você!',
      icon: '/favicon.svg',
      badge: '/favicon.svg',
      data: {
        url: '/'
      }
    };
  }

  const title = notificationData.title || 'AnimesZera';
  const options = {
    body: notificationData.body,
    icon: notificationData.icon || '/favicon.svg',
    badge: notificationData.badge || '/favicon.svg',
    image: notificationData.image,
    data: notificationData.data || {},
    actions: notificationData.actions || [
      {
        action: 'open',
        title: 'Ver agora'
      },
      {
        action: 'close',
        title: 'Fechar'
      }
    ],
    vibrate: [100, 50, 100],
    timestamp: Date.now()
  };

  event.waitUntil(
    self.registration.showNotification(title, options)
  );
});

// Evento para quando o usuário clica na notificação
self.addEventListener('notificationclick', (event) => {
  console.log('[Notification Service Worker] Notificação clicada:', event.notification.tag);
  
  event.notification.close();

  // Lidar com ações específicas
  if (event.action === 'close') {
    return;
  }

  // URL padrão para abrir quando a notificação é clicada
  let urlToOpen = event.notification.data.url || '/';
  
  if (event.action === 'open' && event.notification.data.url) {
    urlToOpen = event.notification.data.url;
  }

  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    })
    .then((windowClients) => {
      // Verificar se já há uma janela aberta e focar nela
      for (let i = 0; i < windowClients.length; i++) {
        const client = windowClients[i];
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      
      // Se não houver janela aberta, abrir uma nova
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Evento para quando a notificação é fechada sem interação
self.addEventListener('notificationclose', (event) => {
  console.log('[Notification Service Worker] Notificação fechada sem interação');
});

// Evento para sincronização em segundo plano (útil para reenviar notificações falhas)
self.addEventListener('sync', (event) => {
  console.log('[Notification Service Worker] Evento de sincronização:', event.tag);
});
