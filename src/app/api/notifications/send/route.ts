import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import webpush from 'web-push'

// Configurar as chaves VAPID para Web Push
// Na produção, estas devem vir de variáveis de ambiente
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U'
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY || 'Dl8Q3ZXwrDkxr3Wou3kYBR3Fg-nEbT-vhS8MdHBEvzA'
const VAPID_SUBJECT = process.env.VAPID_SUBJECT || 'mailto:<EMAIL>'

webpush.setVapidDetails(
  VAPID_SUBJECT,
  VAPID_PUBLIC_KEY,
  VAPID_PRIVATE_KEY
)

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  // Verificar se é um administrador ou uma chamada autorizada
  if (!session?.user || session.user.role !== 'admin') {
    const apiKey = request.headers.get('x-api-key')
    const validApiKey = process.env.NOTIFICATION_API_KEY

    if (!apiKey || apiKey !== validApiKey) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }
  }

  try {
    const { animeId, episodeNumber, title, message, imageUrl } = await request.json()

    if (!animeId) {
      return NextResponse.json(
        { error: 'ID do anime não fornecido' },
        { status: 400 }
      )
    }

    // Buscar o anime
    const anime = await prisma.anime.findUnique({
      where: { id: animeId },
      include: {
        animeSubscriptions: {
          include: {
            user: {
              include: {
                pushSubscription: true
              }
            }
          }
        }
      }
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Preparar a notificação
    const notificationTitle = title || `Novo episódio de ${anime.title}`
    const notificationMessage = message || `O episódio ${episodeNumber} de ${anime.title} já está disponível!`
    const notificationImage = imageUrl || anime.image
    const notificationUrl = `/animes/${anime.slug}?episode=${episodeNumber}`

    const notificationPayload = JSON.stringify({
      title: notificationTitle,
      body: notificationMessage,
      icon: '/favicon.svg',
      badge: '/favicon.svg',
      image: notificationImage,
      data: {
        url: notificationUrl
      }
    })

    // Enviar notificações para todos os usuários inscritos
    const sendPromises = anime.animeSubscriptions.map(async (subscription) => {
      const pushSubscription = subscription.user.pushSubscription
      
      if (!pushSubscription) return { success: false, userId: subscription.userId, error: 'Sem inscrição push' }

      try {
        const pushConfig = {
          endpoint: pushSubscription.endpoint,
          keys: {
            p256dh: pushSubscription.p256dh,
            auth: pushSubscription.auth
          }
        }

        await webpush.sendNotification(pushConfig, notificationPayload)
        return { success: true, userId: subscription.userId }
      } catch (error) {
        console.error(`Erro ao enviar notificação para usuário ${subscription.userId}:`, error)
        
        // Se a inscrição expirou ou é inválida, remover
        if (error.statusCode === 404 || error.statusCode === 410) {
          await prisma.pushSubscription.delete({
            where: { userId: subscription.userId }
          })
        }
        
        return { success: false, userId: subscription.userId, error: error.message }
      }
    })

    const results = await Promise.all(sendPromises)
    const successCount = results.filter(r => r.success).length

    return NextResponse.json({
      success: true,
      message: `Notificações enviadas com sucesso para ${successCount} de ${results.length} usuários`,
      results
    })
  } catch (error) {
    console.error('Erro ao enviar notificações:', error)
    return NextResponse.json(
      { error: 'Erro ao enviar notificações' },
      { status: 500 }
    )
  }
}
