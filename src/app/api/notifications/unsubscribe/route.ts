import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  console.log('API unsubscribe chamada');

  try {
    const session = await getServerSession(authOptions)
    console.log('Sessão obtida:', session ? 'Sim' : 'Não');

    if (!session?.user) {
      console.log('Usuário não autenticado');
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    const body = await request.json()
    console.log('Corpo da requisição recebido');

    const { animeId } = body
    console.log('AnimeId recebido:', animeId);

    if (!animeId) {
      console.log('AnimeId não fornecido');
      return NextResponse.json(
        { error: 'ID do anime não fornecido' },
        { status: 400 }
      )
    }

    // Verificar se o anime existe
    console.log('Verificando se o anime existe...');
    const anime = await prisma.anime.findUnique({
      where: { id: animeId }
    })
    console.log('Anime encontrado:', anime ? 'Sim' : 'Não');

    if (!anime) {
      console.log('Anime não encontrado com ID:', animeId);
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Remover a inscrição do anime
    console.log('Removendo inscrição do anime...');
    try {
      await prisma.animeSubscription.deleteMany({
        where: {
          userId: session.user.id,
          animeId
        }
      })
      console.log('Inscrição removida com sucesso');
    } catch (error) {
      console.error('Erro ao remover inscrição:', error);
      throw error;
    }

    // Verificar se o usuário ainda tem outras inscrições
    console.log('Verificando inscrições restantes...');
    const remainingSubscriptions = await prisma.animeSubscription.count({
      where: {
        userId: session.user.id
      }
    })
    console.log('Inscrições restantes:', remainingSubscriptions);

    // Se não houver mais inscrições, remover também a PushSubscription
    if (remainingSubscriptions === 0) {
      console.log('Removendo push subscription...');
      try {
        await prisma.pushSubscription.deleteMany({
          where: {
            userId: session.user.id
          }
        })
        console.log('Push subscription removida com sucesso');
      } catch (error) {
        console.error('Erro ao remover push subscription:', error);
        // Não lançar erro aqui, pois a inscrição do anime já foi removida
      }
    }

    console.log('Cancelamento de inscrição concluído com sucesso');
    return NextResponse.json({
      success: true,
      message: `Inscrição para notificações de ${anime.title} cancelada com sucesso`
    })
  } catch (error) {
    console.error('Erro ao processar cancelamento de inscrição:', error)
    return NextResponse.json(
      { error: 'Erro ao cancelar inscrição', details: error instanceof Error ? error.message : 'Erro desconhecido' },
      { status: 500 }
    )
  }
}
