import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function PUT(
  request: Request,
  { params }: { params: { slug: string; episodeId: string } }
) {
  try {
    const { slug, episodeId } = params
    const data = await request.json()
    
    // Verifica se o anime existe
    const anime = await prisma.anime.findUnique({
      where: { slug },
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Formata a data para o formato ISO
    const formattedData = {
      ...data,
      airDate: new Date(data.airDate).toISOString(),
    }

    // Atualiza o episódio
    const episode = await prisma.episode.update({
      where: {
        id: episodeId,
      },
      data: formattedData,
    })

    return NextResponse.json(episode)
  } catch (error) {
    console.error('Error updating episode:', error)
    return NextResponse.json(
      { error: 'Erro ao atualizar episódio' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { slug: string; episodeId: string } }
) {
  try {
    const { slug, episodeId } = params
    
    // Verifica se o anime existe
    const anime = await prisma.anime.findUnique({
      where: { slug },
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Deleta o episódio
    await prisma.episode.delete({
      where: {
        id: episodeId,
      },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting episode:', error)
    return NextResponse.json(
      { error: 'Erro ao excluir episódio' },
      { status: 500 }
    )
  }
} 