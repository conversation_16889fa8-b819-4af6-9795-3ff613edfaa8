'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'

interface WatchedEpisode {
  episodeId: string
  watchedAt: string
}

/**
 * Hook para gerenciar episódios assistidos de um anime
 *
 * Este hook fornece funcionalidades para:
 * - Verificar se um episódio foi assistido
 * - Marcar/desmarcar um episódio como assistido
 * - Sincronizar o estado entre o servidor e o localStorage
 */
export function useWatchedEpisodes(animeSlug: string) {
  const { data: session } = useSession()
  const [watchedEpisodes, setWatchedEpisodes] = useState<WatchedEpisode[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Função para buscar episódios assistidos do servidor
  const fetchWatchedEpisodes = useCallback(async () => {
    if (!session?.user) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      console.log(`Buscando episódios assistidos para o anime ${animeSlug}`)
      const response = await fetch(`/api/animes/${animeSlug}/watched-episodes`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Erro na resposta da API:', errorData)
        throw new Error('Erro ao buscar episódios assistidos')
      }

      const data = await response.json()
      console.log(`Encontrados ${data.length} episódios assistidos para o anime ${animeSlug}:`, data)
      setWatchedEpisodes(data)

      // Atualiza o localStorage com os dados do servidor
      if (typeof window !== 'undefined' && session?.user?.id) {
        // Primeiro, limpa os dados antigos
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(`watched-${session.user.id}-`)) {
            const episodeId = key.split('-')[2]
            // Verifica se o episódio ainda está na lista do servidor
            if (!data.some((ep: WatchedEpisode) => ep.episodeId === episodeId)) {
              localStorage.removeItem(key)
            }
          }
        }

        // Depois, adiciona os novos dados
        data.forEach((episode: WatchedEpisode) => {
          const key = `watched-${session.user.id}-${episode.episodeId}`
          localStorage.setItem(key, 'true')
        })
      }
    } catch (error) {
      console.error('Erro ao buscar episódios assistidos:', error)
      setError(error instanceof Error ? error.message : 'Erro desconhecido')

      // Em caso de erro, tenta carregar do localStorage
      if (typeof window !== 'undefined' && session?.user?.id) {
        const localWatchedEpisodes: WatchedEpisode[] = []

        // Busca todos os itens do localStorage que começam com watched-userId
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(`watched-${session.user.id}-`)) {
            const episodeId = key.split('-')[2]
            if (localStorage.getItem(key) === 'true') {
              localWatchedEpisodes.push({
                episodeId,
                watchedAt: new Date().toISOString()
              })
            }
          }
        }

        console.log(`Carregados ${localWatchedEpisodes.length} episódios assistidos do localStorage`)
        setWatchedEpisodes(localWatchedEpisodes)
      }
    } finally {
      setIsLoading(false)
    }
  }, [session, animeSlug])

  // Carregar episódios assistidos quando o componente montar ou quando o usuário ou anime mudar
  useEffect(() => {
    if (session?.user) {
      console.log(`Inicializando hook useWatchedEpisodes para o anime ${animeSlug}`)
      fetchWatchedEpisodes()
    } else {
      setWatchedEpisodes([])
      setIsLoading(false)
    }
  }, [session, animeSlug, fetchWatchedEpisodes])

  // Verificar se um episódio específico foi assistido
  const isEpisodeWatched = useCallback((episodeId: string) => {
    return watchedEpisodes.some(episode => episode.episodeId === episodeId)
  }, [watchedEpisodes])

  // Atualizar o status de um episódio no servidor e localmente
  const updateWatchedStatus = useCallback(async (episodeId: string, isWatched: boolean) => {
    if (!session?.user) {
      toast.error('Você precisa estar logado para marcar episódios como assistidos')
      return false
    }

    console.log(`Atualizando status do episódio ${episodeId} para ${isWatched ? 'assistido' : 'não assistido'}`)

    // Verificar se o estado já é o desejado
    const isAlreadyWatched = isEpisodeWatched(episodeId)
    if (isWatched === isAlreadyWatched) {
      console.log(`Episódio ${episodeId} já está ${isWatched ? 'marcado como assistido' : 'desmarcado'}`)
      return true
    }

    // Atualizar estado local imediatamente (Optimistic UI)
    if (isWatched) {
      // Adicionar à lista de assistidos
      setWatchedEpisodes(prev => [
        ...prev,
        { episodeId, watchedAt: new Date().toISOString() }
      ])

      // Atualizar localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(`watched-${session.user.id}-${episodeId}`, 'true')
      }
    } else {
      // Remover da lista de assistidos
      setWatchedEpisodes(prev =>
        prev.filter(episode => episode.episodeId !== episodeId)
      )

      // Atualizar localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem(`watched-${session.user.id}-${episodeId}`)
      }
    }

    try {
      // Enviar para o servidor
      const method = isWatched ? 'POST' : 'DELETE'
      console.log(`Enviando requisição ${method} para /api/animes/${animeSlug}/watched-episodes`)

      const response = await fetch(`/api/animes/${animeSlug}/watched-episodes`, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ episodeId })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error(`Erro na resposta da API: ${response.status} ${response.statusText}`, errorData)
        throw new Error('Erro ao atualizar status do episódio')
      }

      console.log(`Status do episódio ${episodeId} atualizado com sucesso para ${isWatched ? 'assistido' : 'não assistido'}`)

      // Exibir toast de sucesso
      toast.success(isWatched
        ? 'Episódio marcado como assistido'
        : 'Episódio removido da lista de assistidos'
      )

      return true
    } catch (error) {
      console.error('Erro ao atualizar status do episódio:', error)

      // Exibir toast de erro
      toast.error('Erro ao atualizar status do episódio')

      // Reverter mudanças em caso de erro
      if (isWatched) {
        setWatchedEpisodes(prev => prev.filter(episode => episode.episodeId !== episodeId))
        if (typeof window !== 'undefined') {
          localStorage.removeItem(`watched-${session.user.id}-${episodeId}`)
        }
      } else {
        setWatchedEpisodes(prev => [
          ...prev,
          { episodeId, watchedAt: new Date().toISOString() }
        ])
        if (typeof window !== 'undefined') {
          localStorage.setItem(`watched-${session.user.id}-${episodeId}`, 'true')
        }
      }

      return false
    }
  }, [session, animeSlug, isEpisodeWatched])

  return {
    watchedEpisodes,
    isLoading,
    error,
    isEpisodeWatched,
    updateWatchedStatus,
    refreshWatchedEpisodes: fetchWatchedEpisodes
  }
}
