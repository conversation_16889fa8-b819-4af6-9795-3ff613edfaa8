import { NextResponse } from 'next/server';

// Tipo para o progresso da importação
interface ImportProgress {
  totalSteps: number;
  currentStep: number;
  stepProgress: number;
  totalProgress: number;
  message: string;
}

// Declarar o tipo global para o progresso da importação
declare global {
  var animeImportProgress: Record<string, ImportProgress>;
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const importId = params.id;
    
    if (!importId) {
      return NextResponse.json({ error: 'ID da importação não fornecido' }, { status: 400 });
    }
    
    // Verificar se existe um progresso para este ID
    if (!global.animeImportProgress || !global.animeImportProgress[importId]) {
      return NextResponse.json({ 
        error: 'Importação não encontrada ou já concluída',
        progress: 100,
        message: 'Importação concluída ou não iniciada'
      }, { status: 404 });
    }
    
    // Retornar o progresso atual
    const progress = global.animeImportProgress[importId];
    
    return NextResponse.json({
      progress: progress.totalProgress,
      step: progress.currentStep,
      totalSteps: progress.totalSteps,
      stepProgress: progress.stepProgress,
      message: progress.message,
      isComplete: progress.totalProgress >= 100
    });
  } catch (error) {
    console.error('Erro ao buscar progresso da importação:', error);
    return NextResponse.json({ 
      error: 'Erro ao buscar progresso da importação',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}
