import { prisma } from '@/lib/prisma'
import webpush from 'web-push'

// Configurar as chaves VAPID para Web Push
// Na produção, estas devem vir de variáveis de ambiente
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U'
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY || 'Dl8Q3ZXwrDkxr3Wou3kYBR3Fg-nEbT-vhS8MdHBEvzA'
const VAPID_SUBJECT = process.env.VAPID_SUBJECT || 'mailto:<EMAIL>'

webpush.setVapidDetails(
  VAPID_SUBJECT,
  VAPID_PUBLIC_KEY,
  VAPID_PRIVATE_KEY
)

/**
 * Envia notificações para todos os usuários inscritos em um anime específico
 */
export async function sendAnimeNotification(
  animeId: string,
  episodeNumber: number,
  title?: string,
  message?: string,
  imageUrl?: string
) {
  try {
    // Buscar o anime
    const anime = await prisma.anime.findUnique({
      where: { id: animeId },
      include: {
        animeSubscriptions: {
          include: {
            user: {
              include: {
                pushSubscription: true
              }
            }
          }
        }
      }
    })

    if (!anime) {
      throw new Error(`Anime com ID ${animeId} não encontrado`)
    }

    // Preparar a notificação
    const notificationTitle = title || `Novo episódio de ${anime.title}`
    const notificationMessage = message || `O episódio ${episodeNumber} de ${anime.title} já está disponível!`
    const notificationImage = imageUrl || anime.image
    const notificationUrl = `/animes/${anime.slug}?episode=${episodeNumber}`

    const notificationPayload = JSON.stringify({
      title: notificationTitle,
      body: notificationMessage,
      icon: '/favicon.svg',
      badge: '/favicon.svg',
      image: notificationImage,
      data: {
        url: notificationUrl
      }
    })

    // Enviar notificações para todos os usuários inscritos
    const results = await Promise.all(
      anime.animeSubscriptions.map(async (subscription) => {
        const pushSubscription = subscription.user.pushSubscription
        
        if (!pushSubscription) {
          return { 
            success: false, 
            userId: subscription.userId, 
            error: 'Sem inscrição push' 
          }
        }

        try {
          const pushConfig = {
            endpoint: pushSubscription.endpoint,
            keys: {
              p256dh: pushSubscription.p256dh,
              auth: pushSubscription.auth
            }
          }

          await webpush.sendNotification(pushConfig, notificationPayload)
          return { success: true, userId: subscription.userId }
        } catch (error: any) {
          console.error(`Erro ao enviar notificação para usuário ${subscription.userId}:`, error)
          
          // Se a inscrição expirou ou é inválida, remover
          if (error.statusCode === 404 || error.statusCode === 410) {
            await prisma.pushSubscription.delete({
              where: { userId: subscription.userId }
            })
          }
          
          return { 
            success: false, 
            userId: subscription.userId, 
            error: error.message 
          }
        }
      })
    )

    const successCount = results.filter(r => r.success).length
    
    return {
      success: true,
      message: `Notificações enviadas com sucesso para ${successCount} de ${results.length} usuários`,
      results
    }
  } catch (error) {
    console.error('Erro ao enviar notificações:', error)
    throw error
  }
}
