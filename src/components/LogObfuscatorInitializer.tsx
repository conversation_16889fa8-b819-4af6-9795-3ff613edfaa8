'use client'

import { useEffect } from 'react'
import { initLogObfuscator } from '@/lib/logObfuscator'

/**
 * Componente que inicializa o obfuscador de logs
 * Este componente deve ser adicionado ao layout principal
 * para garantir que o obfuscador seja carregado em todas as páginas
 */
export default function LogObfuscatorInitializer() {
  useEffect(() => {
    // Inicializar o obfuscador de logs apenas no lado do cliente
    initLogObfuscator()
    
    // Não é necessário limpar, pois queremos que o obfuscador permaneça ativo
  }, [])
  
  // Este componente não renderiza nada visível
  return null
}
