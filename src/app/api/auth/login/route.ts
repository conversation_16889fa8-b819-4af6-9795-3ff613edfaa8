import { NextResponse } from 'next/server'
import { generateToken } from '@/lib/jwt'

interface LoginRequest {
  email: string
  password: string
}

// Credenciais de admin via variáveis de ambiente
const ADMIN_USER = {
  id: '1',
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  password: process.env.ADMIN_PASSWORD || 'admin123',
  role: 'admin' as const,
}

export async function POST(request: Request) {
  try {
    const body: LoginRequest = await request.json()

    // Validação básica
    if (!body.email || !body.password) {
      console.log('Tentativa de login sem email ou senha')
      return NextResponse.json(
        { error: 'Email e senha são obrigatórios' },
        { status: 400 }
      )
    }

    // Log de tentativa de login (sem mostrar a senha)
    console.log(`Tentativa de login para: ${body.email}`)

    // Verificar se as variáveis de ambiente estão configuradas
    if (!process.env.ADMIN_EMAIL || !process.env.ADMIN_PASSWORD) {
      console.error('ERRO: Variáveis de ambiente ADMIN_EMAIL ou ADMIN_PASSWORD não configuradas!')
      return NextResponse.json(
        { error: 'Configuração do servidor incompleta' },
        { status: 500 }
      )
    }

    // Verifica credenciais
    if (
      body.email !== ADMIN_USER.email ||
      body.password !== ADMIN_USER.password
    ) {
      console.log(`Login falhado para: ${body.email}`)
      return NextResponse.json(
        { error: 'Email ou senha inválidos' },
        { status: 401 }
      )
    }

    console.log(`Login bem-sucedido para: ${body.email}`)

    // Gera token JWT
    const token = generateToken({
      id: ADMIN_USER.id,
      email: ADMIN_USER.email,
      role: ADMIN_USER.role,
    })

    // Retorna token
    return NextResponse.json(
      { token },
      {
        status: 200,
        headers: {
          'Set-Cookie': `admin_token=${token}; Path=/; HttpOnly; SameSite=Strict; Max-Age=86400`,
        },
      }
    )
  } catch (error) {
    console.error('Erro no login:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}