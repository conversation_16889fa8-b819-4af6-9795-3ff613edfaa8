/**
 * Utilitário para ocultar referências sensíveis nos logs do console
 * 
 * Este módulo substitui os métodos nativos do console para ocultar
 * referências a determinados termos sensíveis antes de exibi-los.
 */

// Lista de termos a serem ocultados
const SENSITIVE_TERMS = [
  'animefire',
  'anime fire',
  'AnimeFire',
  'Anime Fire',
  'ANIMEFIRE',
  'ANIME FIRE',
  'animefire.plus',
  'animefire.net',
  'AnimeFire.plus',
  'AnimeFire.net',
];

// Função para ocultar termos sensíveis em uma string
function obfuscateString(input: any): any {
  if (typeof input !== 'string') {
    return input;
  }
  
  let result = input;
  
  // Substituir cada termo sensível
  SENSITIVE_TERMS.forEach(term => {
    // Criar uma expressão regular para capturar o termo com case insensitive
    const regex = new RegExp(term, 'gi');
    
    // Substituir com asteriscos ou um termo genérico
    result = result.replace(regex, '********');
  });
  
  // Ocultar URLs específicas
  result = result.replace(/https?:\/\/animefire\.plus[^\s"')]*|https?:\/\/animefire\.net[^\s"')]*|https?:\/\/15\.229\.117\.80[^\s"')]*|https?:\/\/api-animefire[^\s"')]*|https?:\/\/anime-fire[^\s"')]*|https?:\/\/animefire[^\s"')]*/gi, 'https://source.domain/***');
  
  return result;
}

// Função para ocultar termos sensíveis em objetos
function obfuscateObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (typeof obj === 'string') {
    return obfuscateString(obj);
  }
  
  if (typeof obj === 'object') {
    if (Array.isArray(obj)) {
      return obj.map(item => obfuscateObject(item));
    }
    
    const result: Record<string, any> = {};
    for (const key in obj) {
      // Ocultar também chaves que contêm termos sensíveis
      const newKey = obfuscateString(key);
      result[newKey] = obfuscateObject(obj[key]);
    }
    return result;
  }
  
  return obj;
}

// Função para processar argumentos de log
function processLogArgs(args: any[]): any[] {
  return args.map(arg => {
    if (typeof arg === 'string') {
      return obfuscateString(arg);
    } else if (typeof arg === 'object') {
      return obfuscateObject(arg);
    }
    return arg;
  });
}

// Armazenar as funções originais do console
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug,
};

// Função para inicializar o obfuscador
export function initLogObfuscator() {
  if (typeof window !== 'undefined') {
    // Substituir os métodos do console no navegador
    console.log = function(...args: any[]) {
      originalConsole.log.apply(console, processLogArgs(args));
    };
    
    console.error = function(...args: any[]) {
      originalConsole.error.apply(console, processLogArgs(args));
    };
    
    console.warn = function(...args: any[]) {
      originalConsole.warn.apply(console, processLogArgs(args));
    };
    
    console.info = function(...args: any[]) {
      originalConsole.info.apply(console, processLogArgs(args));
    };
    
    console.debug = function(...args: any[]) {
      originalConsole.debug.apply(console, processLogArgs(args));
    };
    
    console.log('Console log obfuscator initialized');
  }
}

// Função para restaurar os métodos originais do console
export function restoreOriginalConsole() {
  if (typeof window !== 'undefined') {
    console.log = originalConsole.log;
    console.error = originalConsole.error;
    console.warn = originalConsole.warn;
    console.info = originalConsole.info;
    console.debug = originalConsole.debug;
  }
}
