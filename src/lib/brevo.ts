import axios from 'axios';

// Função para enviar email usando a API REST do Brevo
export const sendEmail = async ({
  to,
  subject,
  htmlContent,
  fromName = 'AnimesZera',
  fromEmail = process.env.EMAIL_FROM || '<EMAIL>',
}: {
  to: { email: string; name?: string }[];
  subject: string;
  htmlContent: string;
  fromName?: string;
  fromEmail?: string;
}) => {
  try {
    const apiKey = process.env.BREVO_API_KEY;

    if (!apiKey) {
      throw new Error('BREVO_API_KEY não está configurada nas variáveis de ambiente');
    }

    // Log para depuração (não inclui a chave completa por segurança)
    console.log('Enviando email via Brevo API');
    console.log('- De:', fromEmail);
    console.log('- Para:', to.map(recipient => recipient.email).join(', '));
    console.log('- Assunto:', subject);
    console.log('- API Key (primeiros 4 caracteres):', apiKey.substring(0, 4) + '...');

    const payload = {
      sender: { name: fromName, email: fromEmail },
      to,
      subject,
      htmlContent,
    };

    const headers = {
      'accept': 'application/json',
      'api-key': apiKey,
      'content-type': 'application/json',
    };

    // Log do payload (sem informações sensíveis)
    console.log('- Payload:', JSON.stringify({
      ...payload,
      htmlContent: htmlContent.substring(0, 50) + '...' // Truncado para o log
    }));

    const response = await axios.post(
      'https://api.brevo.com/v3/smtp/email',
      payload,
      { headers }
    );

    console.log('- Resposta da API:', response.status, response.statusText);
    if (response.data) {
      console.log('- MessageId:', response.data.messageId || 'N/A');
    }

    return {
      success: true,
      messageId: response.data?.messageId || 'sent'
    };
  } catch (error: any) {
    console.error('Erro ao enviar email via Brevo:', error.message);

    // Log detalhado para erros de API
    if (error.response) {
      console.error('- Status:', error.response.status);
      console.error('- Dados:', JSON.stringify(error.response.data));
      console.error('- Headers:', JSON.stringify(error.response.headers));
    }

    throw error;
  }
};
