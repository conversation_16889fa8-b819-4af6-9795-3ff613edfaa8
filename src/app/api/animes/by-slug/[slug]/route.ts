import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug

    // Busca o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: {
        slug: decodeURIComponent(slug)
      },
      include: {
        episodes: {
          orderBy: {
            number: 'asc'
          }
        }
      }
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    return NextResponse.json(anime)
  } catch (error) {
    console.error('Error fetching anime by slug:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar anime' },
      { status: 500 }
    )
  }
} 