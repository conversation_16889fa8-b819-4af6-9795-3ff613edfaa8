'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'

interface Episode {
  episodeNumber: number
  videoUrl: string
  sourceType: string
}

interface FetchResult {
  totalEpisodes: number
  episodesFetched: number
  episodesFound: number
  episodes: Episode[]
}

export default function AnimeMultipleEpisodesFetcher({ animeId }: { animeId: string }) {
  const [animeUrl, setAnimeUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [fetchResult, setFetchResult] = useState<FetchResult | null>(null)
  const [selectedEpisodes, setSelectedEpisodes] = useState<Episode[]>([])
  const [maxEpisodes, setMaxEpisodes] = useState<number | ''>('')
  const router = useRouter()

  const handleFetchEpisodes = async () => {
    if (!animeUrl.trim()) {
      toast.error('Por favor, insira a URL do anime')
      return
    }

    // Validar se a URL contém '/animes/'
    if (!animeUrl.includes('/animes/')) {
      toast.error('A URL deve ser de uma página de anime (contendo /animes/)')
      return
    }

    setIsLoading(true)
    setFetchResult(null)
    setSelectedEpisodes([])

    try {
      const maxParam = maxEpisodes ? `&max=${maxEpisodes}` : ''
      const response = await fetch(`/api/fetch-multiple-episodes?url=${encodeURIComponent(animeUrl)}${maxParam}`)

      if (!response.ok) {
        throw new Error(`Erro ao buscar episódios: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      setFetchResult(data)

      // Selecionar todos os episódios por padrão
      setSelectedEpisodes(data.episodes)

      toast.success(`Encontrados ${data.episodesFound} episódios!`)
    } catch (error) {
      console.error('Erro ao buscar episódios:', error)
      toast.error('Erro ao buscar episódios')
    } finally {
      setIsLoading(false)
    }
  }

  const toggleEpisodeSelection = (episode: Episode) => {
    if (selectedEpisodes.some(e => e.episodeNumber === episode.episodeNumber)) {
      setSelectedEpisodes(selectedEpisodes.filter(e => e.episodeNumber !== episode.episodeNumber))
    } else {
      setSelectedEpisodes([...selectedEpisodes, episode])
    }
  }

  const selectAllEpisodes = () => {
    if (fetchResult) {
      setSelectedEpisodes(fetchResult.episodes)
    }
  }

  const deselectAllEpisodes = () => {
    setSelectedEpisodes([])
  }

  const handleAddEpisodes = async () => {
    if (selectedEpisodes.length === 0) {
      toast.error('Selecione pelo menos um episódio para adicionar')
      return
    }

    setIsLoading(true)

    try {
      // Adicionar episódios em lote
      const response = await fetch(`/api/episodes/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          animeId,
          episodes: selectedEpisodes.map(episode => ({
            number: episode.episodeNumber,
            videoUrl: episode.videoUrl,
            sourceType: episode.sourceType,
          })),
        }),
      })

      if (!response.ok) {
        throw new Error(`Erro ao adicionar episódios: ${response.status} ${response.statusText}`)
      }

      toast.success(`${selectedEpisodes.length} episódios adicionados com sucesso!`)
      router.refresh()
    } catch (error) {
      console.error('Erro ao adicionar episódios:', error)
      toast.error('Erro ao adicionar episódios')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-gray-800 p-6 rounded-lg mb-6">
      <h2 className="text-xl font-bold mb-4">Buscar Múltiplos Episódios</h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            URL do Anime (formato: https://animefire.plus/animes/nome-do-anime/)
          </label>
          <input
            type="text"
            value={animeUrl}
            onChange={(e) => setAnimeUrl(e.target.value)}
            placeholder="Cole a URL da página do anime aqui"
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Máximo de Episódios (opcional)
          </label>
          <input
            type="number"
            value={maxEpisodes}
            onChange={(e) => setMaxEpisodes(e.target.value === '' ? '' : parseInt(e.target.value))}
            placeholder="Deixe em branco para buscar todos"
            min="1"
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <button
            onClick={handleFetchEpisodes}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Buscando...' : 'Buscar Episódios'}
          </button>
        </div>
      </div>

      {fetchResult && (
        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">
              Episódios Encontrados: {fetchResult.episodesFound} de {fetchResult.totalEpisodes}
            </h3>
            <div className="space-x-2">
              <button
                onClick={selectAllEpisodes}
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded-md text-sm"
              >
                Selecionar Todos
              </button>
              <button
                onClick={deselectAllEpisodes}
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded-md text-sm"
              >
                Desmarcar Todos
              </button>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto bg-gray-900 rounded-lg p-4">
            {fetchResult.episodes.length > 0 ? (
              <div className="space-y-2">
                {fetchResult.episodes.map((episode) => (
                  <div
                    key={episode.episodeNumber}
                    className="flex items-center p-2 hover:bg-gray-800 rounded-lg"
                  >
                    <input
                      type="checkbox"
                      checked={selectedEpisodes.some(e => e.episodeNumber === episode.episodeNumber)}
                      onChange={() => toggleEpisodeSelection(episode)}
                      className="mr-3 h-5 w-5 text-blue-600 rounded"
                    />
                    <div className="flex-1">
                      <p className="font-medium">Episódio {episode.episodeNumber}</p>
                      <p className="text-sm text-gray-400 truncate">{episode.videoUrl}</p>
                      <p className="text-xs text-gray-500">Tipo: {episode.sourceType}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-gray-400 py-4">Nenhum episódio encontrado</p>
            )}
          </div>

          {selectedEpisodes.length > 0 && (
            <div className="mt-4">
              <button
                onClick={handleAddEpisodes}
                disabled={isLoading}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Adicionando...' : `Adicionar ${selectedEpisodes.length} Episódios`}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
