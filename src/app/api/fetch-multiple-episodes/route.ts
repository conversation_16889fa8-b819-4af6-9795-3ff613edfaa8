import { NextResponse } from 'next/server'
import * as cheerio from 'cheerio'

// Função para extrair a URL do Blogger da página do anime
async function extractBloggerUrlFromAnimePage(animeUrl: string, episodeNumber: number): Promise<string | null> {
  try {
    console.log(`Extracting Blogger URL for episode ${episodeNumber} from anime page:`, animeUrl)

    // Construir a URL do episódio específico
    const episodeUrl = animeUrl.replace(/\/\d+$/, `/${episodeNumber}`)
    console.log(`Constructed episode URL: ${episodeUrl}`)

    // Fetch the anime page
    const response = await fetch(episodeUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
      }
    })

    if (!response.ok) {
      console.log(`Failed to fetch anime page for episode ${episodeNumber}: ${response.status} ${response.statusText}`)
      return null
    }

    // Get the HTML content
    const html = await response.text()
    console.log(`Anime page HTML length for episode ${episodeNumber}:`, html.length)

    // Look for Blogger URL in the HTML
    const bloggerMatch = html.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/)
    if (bloggerMatch && bloggerMatch[0]) {
      console.log(`Found Blogger URL for episode ${episodeNumber}:`, bloggerMatch[0])
      return bloggerMatch[0]
    }

    // Look for iframe with Blogger URL
    const iframeMatch = html.match(/<iframe[^>]*src=["'](https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+)["'][^>]*>/)
    if (iframeMatch && iframeMatch[1]) {
      console.log(`Found Blogger URL in iframe for episode ${episodeNumber}:`, iframeMatch[1])
      return iframeMatch[1]
    }

    // Look for data attributes that might contain Blogger URL
    const dataMatch = html.match(/data-[^=]*=["'](https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+)["']/)
    if (dataMatch && dataMatch[1]) {
      console.log(`Found Blogger URL in data attribute for episode ${episodeNumber}:`, dataMatch[1])
      return dataMatch[1]
    }

    console.log(`No Blogger URL found for episode ${episodeNumber}`)
    return null
  } catch (error) {
    console.error(`Error extracting Blogger URL for episode ${episodeNumber}:`, error)
    return null
  }
}

// Função para extrair o número total de episódios da página do anime
async function extractTotalEpisodes(animeUrl: string): Promise<number> {
  try {
    console.log('Extracting total episodes from anime page:', animeUrl)

    // Normalizar a URL para garantir que estamos na página principal do anime
    // Remover qualquer número de episódio no final
    let baseAnimeUrl = animeUrl.replace(/\/\d+$/, '')

    // Garantir que a URL termina com uma barra
    if (!baseAnimeUrl.endsWith('/')) {
      baseAnimeUrl += '/'
    }

    // Primeiro, tentar acessar a página principal do anime (sem número de episódio)
    console.log('Fetching main anime page:', baseAnimeUrl)
    let response = await fetch(baseAnimeUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
      }
    })

    // Se a página principal não estiver disponível, tentar o primeiro episódio
    if (!response.ok) {
      console.log(`Main anime page not available, trying first episode`)
      // Adicionar "1" para acessar o primeiro episódio
      const firstEpisodeUrl = baseAnimeUrl + '1'
      console.log('Trying first episode URL:', firstEpisodeUrl)

      response = await fetch(firstEpisodeUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        }
      })

      if (!response.ok) {
        console.log(`Failed to fetch anime page: ${response.status} ${response.statusText}`)
        return 12 // Retorna 12 como fallback (valor comum para animes)
      }
    }

    // Get the HTML content
    const html = await response.text()
    console.log(`Anime page HTML length: ${html.length}`)

    // Carregar o HTML com cheerio
    const $ = cheerio.load(html)

    // Método 1: Procurar por elementos de navegação de episódios específicos do AnimeFirePlus
    const episodeLinks = $('.episodes-list a, .episodes a, .list-episodes a, .episodios a, .episodes-container a')

    if (episodeLinks.length > 0) {
      console.log(`Found ${episodeLinks.length} episode links`)

      // Extrair os números dos episódios para encontrar o maior
      let highestEpisode = 0
      episodeLinks.each((i, el) => {
        const href = $(el).attr('href')
        if (href) {
          const episodeMatch = href.match(/\/(\d+)(?:\?|$)/)
          if (episodeMatch && episodeMatch[1]) {
            const episodeNumber = parseInt(episodeMatch[1])
            if (!isNaN(episodeNumber) && episodeNumber > highestEpisode) {
              highestEpisode = episodeNumber
            }
          }
        }
      })

      if (highestEpisode > 0) {
        console.log(`Highest episode number found: ${highestEpisode}`)
        return highestEpisode
      }

      return episodeLinks.length
    }

    // Método 2: Procurar por seletor de episódios
    const episodeOptions = $('select.form-select option, select.episodes-selector option')

    if (episodeOptions.length > 0) {
      console.log(`Found ${episodeOptions.length} episode options`)

      // Extrair os números dos episódios para encontrar o maior
      let highestEpisode = 0
      episodeOptions.each((i, el) => {
        const value = $(el).attr('value')
        if (value) {
          const episodeNumber = parseInt(value)
          if (!isNaN(episodeNumber) && episodeNumber > highestEpisode) {
            highestEpisode = episodeNumber
          }
        }
      })

      if (highestEpisode > 0) {
        console.log(`Highest episode number found in options: ${highestEpisode}`)
        return highestEpisode
      }

      return episodeOptions.length
    }

    // Método 3: Procurar por elementos com data-id ou data-episode
    const dataElements = $('[data-id], [data-episode]')
    if (dataElements.length > 0) {
      console.log(`Found ${dataElements.length} elements with data-id or data-episode`)

      // Extrair os números dos episódios para encontrar o maior
      let highestEpisode = 0
      dataElements.each((i, el) => {
        const dataId = $(el).attr('data-id') || $(el).attr('data-episode')
        if (dataId) {
          const episodeNumber = parseInt(dataId)
          if (!isNaN(episodeNumber) && episodeNumber > highestEpisode) {
            highestEpisode = episodeNumber
          }
        }
      })

      if (highestEpisode > 0) {
        console.log(`Highest episode number found in data attributes: ${highestEpisode}`)
        return highestEpisode
      }
    }

    // Método 4: Procurar por links com padrões de episódios
    const allLinks = $('a')
    let episodeNumbers = new Set<number>()

    allLinks.each((i, el) => {
      const href = $(el).attr('href')
      if (href) {
        // Procurar por padrões como /animes/nome-do-anime/12
        const episodeMatch = href.match(/\/animes\/[^\/]+\/(\d+)(?:\?|$)/)
        if (episodeMatch && episodeMatch[1]) {
          const episodeNumber = parseInt(episodeMatch[1])
          if (!isNaN(episodeNumber)) {
            episodeNumbers.add(episodeNumber)
          }
        }
      }
    })

    if (episodeNumbers.size > 0) {
      const highestEpisode = Math.max(...Array.from(episodeNumbers))
      console.log(`Found ${episodeNumbers.size} unique episode numbers, highest: ${highestEpisode}`)
      return highestEpisode
    }

    // Método 5: Procurar por números de episódios no texto
    const episodePatterns = [
      /Episódios?:\s*(\d+)/i,
      /Total\s+de\s+(\d+)\s+episódios/i,
      /(\d+)\s+episódios/i,
      /Episódio\s+(\d+)/i,
      /Ep\.\s*(\d+)/i
    ]

    for (const pattern of episodePatterns) {
      const match = html.match(pattern)
      if (match && match[1]) {
        const count = parseInt(match[1])
        if (!isNaN(count) && count > 0) {
          console.log(`Found episode count in text: ${count}`)
          return count
        }
      }
    }

    // Método 6: Verificar se há botões de navegação que indicam o número total
    const paginationText = $('.pagination, .paginacao').text()
    const paginationMatch = paginationText.match(/de\s+(\d+)/i) || paginationText.match(/(\d+)\s*páginas?/i)
    if (paginationMatch && paginationMatch[1]) {
      const count = parseInt(paginationMatch[1])
      if (!isNaN(count) && count > 0) {
        console.log(`Found episode count in pagination: ${count}`)
        return count
      }
    }

    // Método 7: Tentar acessar episódios sequencialmente para determinar o total
    // Este método é mais lento, mas mais preciso
    console.log('Trying sequential episode detection...')

    // Primeiro, vamos verificar se o episódio 1 existe
    try {
      const firstEpisodeUrl = `${baseAnimeUrl}1`
      const firstEpisodeResponse = await fetch(firstEpisodeUrl, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        }
      })

      if (!firstEpisodeResponse.ok) {
        console.log('First episode not found, defaulting to 24 episodes')
        return 24
      }
    } catch (error) {
      // Ignorar erros
    }

    // Vamos tentar uma abordagem de busca binária para encontrar o último episódio
    // Começamos com um intervalo grande e vamos reduzindo
    let low = 1
    let high = 100  // Assumimos que nenhum anime terá mais de 100 episódios
    let lastConfirmedEpisode = 1

    while (low <= high) {
      const mid = Math.floor((low + high) / 2)
      console.log(`Testing episode ${mid}...`)

      try {
        const testUrl = `${baseAnimeUrl}${mid}`
        const testResponse = await fetch(testUrl, {
          method: 'HEAD',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          },
          // Adicionamos um timeout para evitar que a requisição demore muito
          signal: AbortSignal.timeout(3000)
        })

        if (testResponse.ok) {
          // O episódio existe, então o último episódio é pelo menos mid
          lastConfirmedEpisode = Math.max(lastConfirmedEpisode, mid)
          low = mid + 1
        } else {
          // O episódio não existe, então o último episódio é menor que mid
          high = mid - 1
        }
      } catch (error) {
        // Se ocorrer um erro (como timeout), assumimos que o episódio não existe
        high = mid - 1
      }
    }

    // Verificamos se encontramos pelo menos um episódio
    if (lastConfirmedEpisode > 1) {
      console.log(`Last confirmed episode: ${lastConfirmedEpisode}`)
      return lastConfirmedEpisode
    }

    // Se a busca binária falhar, tentamos alguns valores comuns
    const commonEpisodeCounts = [24, 25, 13, 12, 26]
    for (const count of commonEpisodeCounts) {
      const testUrl = `${baseAnimeUrl}${count}`
      try {
        const testResponse = await fetch(testUrl, {
          method: 'HEAD',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          },
          signal: AbortSignal.timeout(3000)
        })

        if (testResponse.ok) {
          console.log(`Episode ${count} exists, assuming it's the last episode`)
          return count
        }
      } catch (error) {
        // Ignorar erros e continuar tentando
      }
    }

    // Se não conseguiu determinar, retorna um valor padrão mais alto
    console.log('Could not determine total episodes, defaulting to 24')
    return 24  // A maioria dos animes tem pelo menos 12 episódios, mas vamos usar 24 para garantir
  } catch (error) {
    console.error('Error extracting total episodes:', error)
    return 12 // Retorna 12 como fallback em caso de erro
  }
}

// Função para extrair a URL de um episódio específico
async function fetchEpisodeUrl(animeUrl: string, episodeNumber: number): Promise<{ episodeNumber: number, videoUrl: string | null, sourceType: string | null }> {
  try {
    console.log(`Fetching URL for episode ${episodeNumber}`)

    // Normalizar a URL base do anime
    let baseAnimeUrl = animeUrl.replace(/\/\d+$/, '')

    // Garantir que a URL termina com uma barra
    if (!baseAnimeUrl.endsWith('/')) {
      baseAnimeUrl += '/'
    }

    // Construir a URL do episódio específico
    const episodeUrl = baseAnimeUrl + episodeNumber

    console.log(`Constructed episode URL: ${episodeUrl}`)

    // Método 1: Tentar extrair a URL do Blogger diretamente da página do anime
    console.log(`Trying Method 1: Direct extraction from anime page`)
    const bloggerUrl = await extractBloggerUrlFromAnimePage(episodeUrl, episodeNumber)

    if (bloggerUrl) {
      console.log(`Method 1 successful: Found Blogger URL for episode ${episodeNumber}`)
      return {
        episodeNumber,
        videoUrl: bloggerUrl,
        sourceType: 'blogger'
      }
    }

    // Método 2: Tentar extrair a URL usando a API de fetch-episode-url
    console.log(`Trying Method 2: Using fetch-episode-url API`)
    try {
      // Construir a URL absoluta para a API (mesma origem)
      const baseUrl = process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : 'http://localhost:3000';
      const apiUrl = `${baseUrl}/api/fetch-episode-url?url=${encodeURIComponent(episodeUrl)}`
      console.log(`Calling API: ${apiUrl}`)

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(apiUrl, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json()

        if (data.videoUrl) {
          console.log(`Method 2 successful: Found video URL for episode ${episodeNumber}`)
          return {
            episodeNumber,
            videoUrl: data.videoUrl,
            sourceType: data.sourceType || 'unknown'
          }
        }
      }
    } catch (error) {
      console.log(`Method 2 failed for episode ${episodeNumber}:`, error)
      // Continue to next method
    }

    // Método 3: Tentar extrair a URL da página de vídeo
    console.log(`Trying Method 3: Extracting from video page`)
    try {
      // Converter para URL da página de vídeo
      const videoPageUrl = episodeUrl.replace('/animes/', '/video/') + '?tempsubs=1'
      console.log(`Video page URL: ${videoPageUrl}`)

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const videoPageResponse = await fetch(videoPageUrl, {
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        }
      });

      clearTimeout(timeoutId);

      if (videoPageResponse.ok) {
        const html = await videoPageResponse.text();

        // Procurar por URLs do Blogger
        const bloggerMatch = html.match(/https:\/\/www\.blogger\.com\/video\.g\?token=[^"'&]+/);
        if (bloggerMatch && bloggerMatch[0]) {
          console.log(`Method 3 successful: Found Blogger URL in video page for episode ${episodeNumber}`);
          return {
            episodeNumber,
            videoUrl: bloggerMatch[0],
            sourceType: 'blogger'
          };
        }

        // Procurar por URLs do LightSpeed
        const lightSpeedMatch = html.match(/https:\/\/lightspeedst\.net\/[^"'\s]+\.mp4/);
        if (lightSpeedMatch && lightSpeedMatch[0]) {
          console.log(`Method 3 successful: Found LightSpeed URL in video page for episode ${episodeNumber}`);
          return {
            episodeNumber,
            videoUrl: lightSpeedMatch[0],
            sourceType: 'direct_mp4'
          };
        }
      }
    } catch (error) {
      console.log(`Method 3 failed for episode ${episodeNumber}:`, error)
      // Continue to next method
    }

    // Método 4: Tentar gerar URLs do LightSpeed com base no nome do anime
    console.log(`Trying Method 4: Generating LightSpeed URLs`)
    try {
      // Extrair o slug do anime da URL
      const animeSlug = baseAnimeUrl.split('/').pop() || '';
      if (animeSlug) {
        // Gerar URLs do LightSpeed com base no slug e no número do episódio
        const lightSpeedUrls = generateLightSpeedUrls(animeSlug, episodeNumber);

        // Verificar cada URL gerada
        for (const url of lightSpeedUrls) {
          try {
            console.log(`Testing LightSpeed URL: ${url}`);
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch(url, {
              method: 'HEAD',
              signal: controller.signal,
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              }
            });

            clearTimeout(timeoutId);

            if (response.ok) {
              console.log(`Method 4 successful: Found working LightSpeed URL for episode ${episodeNumber}`);
              return {
                episodeNumber,
                videoUrl: url,
                sourceType: 'direct_mp4'
              };
            }
          } catch (error) {
            // Ignorar erros e continuar tentando
          }
        }
      }
    } catch (error) {
      console.log(`Method 4 failed for episode ${episodeNumber}:`, error)
      // Continue to next method
    }

    // Se nenhum método funcionou, retornar null
    console.log(`All methods failed for episode ${episodeNumber}`);
    return {
      episodeNumber,
      videoUrl: null,
      sourceType: null
    }
  } catch (error) {
    console.error(`Error fetching episode ${episodeNumber}:`, error)
    return {
      episodeNumber,
      videoUrl: null,
      sourceType: null
    }
  }
}

// Função para gerar URLs do LightSpeed com base no nome do anime e número do episódio
function generateLightSpeedUrls(animeSlug: string, episodeNumber: number): string[] {
  const servers = ['s1', 's2', 's3', 's4', 's5', 's6'];
  const formats = ['mp4', 'mp4_temp'];
  const qualities = ['720p', '480p', '360p'];

  const urls: string[] = [];

  // Gerar URLs para todas as combinações
  for (const server of servers) {
    for (const format of formats) {
      // Formato 1: /s1/mp4/anime-slug/hd/1.mp4
      urls.push(`https://lightspeedst.net/${server}/${format}/${animeSlug}/hd/${episodeNumber}.mp4`);

      // Formato 2: /s1/mp4/anime-slug/sd/1.mp4
      urls.push(`https://lightspeedst.net/${server}/${format}/${animeSlug}/sd/${episodeNumber}.mp4`);

      // Formato 3: /s1/mp4/anime-slug/1/720p.mp4
      for (const quality of qualities) {
        urls.push(`https://lightspeedst.net/${server}/${format}/${animeSlug}/${episodeNumber}/${quality}.mp4`);
      }

      // Formato 4: /s1/mp4/anime-slug-dublado/hd/1.mp4 (para versões dubladas)
      urls.push(`https://lightspeedst.net/${server}/${format}/${animeSlug}-dublado/hd/${episodeNumber}.mp4`);
      urls.push(`https://lightspeedst.net/${server}/${format}/${animeSlug}-dublado/sd/${episodeNumber}.mp4`);
    }
  }

  return urls;
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const url = searchParams.get('url')
  const maxEpisodes = searchParams.get('max') ? parseInt(searchParams.get('max')!) : undefined
  const batchSize = searchParams.get('batch_size') ? parseInt(searchParams.get('batch_size')!) : 2
  const startEpisode = searchParams.get('start') ? parseInt(searchParams.get('start')!) : 1
  const specificEpisodes = searchParams.get('episodes') ? searchParams.get('episodes')!.split(',').map(e => parseInt(e.trim())) : undefined

  if (!url) {
    return NextResponse.json(
      { error: 'URL parameter is required' },
      { status: 400 }
    )
  }

  try {
    console.log('Fetching multiple episodes for URL:', url)

    // Verificar se a URL é de uma página de anime ou converter para o formato correto
    let animeUrl = url
    if (!url.includes('/animes/')) {
      // Tentar converter de /video/ para /animes/
      animeUrl = url.replace('/video/', '/animes/').replace(/\?.*$/, '')
      console.log('Converted video URL to anime URL:', animeUrl)

      if (!animeUrl.includes('/animes/')) {
        return NextResponse.json(
          { error: 'URL must be an anime page URL (containing /animes/) or a video URL that can be converted' },
          { status: 400 }
        )
      }
    }

    // Normalizar a URL para garantir que funciona corretamente
    let normalizedUrl = animeUrl

    // Remover parâmetros de consulta se existirem
    normalizedUrl = normalizedUrl.split('?')[0]

    // Remover qualquer número de episódio no final
    normalizedUrl = normalizedUrl.replace(/\/\d+$/, '')

    // Garantir que a URL termina com uma barra
    if (!normalizedUrl.endsWith('/')) {
      normalizedUrl += '/'
    }

    console.log('Normalized URL:', normalizedUrl)

    // Se foram especificados episódios específicos, usá-los
    if (specificEpisodes && specificEpisodes.length > 0) {
      console.log(`Fetching specific episodes: ${specificEpisodes.join(', ')}`)

      // Processar em lotes para evitar sobrecarga
      const results = []
      for (let i = 0; i < specificEpisodes.length; i += batchSize) {
        const batch = specificEpisodes.slice(i, i + batchSize)
        console.log(`Processing batch ${i/batchSize + 1}: episodes ${batch.join(', ')}`)

        const batchPromises = batch.map(episodeNumber =>
          fetchEpisodeUrl(normalizedUrl, episodeNumber)
        )

        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults)

        // Pausa entre os lotes para evitar sobrecarga
        if (i + batchSize < specificEpisodes.length) {
          console.log('Pausing between batches (3 seconds)...')
          await new Promise(resolve => setTimeout(resolve, 3000))
        }
      }

      // Filtrar episódios que não foram encontrados
      const validEpisodes = results.filter(episode => episode.videoUrl !== null)

      console.log(`Successfully fetched ${validEpisodes.length} out of ${specificEpisodes.length} specific episodes`)

      return NextResponse.json({
        totalEpisodes: specificEpisodes.length,
        episodesFetched: specificEpisodes.length,
        episodesFound: validEpisodes.length,
        episodes: validEpisodes
      })
    }

    // Caso contrário, extrair o número total de episódios e buscar todos
    const totalEpisodes = await extractTotalEpisodes(normalizedUrl)
    console.log(`Total episodes detected: ${totalEpisodes}`)

    // Limitar o número de episódios se necessário e respeitar o episódio inicial
    const endEpisode = maxEpisodes ? Math.min(startEpisode + maxEpisodes - 1, totalEpisodes) : totalEpisodes
    const episodesToFetch = endEpisode - startEpisode + 1

    console.log(`Will fetch ${episodesToFetch} episodes (from ${startEpisode} to ${endEpisode})`)

    // Extrair as URLs para cada episódio em lotes para evitar sobrecarga
    const results = []

    for (let i = startEpisode; i <= endEpisode; i += batchSize) {
      const batchEnd = Math.min(i + batchSize - 1, endEpisode)
      console.log(`Processing batch: episodes ${i} to ${batchEnd}`)

      const batchPromises = []
      for (let j = i; j <= batchEnd; j++) {
        batchPromises.push(fetchEpisodeUrl(normalizedUrl, j))
      }

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Pausa entre os lotes para evitar sobrecarga
      if (i + batchSize <= endEpisode) {
        console.log('Pausing between batches (3 seconds)...')
        await new Promise(resolve => setTimeout(resolve, 3000))
      }
    }

    // Filtrar episódios que não foram encontrados
    const validEpisodes = results.filter(episode => episode.videoUrl !== null)

    console.log(`Successfully fetched ${validEpisodes.length} out of ${episodesToFetch} episodes`)

    return NextResponse.json({
      totalEpisodes,
      episodesFetched: episodesToFetch,
      episodesFound: validEpisodes.length,
      episodes: validEpisodes
    })
  } catch (error) {
    console.error('Error fetching multiple episodes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch episodes' },
      { status: 500 }
    )
  }
}
