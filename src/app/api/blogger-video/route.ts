import { NextRequest, NextResponse } from 'next/server'
import * as cheerio from 'cheerio'

// Função para extrair as URLs de vídeo de diferentes qualidades do Blogger
async function extractBloggerVideoUrls(bloggerUrl: string): Promise<{ url: string, quality: string }[]> {
  try {
    console.log('Extraindo URLs de vídeo do Blogger:', bloggerUrl)
    
    // Buscar a página do Blogger
    const response = await fetch(bloggerUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      },
      cache: 'no-store'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const html = await response.text()
    const $ = cheerio.load(html)
    
    // Procurar por scripts que contenham as URLs de vídeo
    const scripts = $('script').toArray()
    const videoUrls: { url: string, quality: string }[] = []
    
    for (const script of scripts) {
      const content = $(script).html() || ''
      
      // Procurar por arrays de fontes de vídeo no formato JSON
      if (content.includes('streams') && content.includes('play_url')) {
        try {
          // Extrair o objeto JSON que contém as streams
          const jsonMatch = content.match(/var\s+VIDEO_CONFIG\s*=\s*({[\s\S]*?});/)
          
          if (jsonMatch && jsonMatch[1]) {
            const videoConfig = JSON.parse(jsonMatch[1])
            
            if (videoConfig.streams && Array.isArray(videoConfig.streams)) {
              // Processar cada stream disponível
              videoConfig.streams.forEach((stream: any) => {
                if (stream.play_url && typeof stream.play_url === 'string') {
                  // Determinar a qualidade com base na URL ou nas propriedades
                  let quality = 'unknown'
                  
                  if (stream.format_id) {
                    // Usar format_id como indicador de qualidade se disponível
                    quality = stream.format_id
                  } else if (stream.play_url.includes('itag=22')) {
                    quality = '720p'
                  } else if (stream.play_url.includes('itag=18')) {
                    quality = '360p'
                  } else if (stream.play_url.includes('itag=43')) {
                    quality = '360p WebM'
                  } else if (stream.play_url.includes('itag=36')) {
                    quality = '240p'
                  } else if (stream.play_url.includes('itag=17')) {
                    quality = '144p'
                  } else if (stream.play_url.includes('itag=137')) {
                    quality = '1080p'
                  } else if (stream.play_url.includes('itag=136')) {
                    quality = '720p'
                  } else if (stream.play_url.includes('itag=135')) {
                    quality = '480p'
                  }
                  
                  videoUrls.push({
                    url: stream.play_url,
                    quality
                  })
                }
              })
            }
          }
        } catch (error) {
          console.error('Erro ao analisar JSON de vídeo do Blogger:', error)
        }
      }
    }
    
    // Ordenar as URLs por qualidade (da mais alta para a mais baixa)
    return videoUrls.sort((a, b) => {
      // Extrair os números das qualidades
      const getQualityNumber = (quality: string) => {
        const match = quality.match(/(\d+)p/)
        return match ? parseInt(match[1]) : 0
      }
      
      return getQualityNumber(b.quality) - getQualityNumber(a.quality)
    })
  } catch (error) {
    console.error('Erro ao extrair URLs de vídeo do Blogger:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const bloggerUrl = searchParams.get('url')
  const returnAll = searchParams.get('all') === 'true'

  if (!bloggerUrl) {
    return NextResponse.json({ error: 'URL do Blogger não fornecida' }, { status: 400 })
  }

  try {
    // Extrair todas as URLs de vídeo disponíveis
    const videoUrls = await extractBloggerVideoUrls(bloggerUrl)
    
    if (videoUrls.length === 0) {
      return NextResponse.json({ error: 'Nenhuma URL de vídeo encontrada' }, { status: 404 })
    }
    
    // Retornar todas as URLs ou apenas a de melhor qualidade
    if (returnAll) {
      return NextResponse.json({ videoUrls })
    } else {
      // Retornar apenas a URL de melhor qualidade (primeira da lista ordenada)
      return NextResponse.json({ 
        videoUrl: videoUrls[0].url,
        quality: videoUrls[0].quality,
        allQualities: videoUrls.map(item => ({
          quality: item.quality,
          url: item.url
        }))
      })
    }
  } catch (error) {
    console.error('Erro ao processar URL do Blogger:', error)
    return NextResponse.json({ error: 'Erro ao processar URL do Blogger' }, { status: 500 })
  }
}
