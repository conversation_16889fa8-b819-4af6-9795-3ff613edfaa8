import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'
import { authOptions } from '@/lib/auth'
import { generateAnimeUsername } from '@/lib/animeNames'

export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    console.log('Session:', session)
    
    if (!session?.user?.id) {
      console.log('Usuário não autenticado')
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const { username, avatar } = await request.json()
    console.log('Update data:', { username, avatar: avatar ? 'base64 image data' : 'no avatar' })

    if (!username || typeof username !== 'string' || username.trim().length === 0) {
      console.log('Nome de usuário inválido')
      return NextResponse.json({ error: 'Nome de usuário inválido' }, { status: 400 })
    }

    // Verifica se o avatar é uma string base64 válida
    if (avatar && !avatar.startsWith('data:image/')) {
      console.log('Avatar inválido')
      return NextResponse.json({ error: 'Avatar inválido' }, { status: 400 })
    }

    // Verifica se o usuário existe
    const existingUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    })
    console.log('Existing user:', existingUser)

    if (!existingUser) {
      console.log('Usuário não encontrado')
      return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 })
    }

    // Gera um nome de usuário aleatório se não existir
    const finalUsername = username?.trim() || generateAnimeUsername()
    console.log('Nome de usuário final:', finalUsername)

    // Atualiza o usuário
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        name: finalUsername,
        avatar: avatar || existingUser.avatar,
      },
    })
    console.log('Updated user:', { ...updatedUser, avatar: updatedUser.avatar ? 'base64 image data' : 'no avatar' })

    return NextResponse.json({
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      avatar: updatedUser.avatar,
    })
  } catch (error) {
    console.error('Erro ao atualizar perfil:', error)
    return NextResponse.json(
      { error: 'Erro ao atualizar perfil' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        name: true,
        email: true,
        image: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar perfil' },
      { status: 500 }
    );
  }
} 