'use client'

import { useState, useEffect } from 'react'
import { HandThumbUpIcon, HandThumbDownIcon } from '@heroicons/react/24/outline'
import { HandThumbUpIcon as HandThumbUpIconSolid, HandThumbDownIcon as HandThumbDownIconSolid } from '@heroicons/react/24/solid'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'

interface VoteButtonsProps {
  slug: string
}

export default function VoteButtons({ slug }: VoteButtonsProps) {
  const { data: session } = useSession()
  const [likes, setLikes] = useState(0)
  const [dislikes, setDislikes] = useState(0)
  const [userVote, setUserVote] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchVotes()
  }, [slug])

  const fetchVotes = async () => {
    try {
      const response = await fetch(`/api/animes/${slug}/vote`)
      if (!response.ok) {
        throw new Error('Erro ao buscar votos')
      }
      const data = await response.json()
      setLikes(data.likes)
      setDislikes(data.dislikes)
      setUserVote(data.userVote)
    } catch (error) {
      console.error('Error fetching votes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleVote = async (isLike: boolean) => {
    if (!session) {
      toast.error('Você precisa estar logado para votar')
      return
    }

    try {
      const response = await fetch(`/api/animes/${slug}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isLike }),
      })

      if (!response.ok) {
        throw new Error('Erro ao processar voto')
      }

      const data = await response.json()
      setLikes(data.likes)
      setDislikes(data.dislikes)
      setUserVote(data.vote?.isLike ?? null)
    } catch (error) {
      console.error('Error voting:', error)
      toast.error('Erro ao processar voto')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center space-x-4">
        <div className="animate-pulse flex space-x-4">
          <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
          <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-4">
      <button
        onClick={() => handleVote(true)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
          userVote === true
            ? 'bg-purple-600 text-white'
            : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
        }`}
      >
        {userVote === true ? (
          <HandThumbUpIconSolid className="w-5 h-5" />
        ) : (
          <HandThumbUpIcon className="w-5 h-5" />
        )}
        <span>{likes}</span>
      </button>

      <button
        onClick={() => handleVote(false)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
          userVote === false
            ? 'bg-purple-600 text-white'
            : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
        }`}
      >
        {userVote === false ? (
          <HandThumbDownIconSolid className="w-5 h-5" />
        ) : (
          <HandThumbDownIcon className="w-5 h-5" />
        )}
        <span>{dislikes}</span>
      </button>
    </div>
  )
} 