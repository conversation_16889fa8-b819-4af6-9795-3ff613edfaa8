import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { parseDate } from '@/lib/dateUtils';

// Função para verificar se a requisição é autorizada
async function isAuthorized(req: Request) {
  // Verificar se a requisição vem do Vercel Cron ou tem um token válido
  const authHeader = req.headers.get('authorization');
  console.log('Cabeçalho de autorização recebido:', authHeader);

  // Se estiver rodando no Vercel, verificar o cabeçalho de autorização do Vercel Cron
  if (process.env.VERCEL === '1') {
    const isVercelCron = req.headers.get('x-vercel-cron') === 'true';
    console.log('Requisição do Vercel Cron:', isVercelCron);
    if (isVercelCron) return true;
  }

  // Para desenvolvimento local ou chamadas manuais, verificar um token simples
  const token = process.env.CRON_SECRET || 'your-secret-token';
  console.log('Token esperado:', `Bearer ${token}`);

  const isValidToken = authHeader === `Bearer ${token}`;
  console.log('Token válido:', isValidToken);

  return isValidToken;
}

// Função para buscar animes em lançamento
async function getOngoingAnimes() {
  // Primeiro, vamos listar todos os animes e seus status para depuração
  const allAnimes = await prisma.anime.findMany({
    select: {
      id: true,
      title: true,
      status: true,
      totalEpisodes: true
    }
  });

  console.log("=== TODOS OS ANIMES E SEUS STATUS ===");
  allAnimes.forEach(anime => {
    console.log(`Anime: ${anime.title}, Status: "${anime.status}", Total Episódios: ${anime.totalEpisodes} (${typeof anime.totalEpisodes})`);
  });

  // Verificar especificamente o anime Katainaka
  const katainakaAnime = allAnimes.find(anime => anime.title.includes("Katainaka"));
  if (katainakaAnime) {
    console.log("=== DETALHES DO ANIME KATAINAKA ===");
    console.log(JSON.stringify(katainakaAnime, null, 2));

    // Buscar episódios deste anime
    const episodes = await prisma.episode.findMany({
      where: { animeId: katainakaAnime.id },
      orderBy: { number: 'asc' }
    });

    console.log(`Episódios existentes: ${episodes.map(ep => ep.number).join(', ')}`);
  } else {
    console.log("Anime Katainaka não encontrado no banco de dados");
  }

  // Verificar se há animes com status similar a "Em Andamento" ou "Em Lançamento"
  const similarStatus = allAnimes
    .filter(anime =>
      anime.status &&
      (anime.status.toLowerCase().includes("andamento") ||
       anime.status.toLowerCase().includes("lançamento"))
    )
    .map(anime => anime.status);

  if (similarStatus.length > 0) {
    console.log("Status similares encontrados:", [...new Set(similarStatus)]);
  }

  // Agora, vamos buscar apenas os animes em lançamento/andamento
  // Usando uma condição mais flexível para incluir diferentes variações
  return await prisma.anime.findMany({
    where: {
      OR: [
        {
          status: {
            contains: 'andamento',
            mode: 'insensitive'
          }
        },
        {
          status: {
            contains: 'lançamento',
            mode: 'insensitive'
          }
        }
      ]
    },
    select: {
      id: true,
      title: true,
      slug: true,
      status: true, // Adicionado para depuração
      totalEpisodes: true,
      malId: true, // Campo adicionado ao modelo
      episodes: {
        select: {
          id: true,
          number: true,
          title: true,
          videoUrl: true
        },
        orderBy: {
          number: 'desc'
        },
        take: 1
      }
    }
  });
}

// Função para buscar títulos de episódios do MyAnimeList
async function fetchEpisodeTitlesFromMAL(animeTitle: string, episodeNumber: number) {
  try {
    console.log(`Buscando título do episódio ${episodeNumber} de "${animeTitle}" no MyAnimeList...`);

    // Construir a URL para a API Jikan (API não oficial do MyAnimeList)
    const searchUrl = `https://api.jikan.moe/v4/anime?q=${encodeURIComponent(animeTitle)}&limit=1`;

    // Buscar o anime no MyAnimeList
    const searchResponse = await fetch(searchUrl);
    if (!searchResponse.ok) {
      console.log(`Erro ao buscar anime no MyAnimeList: ${searchResponse.status}`);
      return null;
    }

    const searchData = await searchResponse.json();
    if (!searchData.data || searchData.data.length === 0) {
      console.log(`Anime "${animeTitle}" não encontrado no MyAnimeList`);
      return null;
    }

    // Obter o ID do anime no MyAnimeList
    const malId = searchData.data[0].mal_id;
    console.log(`Anime encontrado no MyAnimeList com ID: ${malId}`);

    // Esperar um pouco para evitar rate limiting da API Jikan
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Buscar os episódios do anime
    const episodesUrl = `https://api.jikan.moe/v4/anime/${malId}/episodes`;
    const episodesResponse = await fetch(episodesUrl);
    if (!episodesResponse.ok) {
      console.log(`Erro ao buscar episódios no MyAnimeList: ${episodesResponse.status}`);
      return null;
    }

    const episodesData = await episodesResponse.json();
    if (!episodesData.data || episodesData.data.length === 0) {
      console.log(`Nenhum episódio encontrado para o anime "${animeTitle}" no MyAnimeList`);
      return null;
    }

    // Encontrar o episódio específico pelo número (não pelo mal_id)
    const episode = episodesData.data.find((ep: any) => ep.mal_id === episodeNumber || parseInt(ep.mal_id) === episodeNumber);

    // Se não encontrou pelo mal_id, tentar pelo número do episódio
    if (!episode) {
      const episodeByNumber = episodesData.data.find((ep: any) => {
        // Extrair o número do episódio do título ou do campo episode
        const epNumber = ep.episode || (ep.title && ep.title.match(/^#(\d+)/)) ? parseInt(ep.title.match(/^#(\d+)/)[1]) : null;
        return epNumber === episodeNumber;
      });

      if (!episodeByNumber) {
        console.log(`Episódio ${episodeNumber} não encontrado para o anime "${animeTitle}" no MyAnimeList`);
        return null;
      }

      return episodeByNumber.title;
    }

    console.log(`Título do episódio ${episodeNumber} encontrado: "${episode.title}"`);
    return episode.title;
  } catch (error) {
    console.error(`Erro ao buscar título do episódio no MyAnimeList:`, error);
    return null;
  }
}

// Função para verificar novos episódios no AnimeFire
async function checkNewEpisodes(anime: any) {
  try {
    // Determinar o último episódio atual
    const lastEpisode = anime.episodes[0];
    const lastEpisodeNumber = lastEpisode ? lastEpisode.number : 0;

    console.log(`Verificando novos episódios para ${anime.title}. Último episódio: ${lastEpisodeNumber}`);

    // Construir a URL do AnimeFire para o anime
    const animeSlug = anime.slug;
    const animeFireUrl = `https://animefire.plus/animes/${animeSlug}-todos-os-episodios`;

    // Chave da API
    const apiKey = '0sYiHJHn/2uoVdlwgqcoTgzL3VqSIVw76XTeEVa7DfA=';

    // Sempre usar HTTP para evitar problemas com certificados autoassinados
    const apiUrl = `http://15.229.117.80/api?api_key=${encodeURIComponent(apiKey)}&anime_link=${encodeURIComponent(animeFireUrl)}`;

    console.log(`Chamando API externa: ${apiUrl}`);

    // Fazer a requisição para a API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Erro ao buscar dados do anime: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data || !data.episodes || !Array.isArray(data.episodes)) {
      console.log(`Nenhum episódio encontrado para ${anime.title}`);
      return null;
    }

    // Buscar todos os episódios existentes para este anime
    const existingEpisodes = await prisma.episode.findMany({
      where: { animeId: anime.id },
      select: { number: true }
    });

    console.log(`Episódios existentes para ${anime.title}:`, existingEpisodes.map(ep => ep.number).sort((a, b) => a - b).join(', '));

    // Criar um conjunto com os números dos episódios existentes para busca rápida
    const existingEpisodeNumbers = new Set(existingEpisodes.map(ep => ep.number));

    // Log dos episódios disponíveis na API
    console.log(`Episódios disponíveis na API para ${anime.title}:`, data.episodes.map((ep: any) => parseInt(ep.episode)).sort((a: number, b: number) => a - b).join(', '));

    // Encontrar episódios novos (que não existem no banco de dados)
    const newEpisodes = data.episodes.filter((ep: any) => {
      // Converter para número para garantir comparação correta
      const episodeNumber = parseInt(ep.episode);
      const exists = existingEpisodeNumbers.has(episodeNumber);

      // Log para depuração
      console.log(`Episódio ${episodeNumber} do anime ${anime.title}: ${exists ? 'já existe' : 'não existe'}`);

      return !exists;
    });

    if (newEpisodes.length === 0) {
      console.log(`Nenhum episódio novo ou faltante encontrado para ${anime.title}`);
      return null;
    }

    // Classificar os episódios por tipo (novos ou faltantes)
    const missingEpisodes = newEpisodes.filter(ep => {
      const epNumber = parseInt(ep.episode);
      const isMissing = epNumber < lastEpisodeNumber;
      console.log(`Episódio ${epNumber}: ${isMissing ? 'faltante' : 'novo'} (último: ${lastEpisodeNumber})`);
      return isMissing;
    });

    const futureEpisodes = newEpisodes.filter(ep => parseInt(ep.episode) > lastEpisodeNumber);

    console.log(`Encontrados ${newEpisodes.length} episódios para ${anime.title}:`);
    if (missingEpisodes.length > 0) {
      console.log(`- ${missingEpisodes.length} episódios faltantes: ${missingEpisodes.map(ep => ep.episode).join(', ')}`);
    }
    if (futureEpisodes.length > 0) {
      console.log(`- ${futureEpisodes.length} episódios novos: ${futureEpisodes.map(ep => ep.episode).join(', ')}`);
    }

    // Adicionar os novos episódios
    const addedEpisodes = [];

    for (const ep of newEpisodes) {
      // Escolher a melhor qualidade disponível (preferência: 720p)
      const hdVideo = ep.data.find((d: any) => d.resolution === '720p' && d.status === 'ONLINE');
      const sdVideo = ep.data.find((d: any) => d.resolution === '360p' && d.status === 'ONLINE');
      const fhdVideo = ep.data.find((d: any) => d.resolution === '1080p' && d.status === 'ONLINE');

      const videoUrl = hdVideo?.url || sdVideo?.url || fhdVideo?.url || '';

      // Usar a data de publicação do episódio, se disponível
      let airDate = new Date();
      if (ep.publish_date) {
        try {
          // Usar o utilitário para analisar a data em qualquer formato
          const parsedDate = parseDate(ep.publish_date);

          if (parsedDate) {
            airDate = parsedDate;
            // Garantir que a data tenha o horário definido para meio-dia
            airDate.setHours(12, 0, 0, 0);
            console.log(`Usando data de publicação para episódio ${ep.episode}: ${ep.publish_date} -> ${airDate.toISOString()} (local: ${airDate.toString()})`);
          } else {
            console.log(`Data inválida para episódio ${ep.episode}: "${ep.publish_date}", usando data atual`);
          }
        } catch (dateError) {
          console.error(`Erro ao converter data de publicação: ${ep.publish_date}`, dateError);
        }
      }

      // Converter o número do episódio para inteiro
      const episodeNumber = parseInt(ep.episode);

      // Tentar buscar o título do episódio do MyAnimeList
      let episodeTitle = '';
      let malTitle = null;

      // Verificar se o anime tem malId
      if (anime.malId) {
        try {
          // Usar o malId do anime se disponível
          const episodesUrl = `https://api.jikan.moe/v4/anime/${anime.malId}/episodes`;
          const episodesResponse = await fetch(episodesUrl);

          if (episodesResponse.ok) {
            const episodesData = await episodesResponse.json();
            if (episodesData.data && episodesData.data.length > 0) {
              // Encontrar o episódio pelo número (não pelo mal_id)
              const episode = episodesData.data.find((ep: any) => ep.mal_id === episodeNumber || parseInt(ep.mal_id) === episodeNumber);
              if (episode) {
                malTitle = episode.title;
                console.log(`Título do episódio ${episodeNumber} encontrado via malId: "${malTitle}"`);
              }
            }
          }
        } catch (error) {
          console.error(`Erro ao buscar título do episódio via malId:`, error);
        }
      }

      // Se não encontrou pelo malId, tentar buscar pelo título
      if (!malTitle) {
        malTitle = await fetchEpisodeTitlesFromMAL(anime.title, episodeNumber);
      }

      // Determinar o título final do episódio
      if (malTitle) {
        // Usar o título do MyAnimeList
        episodeTitle = `Episódio ${episodeNumber} - ${malTitle}`;
      } else if (ep.title) {
        // Usar o título da API do AnimeFire
        episodeTitle = `Episódio ${episodeNumber} - ${ep.title}`;
      } else {
        // Usar um título genérico
        episodeTitle = `Episódio ${episodeNumber}`;
      }

      // Log para depuração
      console.log(`Criando episódio ${episodeNumber} para ${anime.title}:`, {
        animeId: anime.id,
        number: episodeNumber,
        title: episodeTitle,
        airDate: airDate,
        frame: data.anime_image || '',
        videoUrl: videoUrl,
        sourceType: 'direct_mp4'
      });

      // Criar o episódio no banco de dados
      const episode = await prisma.episode.create({
        data: {
          animeId: anime.id,
          number: episodeNumber,
          title: episodeTitle,
          airDate: airDate,
          frame: data.anime_image || '',
          videoUrl: videoUrl,
          sourceType: 'direct_mp4'
        }
      });

      addedEpisodes.push(episode);
      console.log(`Episódio ${ep.episode} adicionado para ${anime.title}`);
    }

    // Atualizar o totalEpisodes do anime se necessário
    if (addedEpisodes.length > 0) {
      const highestEpisodeNumber = Math.max(...addedEpisodes.map(ep => ep.number));

      // Verificar se totalEpisodes é um número válido
      let shouldUpdate = false;
      let currentTotalEpisodes = 0;

      if (typeof anime.totalEpisodes === 'number') {
        currentTotalEpisodes = anime.totalEpisodes;
        shouldUpdate = highestEpisodeNumber > currentTotalEpisodes;
      } else if (typeof anime.totalEpisodes === 'string') {
        // Tentar converter para número
        const parsed = parseInt(anime.totalEpisodes);
        if (!isNaN(parsed)) {
          currentTotalEpisodes = parsed;
          shouldUpdate = highestEpisodeNumber > currentTotalEpisodes;
        } else {
          // Se não for um número válido (ex: "??"), sempre atualizar
          shouldUpdate = true;
          console.log(`totalEpisodes para ${anime.title} não é um número válido: "${anime.totalEpisodes}", atualizando para ${highestEpisodeNumber}`);
        }
      } else {
        // Se for null, undefined ou outro tipo, sempre atualizar
        shouldUpdate = true;
        console.log(`totalEpisodes para ${anime.title} é ${anime.totalEpisodes}, atualizando para ${highestEpisodeNumber}`);
      }

      if (shouldUpdate) {
        await prisma.anime.update({
          where: { id: anime.id },
          data: { totalEpisodes: highestEpisodeNumber }
        });

        console.log(`Atualizado totalEpisodes para ${anime.title}: ${highestEpisodeNumber} (era: ${anime.totalEpisodes})`);
      }
    }

    return addedEpisodes;
  } catch (error) {
    console.error(`Erro ao verificar novos episódios para ${anime.title}:`, error);
    return null;
  }
}

export async function GET(req: Request) {
  try {
    // Verificar autorização
    if (!await isAuthorized(req)) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    console.log('Iniciando verificação de novos episódios...');

    // Buscar animes em lançamento/andamento
    const ongoingAnimes = await getOngoingAnimes();
    console.log(`Encontrados ${ongoingAnimes.length} animes em lançamento/andamento`);

    // Listar os animes encontrados
    if (ongoingAnimes.length > 0) {
      console.log("Animes que serão verificados:");
      ongoingAnimes.forEach(anime => {
        console.log(`- ${anime.title} (Status: ${anime.status}, Total Episódios: ${anime.totalEpisodes})`);
      });
    }

    // Verificar novos episódios para cada anime
    const results = [];

    for (const anime of ongoingAnimes) {
      const newEpisodes = await checkNewEpisodes(anime);

      // Obter o último número de episódio
      const lastEpisode = anime.episodes[0];
      const lastEpisodeNumber = lastEpisode ? lastEpisode.number : 0;

      // Classificar os episódios adicionados (se houver)
      let missingEpisodes: any[] = [];
      let futureEpisodes: any[] = [];

      if (newEpisodes) {
        // Converter os números de episódio para inteiros para comparação
        missingEpisodes = newEpisodes.filter(ep => {
          // Garantir que estamos trabalhando com números
          const epNumber = typeof ep.number === 'number' ? ep.number : parseInt(ep.number);
          return epNumber < lastEpisodeNumber;
        });

        futureEpisodes = newEpisodes.filter(ep => {
          // Garantir que estamos trabalhando com números
          const epNumber = typeof ep.number === 'number' ? ep.number : parseInt(ep.number);
          return epNumber > lastEpisodeNumber;
        });

        console.log(`Classificação na resposta: ${missingEpisodes.length} faltantes, ${futureEpisodes.length} novos`);
      }

      results.push({
        anime: anime.title,
        totalEpisodes: newEpisodes ? newEpisodes.length : 0,
        missingEpisodes: missingEpisodes.length,
        newEpisodes: futureEpisodes.length,
        episodeNumbers: newEpisodes ? newEpisodes.map(ep => ep.number).sort((a, b) => a - b) : [],
        success: !!newEpisodes
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Verificação de episódios concluída',
      results
    });
  } catch (error) {
    console.error('Erro ao verificar novos episódios:', error);
    return NextResponse.json({ error: 'Erro ao verificar novos episódios' }, { status: 500 });
  }
}
