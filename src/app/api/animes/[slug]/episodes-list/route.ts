import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

/**
 * Retorna a lista de episódios de um anime pelo slug
 * Usado pelo serviço de atualização de animes
 */
export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    // Verificar autenticação - permitir acesso para o serviço de atualização
    // Não verificamos a role aqui para permitir que o serviço de atualização acesse os dados

    const { slug } = params

    // Buscar o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: { slug }
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Buscar os episódios do anime
    const episodes = await prisma.episode.findMany({
      where: {
        animeId: anime.id
      },
      select: {
        id: true,
        number: true
      },
      orderBy: {
        number: 'asc'
      }
    })

    return NextResponse.json(episodes)
  } catch (error) {
    console.error('Erro ao buscar episódios:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar episódios' },
      { status: 500 }
    )
  }
}
