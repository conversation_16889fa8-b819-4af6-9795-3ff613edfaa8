'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

export default function AdPlacement() {
  const [showAd, setShowAd] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    // Verificar se estamos em uma página onde queremos mostrar anúncios
    const excludedPaths = ['/login', '/register', '/admin', '/privacidade', '/dmca', '/contato']
    if (excludedPaths.some(path => pathname.startsWith(path))) {
      return
    }

    setShowAd(true)
  }, [pathname])

  if (!showAd) {
    return null
  }

  return null
}
