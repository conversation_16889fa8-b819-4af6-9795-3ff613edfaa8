'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { useWatchedEpisodes } from '@/hooks/useWatchedEpisodes'

interface VideoQuality {
  label: string
  url: string
  resolution: string
}

interface AnimeVideoPlayerProps {
  videoUrl: string
  poster?: string
  title?: string
  className?: string
  qualities?: VideoQuality[]
  onNextEpisode?: () => void
  episodeId?: string
  animeSlug?: string
}

/**
 * AnimeVideoPlayer - Um componente moderno para reproduzir vídeos de anime
 *
 * Suporta dois tipos de URLs:
 * 1. URLs do Blogger (contendo 'blogger.com/video.g?token=')
 * 2. URLs diretas de arquivos MP4
 *
 * @param videoUrl - URL do vídeo (Blogger ou MP4 direto)
 * @param poster - URL da imagem de poster (opcional)
 * @param title - Título do vídeo (opcional)
 * @param className - Classes CSS adicionais (opcional)
 */
export default function AnimeVideoPlayer({
  videoUrl,
  poster,
  title = 'Episódio de Anime',
  className = '',
  qualities = [],
  onNextEpisode,
  episodeId,
  animeSlug
}: AnimeVideoPlayerProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null)
  const [showQualityMenu, setShowQualityMenu] = useState(false)
  const [currentQuality, setCurrentQuality] = useState<string>('auto')
  const [currentVideoUrl, setCurrentVideoUrl] = useState(videoUrl)
  const [hasMarkedAsWatched, setHasMarkedAsWatched] = useState(false)
  const [videoError, setVideoError] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')

  const { data: session } = useSession()
  const videoRef = useRef<HTMLVideoElement>(null)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const playerRef = useRef<HTMLDivElement>(null)
  const progressRef = useRef<HTMLDivElement>(null)

  // Gerar qualidades padrão se não forem fornecidas
  const availableQualities = qualities.length > 0 ? qualities : [
    { label: 'Auto', url: videoUrl, resolution: 'auto' }
  ]

  // Se a URL contiver indicações de qualidade, adicione opções
  useEffect(() => {
    if (qualities.length === 0 && videoUrl.includes('lightspeedst.net')) {
      // Detectar qualidade atual
      let detectedQuality = 'Auto'
      if (videoUrl.includes('/hd/')) detectedQuality = '720p'
      else if (videoUrl.includes('/sd/')) detectedQuality = '360p'
      else if (videoUrl.includes('/720p.mp4')) detectedQuality = '720p'
      else if (videoUrl.includes('/480p.mp4')) detectedQuality = '480p'

      setCurrentQuality(detectedQuality)

      // Criar URLs alternativas
      if (videoUrl.includes('/hd/')) {
        const sdUrl = videoUrl.replace('/hd/', '/sd/')
        setCurrentVideoUrl(videoUrl)
      } else if (videoUrl.includes('/sd/')) {
        const hdUrl = videoUrl.replace('/sd/', '/hd/')
        setCurrentVideoUrl(videoUrl)
      } else if (videoUrl.includes('/720p.mp4')) {
        const sdUrl = videoUrl.replace('/720p.mp4', '/480p.mp4')
        setCurrentVideoUrl(videoUrl)
      } else if (videoUrl.includes('/480p.mp4')) {
        const hdUrl = videoUrl.replace('/480p.mp4', '/720p.mp4')
        setCurrentVideoUrl(videoUrl)
      } else {
        setCurrentVideoUrl(videoUrl)
      }
    } else {
      setCurrentVideoUrl(videoUrl)
    }
  }, [videoUrl, qualities])

  // Determina o tipo de vídeo com base na URL
  const isBloggerVideo = videoUrl.includes('blogger.com') || videoUrl.includes('blogspot.com')
  const isDirectMp4 = videoUrl.endsWith('.mp4') ||
                     videoUrl.includes('lightspeedst.net') ||
                     videoUrl.includes('/mp4/') ||
                     videoUrl.includes('/mp4_temp/')

  // Efeito para lidar com o carregamento do vídeo
  useEffect(() => {
    setIsLoading(true)
    setIsPlaying(false)
    setCurrentTime(0)
    setDuration(0)
    setHasMarkedAsWatched(false)
    setVideoError(false)
    setErrorMessage('')
  }, [videoUrl])

  // Usar o hook useWatchedEpisodes para gerenciar o estado de assistido
  const { updateWatchedStatus } = useWatchedEpisodes(animeSlug || '')

  // Efeito para salvar progresso para vídeos do Blogger
  useEffect(() => {
    if (!isBloggerVideo || !session?.user || !episodeId || !animeSlug) return

    // Para vídeos do Blogger, não temos acesso ao progresso real
    // Então vamos simular um progresso de visualização
    let bloggerProgressInterval: NodeJS.Timeout | null = null
    let markAsWatchedTimeout: NodeJS.Timeout | null = null
    let handleBeforeUnload: (() => void) | null = null

    // Criar a função de beforeunload fora do bloco condicional
    handleBeforeUnload = () => {
      // Marcar como assistido quando o usuário sair da página
      if (!hasMarkedAsWatched) {
        updateWatchedStatus(episodeId, true).catch(error => {
          console.error('Erro ao marcar episódio como assistido ao sair:', error)
        })

        // Salvar progresso como 100% ao sair
        fetch('/api/watch-progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            episodeId,
            animeId: animeSlug,
            currentTime: 120, // Tempo simulado completo
            duration: 120, // Duração simulada
            percentage: 100, // Porcentagem completa
            isBloggerEpisode: true // Marcar explicitamente como episódio do Blogger
          })
        }).catch(error => {
          console.error('Erro ao salvar progresso final:', error)
        })
      }
    }

    // Adicionar o evento de beforeunload
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Iniciar um intervalo para simular o progresso
    if (!hasMarkedAsWatched) {
      // Marcar como assistido após 10 segundos para garantir
      markAsWatchedTimeout = setTimeout(() => {
        markAsWatched()
      }, 10000)

      // Salvar progresso simulado periodicamente
      bloggerProgressInterval = setInterval(() => {
        fetch('/api/watch-progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            episodeId,
            animeId: animeSlug,
            currentTime: 60, // Tempo simulado
            duration: 120, // Duração simulada
            percentage: 50, // Porcentagem simulada
            isBloggerEpisode: true // Marcar explicitamente como episódio do Blogger
          })
        }).catch(error => {
          console.error('Erro ao salvar progresso simulado:', error)
        })
      }, 30000) // A cada 30 segundos
    }

    return () => {
      if (bloggerProgressInterval) {
        clearInterval(bloggerProgressInterval)
      }

      if (markAsWatchedTimeout) {
        clearTimeout(markAsWatchedTimeout)
      }

      // Limpar evento ao desmontar
      if (handleBeforeUnload) {
        window.removeEventListener('beforeunload', handleBeforeUnload)
      }
    }
  }, [isBloggerVideo, session, episodeId, animeSlug, hasMarkedAsWatched, updateWatchedStatus])

  // Função para marcar o episódio como assistido
  const markAsWatched = async () => {
    if (!session || !episodeId || !animeSlug || hasMarkedAsWatched) return

    // Removido log de marcação automática de episódio

    try {
      // Marcar localmente primeiro
      setHasMarkedAsWatched(true)

      // Usar o hook para atualizar o status no servidor
      const success = await updateWatchedStatus(episodeId, true)

      if (!success) {
        setHasMarkedAsWatched(false) // Reverter estado local em caso de erro
      }
    } catch (error) {
      console.error('Erro ao marcar episódio como assistido:', error)
      setHasMarkedAsWatched(false) // Reverter estado local em caso de erro
    }
  }

  // Função para salvar o progresso de visualização
  const saveWatchProgress = useCallback(async () => {
    if (!session?.user || !episodeId || !animeSlug) {
      return
    }

    if (!videoRef.current) {
      return
    }

    const currentTime = videoRef.current.currentTime
    const duration = videoRef.current.duration

    if (!duration) {
      return
    }

    // Calcular a porcentagem assistida
    const percentage = Math.min(Math.round((currentTime / duration) * 100), 100)

    // Não salvar se o progresso for muito pequeno (menos de 1%)
    if (percentage < 1) {
      return
    }

    try {
      const response = await fetch('/api/watch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          episodeId,
          animeId: animeSlug,
          currentTime,
          duration,
          percentage
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Erro ao salvar progresso:', errorText)

        // Tentar analisar o erro como JSON
        try {
          const errorJson = JSON.parse(errorText)
          console.error('Detalhes do erro:', errorJson)
        } catch (parseError) {
          // Se não for JSON, usar o texto bruto
          console.error('Resposta de erro não é JSON:', errorText)
        }
      }
    } catch (error) {
      console.error('Erro ao salvar progresso:', error)
    }
  }, [session, episodeId, animeSlug])

  // Carregar o progresso de visualização ao montar o componente
  useEffect(() => {
    if (!session?.user || !episodeId || !videoRef.current) return

    const loadWatchProgress = async () => {
      try {
        const response = await fetch(`/api/watch-progress?episodeId=${episodeId}`)

        if (response.ok) {
          const data = await response.json()

          if (data.currentTime && videoRef.current) {
            // Só definir o tempo se for maior que 0 e menor que 98% da duração
            // para evitar começar no final do vídeo
            if (data.currentTime > 0 && (!data.duration || data.percentage < 98)) {
              videoRef.current.currentTime = data.currentTime
            }
          }
        }
      } catch (error) {
        console.error('Erro ao carregar progresso:', error)
      }
    }

    loadWatchProgress()
  }, [session, episodeId])

  // Efeito para esconder os controles após um período de inatividade
  useEffect(() => {
    const hideControls = () => {
      if (isPlaying) {
        setShowControls(false)
      }
    }

    if (controlsTimeout) {
      clearTimeout(controlsTimeout)
    }

    if (showControls && isPlaying) {
      const timeout = setTimeout(hideControls, 3000)
      setControlsTimeout(timeout)
    }

    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout)
      }
    }
  }, [showControls, isPlaying])

  // Efeito para fechar o menu de qualidade quando clicar fora dele
  useEffect(() => {
    if (!showQualityMenu) return

    const handleClickOutside = (event: MouseEvent) => {
      if (playerRef.current && !playerRef.current.contains(event.target as Node)) {
        setShowQualityMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showQualityMenu])

  // Manipuladores de eventos
  const handleVideoLoaded = () => {
    setIsLoading(false)
    if (videoRef.current) {
      setDuration(videoRef.current.duration)
    }
  }

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    setIsLoading(false)
    setVideoError(true)

    // Obter informações detalhadas sobre o erro
    const target = e.target as HTMLVideoElement
    const errorCode = target.error?.code

    console.error('Erro no player de vídeo:', {
      errorCode,
      errorMessage: target.error?.message,
      videoUrl: currentVideoUrl,
      networkState: target.networkState,
      readyState: target.readyState
    })

    // Definir mensagem de erro com base no código de erro
    switch (errorCode) {
      case 1: // MEDIA_ERR_ABORTED
        setErrorMessage('A reprodução foi abortada pelo usuário.')
        break
      case 2: // MEDIA_ERR_NETWORK
        setErrorMessage('Ocorreu um erro de rede ao carregar o vídeo.')
        break
      case 3: // MEDIA_ERR_DECODE
        setErrorMessage('Ocorreu um erro ao decodificar o vídeo.')
        break
      case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
        setErrorMessage('O formato do vídeo não é suportado ou a URL está inacessível.')
        break
      default:
        setErrorMessage('Ocorreu um erro ao reproduzir o vídeo.')
    }

    // Tentar carregar uma versão alternativa do vídeo se disponível
    if (qualities.length > 1 && currentQuality !== 'Auto') {
      // Tentar mudar para qualidade automática
      const autoQuality = qualities.find(q => q.resolution === 'auto')
      if (autoQuality) {
        // Tentando alternar para qualidade automática após erro
        setCurrentQuality('Auto')
        setCurrentVideoUrl(autoQuality.url)
        setVideoError(false)
        setErrorMessage('')
        setIsLoading(true)
      }
    }
  }

  const handleIframeLoaded = () => {
    setIsLoading(false)

    // Para vídeos do Blogger, marcar como assistido após o carregamento
    // já que não temos acesso ao progresso do vídeo dentro do iframe
    if (isBloggerVideo && !hasMarkedAsWatched && episodeId && animeSlug) {
      console.log('Vídeo do Blogger carregado, marcando como assistido em 5 segundos...')

      // Pequeno atraso para garantir que o usuário realmente começou a assistir
      setTimeout(() => {
        markAsWatched()

        // Salvar progresso simulado para garantir que apareça em "Continue Assistindo"
        fetch('/api/watch-progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            episodeId,
            animeId: animeSlug,
            currentTime: 60, // Tempo simulado
            duration: 120, // Duração simulada
            percentage: 50, // Porcentagem simulada
            isBloggerEpisode: true // Marcar explicitamente como episódio do Blogger
          })
        }).catch(error => {
          console.error('Erro ao salvar progresso inicial para vídeo do Blogger:', error)
        })
      }, 5000) // Marcar como assistido após 5 segundos de carregamento
    }
  }

  // Referência para o último tempo em que o progresso foi salvo
  const lastProgressSaveRef = useRef(0)

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const newTime = videoRef.current.currentTime
      setCurrentTime(newTime)

      // Marcar como assistido quando o usuário assistir pelo menos 30 segundos
      // ou 20% do vídeo, o que ocorrer primeiro
      if (!hasMarkedAsWatched &&
          ((newTime > 30) || (duration > 0 && newTime / duration > 0.2))) {
        // Marcando como assistido quando atingir o limite de progresso
        markAsWatched()
      }

      // Salvar o progresso a cada 15 segundos ou quando o usuário pausar o vídeo
      if (session?.user &&
          (Math.abs(newTime - lastProgressSaveRef.current) > 15)) {
        lastProgressSaveRef.current = newTime
        saveWatchProgress()
      }
    }
  }

  const handlePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
        // Salvar o progresso quando o usuário pausar o vídeo
        saveWatchProgress()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value)
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
    setIsMuted(newVolume === 0)
  }

  const handleMuteToggle = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  const handleEnded = () => {
    setIsPlaying(false)

    // Salvar o progresso como 100% quando o vídeo terminar
    if (videoRef.current && videoRef.current.duration && episodeId && animeSlug) {
      const duration = videoRef.current.duration
      try {
        fetch('/api/watch-progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            episodeId,
            animeId: animeSlug,
            currentTime: duration,
            duration,
            percentage: 100
          })
        })
        // Progresso final salvo como 100%
      } catch (error) {
        console.error('Erro ao salvar progresso final:', error)
      }
    }

    if (onNextEpisode) {
      onNextEpisode()
    }
  }

  const handleFullscreenToggle = () => {
    if (!playerRef.current) return

    if (!document.fullscreenElement) {
      playerRef.current.requestFullscreen().catch(err => {
        console.error(`Erro ao entrar em tela cheia: ${err.message}`)
      })
    } else {
      document.exitFullscreen()
    }
  }

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!progressRef.current || !videoRef.current) return

    const rect = progressRef.current.getBoundingClientRect()
    const pos = (e.clientX - rect.left) / rect.width
    const newTime = pos * duration

    videoRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`
  }

  const handleQualityChange = (quality: string, url: string) => {
    // Salvar a posição atual do vídeo
    const currentPos = videoRef.current?.currentTime || 0
    const wasPlaying = isPlaying

    // Atualizar a qualidade e URL
    setCurrentQuality(quality)
    setCurrentVideoUrl(url)
    setShowQualityMenu(false)

    // Após a mudança de URL, restaurar a posição e estado de reprodução
    setTimeout(() => {
      if (videoRef.current) {
        videoRef.current.currentTime = currentPos
        if (wasPlaying) {
          videoRef.current.play()
        }
      }
    }, 100)
  }

  // Renderiza o player apropriado com base no tipo de URL
  const renderPlayer = () => {
    // Se houver um erro no vídeo, exibir mensagem de erro
    if (videoError) {
      return (
        <div className="w-full h-full flex items-center justify-center bg-gray-900 flex-col p-4">
          <svg className="w-16 h-16 text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-xl font-bold text-white mb-2">Erro ao reproduzir vídeo</h3>
          <p className="text-gray-400 text-center mb-4">{errorMessage}</p>
          <div className="flex flex-wrap gap-2 justify-center">
            <button
              onClick={() => {
                setVideoError(false)
                setIsLoading(true)
                setCurrentVideoUrl(videoUrl)

                // Pequeno atraso para garantir que o player seja reiniciado
                setTimeout(() => {
                  if (videoRef.current) {
                    videoRef.current.load()
                    videoRef.current.play().catch(err => {
                      console.error('Erro ao tentar reproduzir novamente:', err)
                    })
                  }
                }, 500)
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Tentar novamente
            </button>

            {/* Botão para tentar proxy alternativo */}
            {episodeId && (
              <button
                onClick={async () => {
                  setVideoError(false)
                  setIsLoading(true)
                  setErrorMessage('')

                  try {
                    // Tentar carregar o vídeo através da API de proxy
                    const proxyUrl = `/api/video/${episodeId}`
                    setCurrentVideoUrl(proxyUrl)

                    // Pequeno atraso para garantir que o player seja reiniciado
                    setTimeout(() => {
                      if (videoRef.current) {
                        videoRef.current.load()
                        videoRef.current.play().catch(err => {
                          console.error('Erro ao tentar reproduzir com proxy:', err)
                        })
                      }
                    }, 500)
                  } catch (error) {
                    console.error('Erro ao tentar usar proxy alternativo:', error)
                    setVideoError(true)
                    setErrorMessage('Não foi possível carregar o vídeo através do proxy alternativo.')
                  }
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Usar proxy alternativo
              </button>
            )}
          </div>
        </div>
      )
    }

    if (isBloggerVideo) {
      return (
        <iframe
          ref={iframeRef}
          src={currentVideoUrl}
          title={title}
          className="w-full h-full"
          allowFullScreen
          onLoad={handleIframeLoaded}
          style={{ border: 'none', overflow: 'hidden' }}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        />
      )
    } else if (isDirectMp4) {
      return (
        <video
          ref={videoRef}
          src={currentVideoUrl}
          poster={poster}
          className="w-full h-full"
          onLoadedData={handleVideoLoaded}
          onTimeUpdate={handleTimeUpdate}
          onError={handleVideoError}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onEnded={handleEnded}
          onClick={handlePlay}
          playsInline // Adiciona suporte para iOS
          crossOrigin="anonymous" // Ajuda com problemas de CORS
        >
          <source src={currentVideoUrl} type="video/mp4" />
          Seu navegador não suporta o elemento de vídeo.
        </video>
      )
    } else {
      // Tenta tratar como vídeo MP4 por padrão
      return (
        <video
          ref={videoRef}
          src={currentVideoUrl}
          poster={poster}
          className="w-full h-full"
          onLoadedData={handleVideoLoaded}
          onTimeUpdate={handleTimeUpdate}
          onError={handleVideoError}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onEnded={handleEnded}
          onClick={handlePlay}
          playsInline // Adiciona suporte para iOS
          crossOrigin="anonymous" // Ajuda com problemas de CORS
        >
          <source src={currentVideoUrl} type="video/mp4" />
          Seu navegador não suporta o elemento de vídeo.
        </video>
      )
    }
  }

  // Renderiza os controles personalizados para vídeos MP4
  const renderCustomControls = () => {
    if (isBloggerVideo || videoError) return null

    return (
      <div
        className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}
        onMouseEnter={() => setShowControls(true)}
      >
        {/* Barra de progresso */}
        <div
          ref={progressRef}
          className="w-full h-1 bg-gray-600 rounded-full mb-4 cursor-pointer"
          onClick={handleProgressClick}
        >
          <div
            className="h-full bg-purple-600 rounded-full relative"
            style={{ width: `${(currentTime / duration) * 100}%` }}
          >
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full"></div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Botão de play/pause */}
            <button
              onClick={handlePlay}
              className="text-white hover:text-purple-400 transition-colors"
            >
              {isPlaying ? (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z" />
                </svg>
              )}
            </button>

            {/* Botão de próximo episódio */}
            {onNextEpisode && (
              <button
                onClick={onNextEpisode}
                className="text-white hover:text-purple-400 transition-colors ml-2"
                title="Próximo episódio"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" />
                </svg>
              </button>
            )}

            {/* Controle de volume */}
            <div className="flex items-center space-x-2">
              <button
                onClick={handleMuteToggle}
                className="text-white hover:text-purple-400 transition-colors"
              >
                {isMuted ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" />
                  </svg>
                )}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={handleVolumeChange}
                className="w-20 accent-purple-600"
              />
            </div>

            {/* Tempo atual / duração */}
            <div className="text-white text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Título do episódio */}
            <div className="text-white text-sm hidden md:block">
              {title}
            </div>

            {/* Seletor de qualidade */}
            <div className="relative">
              <button
                onClick={() => setShowQualityMenu(!showQualityMenu)}
                className="flex items-center text-white hover:text-purple-400 transition-colors text-sm"
              >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
                {currentQuality}
                <svg className={`w-4 h-4 ml-1 transform transition-transform ${showQualityMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Menu de qualidades */}
              {showQualityMenu && (
                <div className="absolute bottom-full right-0 mb-2 bg-gray-800 rounded-lg shadow-lg overflow-hidden z-20">
                  <div className="p-1">
                    {/* Opção de qualidade automática */}
                    <button
                      onClick={() => handleQualityChange('Auto', videoUrl)}
                      className={`w-full px-4 py-2 text-left text-sm rounded hover:bg-gray-700 ${
                        currentQuality === 'Auto' ? 'bg-purple-600 text-white' : 'text-white'
                      }`}
                    >
                      Auto
                    </button>

                    {/* Opção de qualidade HD (720p) */}
                    {videoUrl.includes('/sd/') && (
                      <button
                        onClick={() => handleQualityChange('720p', videoUrl.replace('/sd/', '/hd/'))}
                        className={`w-full px-4 py-2 text-left text-sm rounded hover:bg-gray-700 ${
                          currentQuality === '720p' ? 'bg-purple-600 text-white' : 'text-white'
                        }`}
                      >
                        720p (HD)
                      </button>
                    )}

                    {/* Opção de qualidade SD (360p) */}
                    {videoUrl.includes('/hd/') && (
                      <button
                        onClick={() => handleQualityChange('360p', videoUrl.replace('/hd/', '/sd/'))}
                        className={`w-full px-4 py-2 text-left text-sm rounded hover:bg-gray-700 ${
                          currentQuality === '360p' ? 'bg-purple-600 text-white' : 'text-white'
                        }`}
                      >
                        360p (SD)
                      </button>
                    )}

                    {/* Opção de qualidade HD (720p) para mp4_temp */}
                    {videoUrl.includes('/480p.mp4') && (
                      <button
                        onClick={() => handleQualityChange('720p', videoUrl.replace('/480p.mp4', '/720p.mp4'))}
                        className={`w-full px-4 py-2 text-left text-sm rounded hover:bg-gray-700 ${
                          currentQuality === '720p' ? 'bg-purple-600 text-white' : 'text-white'
                        }`}
                      >
                        720p (HD)
                      </button>
                    )}

                    {/* Opção de qualidade SD (480p) para mp4_temp */}
                    {videoUrl.includes('/720p.mp4') && (
                      <button
                        onClick={() => handleQualityChange('480p', videoUrl.replace('/720p.mp4', '/480p.mp4'))}
                        className={`w-full px-4 py-2 text-left text-sm rounded hover:bg-gray-700 ${
                          currentQuality === '480p' ? 'bg-purple-600 text-white' : 'text-white'
                        }`}
                      >
                        480p (SD)
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Botão de tela cheia */}
            <button
              onClick={handleFullscreenToggle}
              className="text-white hover:text-purple-400 transition-colors"
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={playerRef}
      className={`relative aspect-video bg-black ${className}`}
      onMouseMove={() => setShowControls(true)}
    >
      {/* Player de vídeo */}
      {renderPlayer()}

      {/* Controles personalizados */}
      {renderCustomControls()}

      {/* Indicador de carregamento */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 z-10">
          <div className="relative">
            <svg className="animate-spin h-12 w-12 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>
      )}
    </div>
  )
}
