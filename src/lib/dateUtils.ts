/**
 * Utilitários para manipulação de datas
 */

/**
 * Detecta o formato de uma string de data e a converte para um objeto Date
 * Suporta formatos DD/MM/YYYY, MM/DD/YYYY e YYYY-MM-DD
 *
 * @param dateString String de data em vários formatos possíveis
 * @returns Objeto Date ou null se a data for inválida
 */
export function parseDate(dateString: string): Date | null {
  if (!dateString) return null;

  try {
    // Verificar se é uma data ISO (YYYY-MM-DD)
    if (dateString.includes('-')) {
      try {
        // Extrair os componentes da data ISO
        const [yearStr, monthStr, dayStr] = dateString.split('-');
        const year = parseInt(yearStr, 10);
        const month = parseInt(monthStr, 10);
        const day = parseInt(dayStr, 10);

        // Criar a data com horário ao meio-dia para evitar problemas de fuso horário
        const isoDate = new Date(year, month - 1, day, 12, 0, 0);

        if (!isNaN(isoDate.getTime())) {
          console.log(`Data ISO processada: ${dateString} -> ${isoDate.toISOString()} (local: ${isoDate.toString()})`);
          return isoDate;
        }
      } catch (error) {
        console.error(`Erro ao processar data ISO ${dateString}:`, error);

        // Fallback para o método padrão
        const isoDate = new Date(dateString);
        if (!isNaN(isoDate.getTime())) {
          // Ajustar para meio-dia no mesmo dia
          isoDate.setHours(12, 0, 0, 0);
          console.log(`Data ISO (fallback) processada: ${dateString} -> ${isoDate.toISOString()}`);
          return isoDate;
        }
      }
    }

    // Verificar se é uma string de data com barras (DD/MM/YYYY ou MM/DD/YYYY)
    if (dateString.includes('/')) {
      const dateParts = dateString.split('/').map(Number);
      if (dateParts.length !== 3) {
        return null;
      }

      let day, month, year;

      // Detectar o formato
      if (dateParts[0] > 12 && dateParts[0] <= 31) {
        // Formato brasileiro (DD/MM/YYYY)
        [day, month, year] = dateParts;
        console.log(`Data detectada como formato brasileiro: ${dateString} -> ${day}/${month}/${year}`);
      } else if (dateParts[1] > 12 && dateParts[1] <= 31) {
        // Formato americano (MM/DD/YYYY)
        [month, day, year] = dateParts;
        console.log(`Data detectada como formato americano: ${dateString} -> ${day}/${month}/${year}`);
      } else if (dateParts[2] < 100) {
        // Formato com ano de 2 dígitos, assumir DD/MM/YY
        [day, month, year] = dateParts;
        year += 2000; // Assumir anos 2000+
        console.log(`Data com ano de 2 dígitos: ${dateString} -> ${day}/${month}/${year}`);
      } else {
        // Tentar ambos os formatos e ver qual produz uma data válida

        // Tentar formato brasileiro primeiro (DD/MM/YYYY)
        let brDate = new Date(dateParts[2], dateParts[1] - 1, dateParts[0]);

        // Tentar formato americano (MM/DD/YYYY)
        let usDate = new Date(dateParts[2], dateParts[0] - 1, dateParts[1]);

        // Verificar qual data é válida
        if (!isNaN(brDate.getTime()) && dateParts[0] <= 31 && dateParts[1] <= 12) {
          [day, month, year] = dateParts;
          console.log(`Data assumida como formato brasileiro: ${dateString} -> ${day}/${month}/${year}`);
        } else if (!isNaN(usDate.getTime()) && dateParts[1] <= 31 && dateParts[0] <= 12) {
          [month, day, year] = dateParts;
          console.log(`Data assumida como formato americano: ${dateString} -> ${day}/${month}/${year}`);
        } else {
          // Se ambos os formatos produzirem datas inválidas, assumir formato brasileiro
          [day, month, year] = dateParts;
          console.log(`Formato de data ambíguo, assumindo brasileiro: ${dateString} -> ${day}/${month}/${year}`);
        }
      }

      // Criar a data com horário ao meio-dia para evitar problemas de fuso horário
      // (mês em JavaScript é 0-indexed)
      const date = new Date(year, month - 1, day, 12, 0, 0);

      // Verificar se a data é válida
      if (isNaN(date.getTime())) {
        console.error(`Data inválida após processamento: ${day}/${month}/${year}`);
        return null;
      }

      // Log para depuração
      console.log(`Data criada: ${day}/${month}/${year} -> ${date.toISOString()} (local: ${date.toString()})`);

      return date;
    }

    // Tentar outros formatos comuns
    const fallbackDate = new Date(dateString);
    if (!isNaN(fallbackDate.getTime())) {
      // Ajustar para meio-dia no mesmo dia
      fallbackDate.setHours(12, 0, 0, 0);
      console.log(`Data (fallback) processada: ${dateString} -> ${fallbackDate.toISOString()}`);
      return fallbackDate;
    }

    return null;
  } catch (error) {
    console.error('Erro ao analisar data:', error);
    return null;
  }
}

/**
 * Formata uma data para o formato brasileiro (DD/MM/YYYY)
 *
 * @param date Data a ser formatada
 * @returns String no formato DD/MM/YYYY
 */
export function formatDateBR(date: Date | string | null): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Verificar se a data é válida
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // Formatar para DD/MM/YYYY
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error('Erro ao formatar data:', error);
    return '';
  }
}

/**
 * Formata uma data para exibição com data e hora no formato brasileiro
 *
 * @param date Data a ser formatada
 * @returns String no formato DD/MM/YYYY HH:MM
 */
export function formatDateTimeBR(date: Date | string | null): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Verificar se a data é válida
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // Formatar para DD/MM/YYYY HH:MM
    return dateObj.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Erro ao formatar data e hora:', error);
    return '';
  }
}
