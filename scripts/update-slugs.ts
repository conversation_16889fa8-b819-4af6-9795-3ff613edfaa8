import { PrismaClient } from '@prisma/client'
import slugify from 'slugify'

const prisma = new PrismaClient()

async function main() {
  // Get all animes
  const animes = await prisma.anime.findMany()

  console.log(`Found ${animes.length} animes to process`)

  // Update each anime with a slug
  for (const anime of animes) {
    let baseSlug = slugify(anime.title, { lower: true, strict: true })
    let slug = baseSlug
    let counter = 1

    // Keep trying until we find a unique slug
    while (true) {
      const existing = await prisma.anime.findUnique({
        where: { 
          slug,
          NOT: {
            id: anime.id
          }
        }
      })

      if (!existing) {
        break
      }

      // If slug exists, append a number
      slug = `${baseSlug}-${counter}`
      counter++
    }

    if (anime.slug !== slug) {
      await prisma.anime.update({
        where: { id: anime.id },
        data: { slug }
      })
      console.log(`Updated anime "${anime.title}" with slug "${slug}"`)
    } else {
      console.log(`Anime "${anime.title}" already has correct slug "${slug}"`)
    }
  }
}

main()
  .catch((e) => {
    console.error('Error updating slugs:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 