import { NextResponse } from 'next/server'

export async function POST() {
  try {
    return NextResponse.json(
      { message: 'Logout realizado com sucesso' },
      {
        status: 200,
        headers: {
          'Set-Cookie': 'admin_token=; Path=/; HttpOnly; SameSite=Strict; Max-Age=0',
        },
      }
    )
  } catch (error) {
    console.error('Erro no logout:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
} 