'use client'

import { useState, useEffect } from 'react'

interface ConfigStatus {
  isConfigured: boolean
  message?: string
  hasEnvironmentVars: {
    ADMIN_EMAIL: boolean
    ADMIN_PASSWORD: boolean
    JWT_SECRET: boolean
  }
  emailConfigured: string
}

export default function AdminConfigStatus() {
  const [config, setConfig] = useState<ConfigStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    fetchConfigStatus()
  }, [])

  const fetchConfigStatus = async () => {
    try {
      const response = await fetch('/api/admin/config')
      if (response.ok) {
        const data = await response.json()
        setConfig(data)
      }
    } catch (error) {
      console.error('Erro ao buscar status da configuração:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-700 rounded w-1/4 mb-2"></div>
          <div className="h-3 bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
    )
  }

  if (!config) return null

  const getStatusColor = () => {
    if (config.isConfigured) return 'bg-green-800 border-green-600 text-green-200'
    return 'bg-yellow-800 border-yellow-600 text-yellow-200'
  }

  const getStatusIcon = () => {
    if (config.isConfigured) {
      return (
        <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
    return (
      <svg className="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    )
  }

  return (
    <div className={`rounded-lg border p-4 mb-6 ${getStatusColor()}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <h3 className="font-semibold">
              {config.isConfigured ? 'Configuração Segura' : 'Atenção: Configuração de Segurança'}
            </h3>
            <p className="text-sm opacity-90">
              {config.message || 'Credenciais de admin configuradas via variáveis de ambiente'}
            </p>
          </div>
        </div>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-sm underline opacity-75 hover:opacity-100"
        >
          {showDetails ? 'Ocultar' : 'Detalhes'}
        </button>
      </div>

      {showDetails && (
        <div className="mt-4 pt-4 border-t border-current border-opacity-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Variáveis de Ambiente:</h4>
              <ul className="space-y-1">
                <li className="flex items-center space-x-2">
                  <span className={config.hasEnvironmentVars.ADMIN_EMAIL ? 'text-green-400' : 'text-red-400'}>
                    {config.hasEnvironmentVars.ADMIN_EMAIL ? '✓' : '✗'}
                  </span>
                  <span>ADMIN_EMAIL</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className={config.hasEnvironmentVars.ADMIN_PASSWORD ? 'text-green-400' : 'text-red-400'}>
                    {config.hasEnvironmentVars.ADMIN_PASSWORD ? '✓' : '✗'}
                  </span>
                  <span>ADMIN_PASSWORD</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className={config.hasEnvironmentVars.JWT_SECRET ? 'text-green-400' : 'text-red-400'}>
                    {config.hasEnvironmentVars.JWT_SECRET ? '✓' : '✗'}
                  </span>
                  <span>JWT_SECRET</span>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Email Configurado:</h4>
              <p className="font-mono text-xs bg-black bg-opacity-20 p-2 rounded">
                {config.emailConfigured}
              </p>
            </div>
          </div>
          
          {!config.isConfigured && (
            <div className="mt-4 p-3 bg-black bg-opacity-20 rounded text-xs">
              <p className="font-medium mb-1">Para configurar no Vercel:</p>
              <ol className="list-decimal list-inside space-y-1 opacity-90">
                <li>Acesse o painel da Vercel</li>
                <li>Vá em Settings → Environment Variables</li>
                <li>Adicione ADMIN_EMAIL e ADMIN_PASSWORD</li>
                <li>Faça redeploy da aplicação</li>
              </ol>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
