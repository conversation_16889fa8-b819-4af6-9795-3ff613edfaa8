import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'
import { authOptions } from '@/lib/auth'

// GET /api/watch-progress
// Retorna todos os progressos de visualização do usuário
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')

    // Se um episodeId for fornecido, retorna apenas o progresso desse episódio
    if (episodeId) {
      console.log('Buscando progresso para o episódio:', episodeId)

      // Buscar o comentário de progresso
      const progressId = `watch_progress_${session.user.id}_${episodeId}`
      const comment = await prisma.comment.findUnique({
        where: {
          id: progressId
        },
        include: {
          episode: {
            select: {
              id: true,
              number: true,
              title: true,
              animeId: true,
              frame: true,
              anime: {
                select: {
                  id: true,
                  title: true,
                  image: true,
                  slug: true
                }
              }
            }
          }
        }
      })

      if (!comment) {
        console.log('Nenhum progresso encontrado para o episódio:', episodeId)
        return NextResponse.json({ currentTime: 0, duration: 0, percentage: 0 })
      }

      try {
        // Extrair os dados do progresso do conteúdo do comentário
        const progressData = JSON.parse(comment.content)

        // Converter para o formato esperado
        const watchProgress = {
          id: comment.id,
          userId: session.user.id,
          episodeId: episodeId,
          currentTime: progressData.currentTime || 0,
          duration: progressData.duration || 0,
          percentage: progressData.percentage || 0,
          updatedAt: comment.updatedAt,
          isBloggerEpisode: progressData.isBloggerEpisode || false,
          episode: comment.episode
        }

        console.log('Progresso encontrado:', watchProgress)
        return NextResponse.json(watchProgress)
      } catch (parseError) {
        console.error('Erro ao analisar conteúdo do comentário:', parseError)
        return NextResponse.json({ currentTime: 0, duration: 0, percentage: 0 })
      }
    }

    // Caso contrário, retorna todos os progressos do usuário
    console.log('Buscando todos os progressos do usuário:', session.user.id)

    // Buscar todos os comentários de progresso
    const comments = await prisma.comment.findMany({
      where: {
        userId: session.user.id,
        content: {
          contains: 'watch_progress'
        }
      },
      include: {
        episode: {
          select: {
            id: true,
            number: true,
            title: true,
            animeId: true,
            frame: true,
            videoUrl: true,
            anime: {
              select: {
                id: true,
                title: true,
                image: true,
                slug: true
              }
            }
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    // Converter os comentários para o formato esperado
    let allWatchProgress = comments.map(comment => {
      try {
        const progressData = JSON.parse(comment.content)
        return {
          id: comment.id,
          userId: comment.userId,
          episodeId: comment.episodeId || '',
          animeId: comment.animeId,
          currentTime: progressData.currentTime || 0,
          duration: progressData.duration || 0,
          percentage: progressData.percentage || 0,
          updatedAt: comment.updatedAt,
          isBloggerEpisode: progressData.isBloggerEpisode || false,
          episode: comment.episode
        }
      } catch (parseError) {
        console.error('Erro ao analisar conteúdo do comentário:', parseError, comment.content)
        return null
      }
    }).filter(Boolean) // Remover itens nulos

    // Ordenar por data de atualização (mais recente primeiro)
    allWatchProgress.sort((a, b) =>
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    )

    // Primeiro, vamos buscar informações sobre os episódios para identificar URLs do Blogger
    const episodeIds = allWatchProgress.map(progress => progress.episodeId).filter(Boolean)

    // Buscar detalhes dos episódios para identificar URLs do Blogger
    const episodes = await prisma.episode.findMany({
      where: {
        id: {
          in: episodeIds
        }
      },
      select: {
        id: true,
        videoUrl: true
      }
    })

    // Criar um mapa de episódios com URLs do Blogger
    const bloggerEpisodes = new Set(
      episodes
        .filter(ep => ep.videoUrl && (ep.videoUrl.includes('blogger.com') || ep.videoUrl.includes('blogspot.com')))
        .map(ep => ep.id)
    )

    // Agrupar por animeId e pegar o episódio mais recente de cada anime
    const animeMap = new Map()

    // Para cada progresso, adicionar ao mapa
    allWatchProgress.forEach(progress => {
      if (progress.episode && progress.episode.anime) {
        const animeId = progress.episode.anime.id

        // Verificar se é um episódio do Blogger
        const isBloggerEpisode = progress.isBloggerEpisode ||
          bloggerEpisodes.has(progress.episodeId) ||
          (progress.episode.videoUrl &&
            (progress.episode.videoUrl.includes('blogger.com') ||
             progress.episode.videoUrl.includes('blogspot.com')))

        // Marcar explicitamente como episódio do Blogger se for o caso
        if (isBloggerEpisode) {
          progress.isBloggerEpisode = true
        }

        // Sempre usar o episódio mais recente para cada anime
        const existingProgress = animeMap.get(animeId)
        if (!existingProgress || new Date(progress.updatedAt) > new Date(existingProgress.updatedAt)) {
          animeMap.set(animeId, progress)
        }
      }
    })

    // Converter o mapa de volta para um array
    const watchProgress = Array.from(animeMap.values())

    return NextResponse.json(watchProgress)
  } catch (error) {
    console.error('Erro ao buscar progresso de visualização:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar progresso de visualização' },
      { status: 500 }
    )
  }
}

// POST /api/watch-progress
// Atualiza o progresso de visualização de um episódio
export async function POST(request: NextRequest) {
  try {
    console.log('Iniciando POST /api/watch-progress')

    const session = await getServerSession(authOptions)
    if (!session?.user) {
      console.log('Usuário não autenticado')
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    console.log('Usuário autenticado:', session.user.id)

    const body = await request.json()
    console.log('Corpo da requisição:', body)

    const { episodeId, animeSlug, currentTime, duration, percentage, isBloggerEpisode: clientIsBloggerEpisode } = body

    if (!episodeId) {
      console.log('Parâmetro obrigatório ausente:', { episodeId })
      return NextResponse.json(
        { error: 'ID do episódio é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o episódio existe e obter o animeId real
    console.log('Verificando se o episódio existe:', episodeId)
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId },
      select: {
        id: true,
        animeId: true,
        videoUrl: true,
        anime: {
          select: {
            id: true,
            slug: true
          }
        }
      }
    })

    // Verificar se é um episódio do Blogger
    // Usar a informação do cliente se disponível, ou verificar a URL
    const isBloggerEpisode = clientIsBloggerEpisode === true ||
      (episode?.videoUrl && (episode.videoUrl.includes('blogger.com') || episode.videoUrl.includes('blogspot.com')))

    console.log('Episódio é do Blogger:', isBloggerEpisode, 'clientIsBloggerEpisode:', clientIsBloggerEpisode)

    if (!episode) {
      console.log('Episódio não encontrado:', episodeId)
      return NextResponse.json(
        { error: 'Episódio não encontrado' },
        { status: 404 }
      )
    }

    // Obter o ID real do anime
    const animeId = episode.animeId

    console.log('Episódio encontrado, animeId real:', animeId)

    // Usar o modelo Comment para armazenar o progresso de visualização
    console.log('Usando o modelo Comment para armazenar o progresso')

    // Criar um ID único para o comentário de progresso
    const progressId = `watch_progress_${session.user.id}_${episodeId}`

    // Criar um conteúdo JSON com os dados do progresso
    const progressContent = JSON.stringify({
      type: 'watch_progress',
      currentTime: currentTime || 0,
      duration: duration || 0,
      percentage: percentage || 0,
      updatedAt: new Date().toISOString(),
      isBloggerEpisode: isBloggerEpisode || false
    })

    try {
      console.log('Tentando upsert no Comment com animeId:', animeId)

      // Verificar se já existe um comentário com esse ID
      const existingComment = await prisma.comment.findUnique({
        where: {
          id: progressId
        }
      })

      let comment

      if (existingComment) {
        // Se já existe, apenas atualizar
        console.log('Comentário existente encontrado, atualizando')
        comment = await prisma.comment.update({
          where: {
            id: progressId
          },
          data: {
            content: progressContent,
            updatedAt: new Date()
          }
        })
      } else {
        // Se não existe, criar novo
        console.log('Criando novo comentário de progresso')
        comment = await prisma.comment.create({
          data: {
            id: progressId,
            content: progressContent,
            userId: session.user.id,
            animeId: animeId,
            episodeId: episodeId
          }
        })
      }

      console.log('Progresso salvo com sucesso como comentário:', {
        userId: session.user.id,
        episodeId,
        animeId,
        currentTime,
        duration,
        percentage,
        commentId: comment.id
      })

      // Converter o comentário para o formato de resposta esperado
      const watchProgress = {
        id: comment.id,
        userId: session.user.id,
        episodeId: episodeId,
        currentTime: currentTime || 0,
        duration: duration || 0,
        percentage: percentage || 0,
        updatedAt: comment.updatedAt,
        isBloggerEpisode: isBloggerEpisode || false,
        episode: await prisma.episode.findUnique({
          where: { id: episodeId },
          select: {
            id: true,
            number: true,
            title: true,
            animeId: true,
            frame: true,
            anime: {
              select: {
                id: true,
                title: true,
                image: true,
                slug: true
              }
            }
          }
        })
      }

      return NextResponse.json(watchProgress)
    } catch (error) {
      console.error('Erro ao salvar progresso como comentário:', error)
      throw error // Propagar o erro para o bloco catch externo
    }
  } catch (error) {
    console.error('Erro ao atualizar progresso de visualização:', error)
    return NextResponse.json(
      { error: 'Erro ao atualizar progresso de visualização' },
      { status: 500 }
    )
  }
}

// DELETE /api/watch-progress
// Remove o progresso de visualização de um episódio ou todos os progressos do usuário
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')

    // Se um episodeId for fornecido, remove apenas o progresso desse episódio
    if (episodeId) {
      console.log('Removendo progresso para o episódio:', episodeId)

      // Buscar o comentário de progresso
      const progressId = `watch_progress_${session.user.id}_${episodeId}`

      try {
        await prisma.comment.delete({
          where: {
            id: progressId
          }
        })

        console.log('Progresso removido com sucesso')
        return NextResponse.json({ success: true, message: 'Progresso removido com sucesso' })
      } catch (error) {
        console.error('Erro ao remover progresso:', error)
        return NextResponse.json({ success: false, message: 'Progresso não encontrado' }, { status: 404 })
      }
    }

    // Caso contrário, remove todos os progressos do usuário
    console.log('Removendo todos os progressos do usuário:', session.user.id)

    const result = await prisma.comment.deleteMany({
      where: {
        userId: session.user.id,
        content: {
          contains: 'watch_progress'
        }
      }
    })

    console.log(`${result.count} registros de progresso removidos`)

    return NextResponse.json({ success: true, message: 'Todos os progressos removidos com sucesso' })
  } catch (error) {
    console.error('Erro ao remover progresso de visualização:', error)
    return NextResponse.json(
      { error: 'Erro ao remover progresso de visualização' },
      { status: 500 }
    )
  }
}