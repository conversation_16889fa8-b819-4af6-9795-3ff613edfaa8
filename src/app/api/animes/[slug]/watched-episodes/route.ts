import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET - Obter episódios assistidos de um anime
export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions)
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    const userId = session.user.id
    const { slug } = params

    // Buscar o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: { slug },
      select: { id: true }
    })

    if (!anime) {
      return NextResponse.json(
        { error: 'Anime não encontrado' },
        { status: 404 }
      )
    }

    // Buscar episódios assistidos pelo usuário para este anime
    const watchedEpisodes = await prisma.watchedEpisode.findMany({
      where: {
        userId,
        episode: {
          anime: {
            slug
          }
        }
      },
      select: {
        episodeId: true,
        watchedAt: true
      }
    })

    return NextResponse.json(watchedEpisodes)
  } catch (error) {
    console.error('Erro ao buscar episódios assistidos:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar episódios assistidos' },
      { status: 500 }
    )
  }
}

// POST - Marcar um episódio como assistido
export async function POST(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions)
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    const userId = session.user.id
    const { slug } = params
    const { episodeId } = await request.json()

    if (!episodeId) {
      return NextResponse.json(
        { error: 'ID do episódio é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o episódio existe e pertence ao anime correto
    const episode = await prisma.episode.findFirst({
      where: {
        id: episodeId,
        anime: {
          slug
        }
      }
    })

    if (!episode) {
      return NextResponse.json(
        { error: 'Episódio não encontrado' },
        { status: 404 }
      )
    }

    // Criar ou atualizar o registro de episódio assistido
    const watchedEpisode = await prisma.watchedEpisode.upsert({
      where: {
        userId_episodeId: {
          userId,
          episodeId
        }
      },
      update: {
        watchedAt: new Date()
      },
      create: {
        userId,
        episodeId,
        watchedAt: new Date()
      }
    })

    return NextResponse.json(watchedEpisode)
  } catch (error) {
    console.error('Erro ao marcar episódio como assistido:', error)
    return NextResponse.json(
      { error: 'Erro ao marcar episódio como assistido' },
      { status: 500 }
    )
  }
}

// DELETE - Remover um episódio da lista de assistidos
export async function DELETE(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions)
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    const userId = session.user.id
    const { slug } = params
    const { episodeId } = await request.json()

    if (!episodeId) {
      return NextResponse.json(
        { error: 'ID do episódio é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o episódio existe e pertence ao anime correto
    const episode = await prisma.episode.findFirst({
      where: {
        id: episodeId,
        anime: {
          slug
        }
      }
    })

    if (!episode) {
      return NextResponse.json(
        { error: 'Episódio não encontrado' },
        { status: 404 }
      )
    }

    // Remover o registro de episódio assistido
    await prisma.watchedEpisode.delete({
      where: {
        userId_episodeId: {
          userId,
          episodeId
        }
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Erro ao remover episódio da lista de assistidos:', error)
    return NextResponse.json(
      { error: 'Erro ao remover episódio da lista de assistidos' },
      { status: 500 }
    )
  }
}
