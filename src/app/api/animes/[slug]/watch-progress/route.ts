import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/animes/[slug]/watch-progress
// Retorna o progresso de visualização do usuário para todos os episódios de um anime
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const { slug } = params

    // Buscar o anime pelo slug
    const anime = await prisma.anime.findUnique({
      where: { slug },
      select: { id: true }
    })

    if (!anime) {
      return NextResponse.json({ error: 'Anime não encontrado' }, { status: 404 })
    }

    // Buscar todos os episódios do anime
    const episodes = await prisma.episode.findMany({
      where: { animeId: anime.id },
      select: { id: true }
    })

    const episodeIds = episodes.map(episode => episode.id)

    // Buscar o progresso de visualização do usuário para todos os episódios
    const watchProgress = await prisma.watchProgress.findMany({
      where: {
        userId: session.user.id,
        episodeId: { in: episodeIds }
      },
      include: {
        episode: {
          select: {
            id: true,
            number: true,
            title: true,
            animeId: true,
            anime: {
              select: {
                id: true,
                title: true,
                image: true,
                slug: true
              }
            }
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    return NextResponse.json(watchProgress)
  } catch (error) {
    console.error('Erro ao buscar progresso de visualização:', error)
    return NextResponse.json(
      { error: 'Erro ao buscar progresso de visualização' },
      { status: 500 }
    )
  }
}

// POST /api/animes/[slug]/watch-progress
// Atualiza o progresso de visualização de um episódio
export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const { episodeId, currentTime, duration, percentage } = await request.json()

    if (!episodeId) {
      return NextResponse.json(
        { error: 'ID do episódio é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o episódio existe
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId },
      select: { id: true, animeId: true }
    })

    if (!episode) {
      return NextResponse.json(
        { error: 'Episódio não encontrado' },
        { status: 404 }
      )
    }

    // Atualizar ou criar o progresso de visualização
    const watchProgress = await prisma.watchProgress.upsert({
      where: {
        userId_episodeId: {
          userId: session.user.id,
          episodeId
        }
      },
      update: {
        currentTime: currentTime || 0,
        duration: duration || 0,
        percentage: percentage || 0
      },
      create: {
        userId: session.user.id,
        episodeId,
        currentTime: currentTime || 0,
        duration: duration || 0,
        percentage: percentage || 0
      }
    })

    return NextResponse.json(watchProgress)
  } catch (error) {
    console.error('Erro ao atualizar progresso de visualização:', error)
    return NextResponse.json(
      { error: 'Erro ao atualizar progresso de visualização' },
      { status: 500 }
    )
  }
}

// DELETE /api/animes/[slug]/watch-progress
// Remove o progresso de visualização de um episódio
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const { episodeId } = await request.json()

    if (!episodeId) {
      return NextResponse.json(
        { error: 'ID do episódio é obrigatório' },
        { status: 400 }
      )
    }

    // Remover o progresso de visualização
    await prisma.watchProgress.delete({
      where: {
        userId_episodeId: {
          userId: session.user.id,
          episodeId
        }
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Erro ao remover progresso de visualização:', error)
    return NextResponse.json(
      { error: 'Erro ao remover progresso de visualização' },
      { status: 500 }
    )
  }
}
