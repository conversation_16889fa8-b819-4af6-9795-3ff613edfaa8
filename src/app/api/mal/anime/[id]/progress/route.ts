import { NextResponse } from 'next/server'

// Armazena o progresso atual para cada requisição
const progressMap = new Map<string, number>()

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const id = params.id
  progressMap.set(id, 0)

  const encoder = new TextEncoder()
  const stream = new ReadableStream({
    start(controller) {
      let lastProgress = 0
      
      // Envia atualizações a cada 50ms para uma animação mais suave
      const interval = setInterval(() => {
        const currentProgress = progressMap.get(id) || 0
        
        // Só envia atualização se o progresso mudou
        if (currentProgress !== lastProgress) {
          controller.enqueue(encoder.encode(`data: ${currentProgress}\n\n`))
          lastProgress = currentProgress
        }

        if (currentProgress >= 100) {
          clearInterval(interval)
          controller.close()
          progressMap.delete(id)
        }
      }, 50)
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
}

// Função para atualizar o progresso
export function updateProgress(id: string, progress: number) {
  progressMap.set(id, progress)
} 