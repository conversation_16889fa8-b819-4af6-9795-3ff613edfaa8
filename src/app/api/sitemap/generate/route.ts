import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { generateSitemap } from '@/services/sitemapService'

export async function GET(request: Request) {
  try {
    // Verificar autenticação (apenas administradores podem gerar o sitemap manualmente)
    const session = await getServerSession(authOptions)
    if (!session || !session.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    // Gerar o sitemap
    const result = await generateSitemap()

    if (result.success) {
      // Verificar se estamos em ambiente de produção (Vercel)
      const isVercel = process.env.VERCEL === '1';

      if (isVercel && result.xml) {
        // No ambiente Vercel, retornar o XML diretamente como resposta
        // para que o usuário possa baixá-lo manualmente
        return new NextResponse(result.xml, {
          headers: {
            'Content-Type': 'application/xml',
            'Content-Disposition': 'attachment; filename="sitemap.xml"'
          }
        });
      }

      // Em ambiente de desenvolvimento, retornar informações sobre o arquivo salvo
      return NextResponse.json({
        success: true,
        message: result.message,
        path: result.path
      });
    } else {
      return NextResponse.json(
        { error: result.message },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Erro ao gerar sitemap:', error)
    return NextResponse.json(
      { error: 'Erro ao gerar sitemap: ' + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    )
  }
}
