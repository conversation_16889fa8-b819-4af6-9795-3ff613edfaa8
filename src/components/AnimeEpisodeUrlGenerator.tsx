'use client'

import { useState, useEffect } from 'react'

interface AnimeEpisodeUrlGeneratorProps {
  onAddEpisodes: (animeSlug: string, episodesData: any[]) => void
}

interface Anime {
  id: string
  title: string
  slug: string
  image: string
  episodes: {
    id: string
    number: number
  }[]
}

interface GeneratedEpisode {
  number: number
  title: string
  videoUrl: string
}

export default function AnimeEpisodeUrlGenerator({ onAddEpisodes }: AnimeEpisodeUrlGeneratorProps) {
  const [animes, setAnimes] = useState<Anime[]>([])
  const [selectedAnimeSlug, setSelectedAnimeSlug] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedEpisodes, setGeneratedEpisodes] = useState<GeneratedEpisode[]>([])
  
  // URL pattern configuration
  const [baseUrl, setBaseUrl] = useState('https://lightspeedst.net')
  const [server, setServer] = useState('s5')
  const [format, setFormat] = useState('mp4')
  const [animeName, setAnimeName] = useState('')
  const [quality, setQuality] = useState('hd')
  const [startEpisode, setStartEpisode] = useState(1)
  const [endEpisode, setEndEpisode] = useState(12)
  const [urlPattern, setUrlPattern] = useState<string>('')
  const [useCustomPattern, setUseCustomPattern] = useState(false)
  const [customPattern, setCustomPattern] = useState('')

  // Fetch animes on component mount
  useEffect(() => {
    fetchAnimes()
  }, [])

  // Update URL pattern when configuration changes
  useEffect(() => {
    if (!useCustomPattern) {
      // Check if we should use the format with quality folder (hd) or direct episode number
      if (quality === 'hd' || quality === 'sd') {
        setUrlPattern(`${baseUrl}/${server}/${format}/${animeName}/${quality}/{episode}.mp4`)
      } else {
        setUrlPattern(`${baseUrl}/${server}/${format}/${animeName}/{episode}/${quality}.mp4`)
      }
    } else {
      setUrlPattern(customPattern)
    }
  }, [baseUrl, server, format, animeName, quality, useCustomPattern, customPattern])

  const fetchAnimes = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/animes')
      if (!response.ok) {
        throw new Error('Failed to fetch animes')
      }
      const data = await response.json()
      setAnimes(data)
    } catch (error) {
      console.error('Error fetching animes:', error)
      setError('Erro ao carregar animes')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAnimeSelect = (slug: string) => {
    setSelectedAnimeSlug(slug)
    const selectedAnime = animes.find(anime => anime.slug === slug)
    
    if (selectedAnime) {
      // Convert anime title to URL-friendly format for the pattern
      const formattedName = selectedAnime.title
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove accents
        .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric with hyphens
        .replace(/(^-|-$)/g, '') // Remove leading/trailing hyphens
      
      setAnimeName(formattedName)
      
      // Set end episode to match the total episodes if available
      if (selectedAnime.episodes && selectedAnime.episodes.length > 0) {
        const maxEpisode = Math.max(...selectedAnime.episodes.map(ep => ep.number))
        setEndEpisode(maxEpisode + 1) // Set to next episode
        setStartEpisode(maxEpisode + 1)
      }
    }
  }

  const generateEpisodes = () => {
    setError(null)
    
    if (!selectedAnimeSlug) {
      setError('Selecione um anime primeiro')
      return
    }
    
    if (!urlPattern) {
      setError('Padrão de URL inválido')
      return
    }
    
    if (startEpisode > endEpisode) {
      setError('O episódio inicial deve ser menor ou igual ao episódio final')
      return
    }
    
    try {
      setIsGenerating(true)
      const selectedAnime = animes.find(anime => anime.slug === selectedAnimeSlug)
      
      if (!selectedAnime) {
        throw new Error('Anime não encontrado')
      }
      
      const episodes: GeneratedEpisode[] = []
      
      for (let i = startEpisode; i <= endEpisode; i++) {
        const videoUrl = urlPattern.replace('{episode}', i.toString())
        episodes.push({
          number: i,
          title: `Episódio ${i}`,
          videoUrl
        })
      }
      
      setGeneratedEpisodes(episodes)
    } catch (error) {
      console.error('Error generating episodes:', error)
      setError('Erro ao gerar episódios')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAddEpisodes = async () => {
    if (!selectedAnimeSlug || generatedEpisodes.length === 0) return
    
    try {
      const selectedAnime = animes.find(anime => anime.slug === selectedAnimeSlug)
      
      if (!selectedAnime) {
        throw new Error('Anime não encontrado')
      }
      
      const episodesData = generatedEpisodes.map(episode => ({
        number: episode.number,
        title: episode.title,
        airDate: new Date().toISOString(),
        frame: selectedAnime.image,
        videoUrl: episode.videoUrl
      }))
      
      onAddEpisodes(selectedAnimeSlug, episodesData)
      
      // Reset form
      setGeneratedEpisodes([])
    } catch (error) {
      console.error('Error adding episodes:', error)
      setError('Erro ao adicionar episódios')
    }
  }

  const testUrl = (url: string) => {
    window.open(url, '_blank')
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
      <h2 className="text-xl font-bold mb-4">Gerador de URLs de Episódios</h2>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-300 mb-1">
          Selecione um Anime
        </label>
        <select
          value={selectedAnimeSlug}
          onChange={(e) => handleAnimeSelect(e.target.value)}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Selecione um anime...</option>
          {animes.map((anime) => (
            <option key={anime.id} value={anime.slug}>
              {anime.title}
            </option>
          ))}
        </select>
      </div>
      
      <div className="mb-4">
        <div className="flex items-center mb-2">
          <input
            type="checkbox"
            id="useCustomPattern"
            checked={useCustomPattern}
            onChange={(e) => setUseCustomPattern(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="useCustomPattern" className="text-sm font-medium text-gray-300">
            Usar padrão de URL personalizado
          </label>
        </div>
        
        {useCustomPattern ? (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Padrão de URL Personalizado
            </label>
            <input
              type="text"
              value={customPattern}
              onChange={(e) => setCustomPattern(e.target.value)}
              placeholder="https://lightspeedst.net/s5/mp4/anime-name/hd/{episode}.mp4"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-400 mt-1">
              Use {"{episode}"} para o número do episódio
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                URL Base
              </label>
              <input
                type="text"
                value={baseUrl}
                onChange={(e) => setBaseUrl(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Servidor
              </label>
              <select
                value={server}
                onChange={(e) => setServer(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="s1">s1</option>
                <option value="s2">s2</option>
                <option value="s3">s3</option>
                <option value="s4">s4</option>
                <option value="s5">s5</option>
                <option value="s6">s6</option>
                <option value="s7">s7</option>
                <option value="s8">s8</option>
                <option value="s9">s9</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Formato
              </label>
              <select
                value={format}
                onChange={(e) => setFormat(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="mp4">mp4</option>
                <option value="mp4_temp">mp4_temp</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Qualidade
              </label>
              <select
                value={quality}
                onChange={(e) => setQuality(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="hd">hd</option>
                <option value="sd">sd</option>
                <option value="720p">720p</option>
                <option value="480p">480p</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Nome do Anime (para URL)
              </label>
              <input
                type="text"
                value={animeName}
                onChange={(e) => setAnimeName(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Episódio Inicial
          </label>
          <input
            type="number"
            value={startEpisode}
            onChange={(e) => setStartEpisode(parseInt(e.target.value) || 1)}
            min="1"
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Episódio Final
          </label>
          <input
            type="number"
            value={endEpisode}
            onChange={(e) => setEndEpisode(parseInt(e.target.value) || 1)}
            min="1"
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-300 mb-1">
          Padrão de URL Gerado
        </label>
        <div className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-300 break-all">
          {urlPattern}
        </div>
      </div>
      
      <div className="flex justify-end mb-6">
        <button
          onClick={generateEpisodes}
          disabled={!selectedAnimeSlug || !urlPattern}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {isGenerating ? 'Gerando...' : 'Gerar Episódios'}
        </button>
      </div>
      
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-200">
          {error}
        </div>
      )}
      
      {generatedEpisodes.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Episódios Gerados ({generatedEpisodes.length})</h3>
          <div className="max-h-60 overflow-y-auto mb-4 bg-gray-700 border border-gray-600 rounded-lg">
            {generatedEpisodes.map((episode) => (
              <div
                key={episode.number}
                className="p-3 border-b border-gray-600 last:border-b-0 flex justify-between items-center"
              >
                <div>
                  <span className="font-medium">Episódio {episode.number}</span>
                  <p className="text-sm text-gray-400 break-all">{episode.videoUrl}</p>
                </div>
                <button
                  onClick={() => testUrl(episode.videoUrl)}
                  className="ml-2 p-1 text-blue-400 hover:text-blue-300"
                  title="Testar URL"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
          
          <button
            onClick={handleAddEpisodes}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Adicionar Episódios ao Anime
          </button>
        </div>
      )}
    </div>
  )
}
