'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface Anime {
  id: string
  title: string
  description: string
  image: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
  episodes: {
    id: string
    number: number
    title: string
    airDate: string
    frame: string
    videoUrl: string
  }[]
}

export default function AnimePage({ params }: { params: { id: string } }) {
  const [anime, setAnime] = useState<Anime | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchAnime()
  }, [params.id])

  const fetchAnime = async () => {
    try {
      const response = await fetch(`/api/animes/${params.id}`)
      const data = await response.json()
      setAnime(data)
    } catch (error) {
      console.error('Error fetching anime:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-screen">Carregando...</div>
  }

  if (!anime) {
    return <div className="flex justify-center items-center min-h-screen">Anime não encontrado</div>
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="relative h-[400px] rounded-lg overflow-hidden">
        <Image
          src={anime.image}
          alt={anime.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent">
          <div className="container mx-auto px-4 h-full flex items-end pb-8">
            <div className="max-w-3xl">
              <h1 className="text-4xl font-bold text-white mb-4">{anime.title}</h1>
              <div className="flex items-center space-x-4 text-white mb-4">
                <span>{anime.status}</span>
                <span>•</span>
                <span>{anime.totalEpisodes} Episódios</span>
                <span>•</span>
                <span>{anime.year}</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                {anime.genres.map((genre) => (
                  <span
                    key={genre}
                    className="px-3 py-1 bg-purple-600 text-white rounded-full text-sm"
                  >
                    {genre}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4">
        {/* Sinopse */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Sinopse</h2>
          <p className="text-gray-600 dark:text-gray-400">{anime.description}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Episodes */}
            <div>
              <h2 className="text-xl font-bold mb-4">Episódios</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {anime.episodes.map((episode) => (
                  <Link
                    key={episode.id}
                    href={`/anime/${params.id}/episode/${episode.number}`}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden hover:shadow-lg transition-shadow"
                  >
                    <div className="relative aspect-video">
                      <Image
                        src={episode.frame}
                        alt={`Episódio ${episode.number}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-medium mb-1">
                        Episódio {episode.number}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {episode.title}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                        {new Date(episode.airDate).toLocaleDateString()}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                Informações
              </h3>
              <div className="space-y-2">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Estúdio:</span>
                  <span className="ml-2 text-gray-800 dark:text-gray-200">{anime.studio}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Status:</span>
                  <span className="ml-2 text-gray-800 dark:text-gray-200">{anime.status}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Ano:</span>
                  <span className="ml-2 text-gray-800 dark:text-gray-200">{anime.year}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Episódios:</span>
                  <span className="ml-2 text-gray-800 dark:text-gray-200">{anime.totalEpisodes}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 