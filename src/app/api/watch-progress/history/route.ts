import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'
import { authOptions } from '@/lib/auth'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Busca o histórico de progresso do usuário
    const watchHistory = await prisma.comment.findMany({
      where: {
        userId: session.user.id,
        content: {
          contains: 'watch_progress'
        }
      },
      include: {
        episode: {
          select: {
            id: true,
            number: true,
            title: true,
            frame: true,
            anime: {
              select: {
                id: true,
                slug: true,
                title: true,
                image: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Processa o histórico para pegar apenas o último episódio de cada anime
    const latestEpisodesByAnime = new Map()
    
    watchHistory.forEach(progress => {
      if (!progress.episode) return // Pula se não houver episódio associado
      
      const animeId = progress.episode.anime.id
      if (!latestEpisodesByAnime.has(animeId)) {
        latestEpisodesByAnime.set(animeId, {
          id: progress.id,
          content: progress.content,
          createdAt: progress.createdAt,
          episode: {
            id: progress.episode.id,
            number: progress.episode.number,
            title: progress.episode.title,
            frame: progress.episode.frame,
            anime: progress.episode.anime
          }
        })
      }
    })

    // Converte o Map para array
    const uniqueWatchHistory = Array.from(latestEpisodesByAnime.values())

    return NextResponse.json(uniqueWatchHistory)
  } catch (error) {
    console.error('Error fetching watch history:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 