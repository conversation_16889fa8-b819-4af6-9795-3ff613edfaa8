# Sistema de Atualização de Títulos de Episódios

Este documento explica como funciona o sistema automático de atualização de títulos de episódios.

## 🎯 Objetivo

Resolver o problema de episódios importados sem títulos ou com títulos genéricos, mantendo-os sempre atualizados com os títulos corretos do MyAnimeList.

## 🔄 Como Funciona

### Atualização Automática (GitHub Actions)
- **Frequência**: Diariamente às 00:00 (horário de Brasília)
- **Processo**: Verifica episódios com títulos genéricos e busca títulos atualizados
- **Relatórios**: Gera relatórios automáticos de cada execução

### Atualização Manual (Interface Admin)
- **Localização**: `/admin/animes` → Seção "Atualização de Títulos de Episódios"
- **Opções**: Atualizar anime específico ou todos os animes
- **Controle**: Forçar atualização mesmo para episódios com títulos

## 🛠️ Configuração

### 1. Variáveis de Ambiente na Vercel

Adicione estas variáveis no painel da Vercel:

| Nome | Valor | Descrição |
|------|-------|-----------|
| `CRON_API_KEY` | `sua-chave-secreta` | Chave para autenticar GitHub Actions |
| `NEXT_PUBLIC_APP_URL` | `https://animeszera.com.br` | URL da sua aplicação |

### 2. Secrets do GitHub

Configure estes secrets no repositório GitHub:

1. Vá para **Settings** → **Secrets and variables** → **Actions**
2. Adicione os secrets:

| Nome | Valor |
|------|-------|
| `CRON_API_KEY` | Mesma chave configurada na Vercel |
| `NEXT_PUBLIC_APP_URL` | URL da sua aplicação |

## 📋 Títulos Detectados como Genéricos

O sistema identifica automaticamente estes padrões como títulos genéricos:

- `Episódio 1`, `Episódio 2`, etc.
- `Episode 1`, `Episode 2`, etc.
- `Ep. 1`, `Ep. 2`, etc.
- `Ep 1`, `Ep 2`, etc.
- Apenas números: `1`, `2`, etc.

## 🎮 Como Usar

### Execução Manual

1. Acesse `/admin/animes`
2. Vá para a seção "Atualização de Títulos de Episódios"
3. Configure as opções:
   - **ID do Anime**: Deixe vazio para todos ou especifique um anime
   - **Forçar atualização**: Marque para atualizar mesmo episódios com títulos
4. Clique em "Atualizar Títulos"

### Execução via GitHub Actions

#### Automática:
- Executa automaticamente todos os dias às 00:00

#### Manual:
1. Vá para **Actions** no GitHub
2. Selecione "Update Episode Titles"
3. Clique em "Run workflow"
4. Configure opções se necessário
5. Clique em "Run workflow"

## 📊 Relatórios e Logs

### Interface Admin
- **Resumo**: Total verificado, atualizados, erros, ignorados
- **Log detalhado**: Lista dos episódios processados
- **Timestamp**: Data/hora da última atualização

### GitHub Actions
- **Artifacts**: Relatórios detalhados salvos por 30 dias
- **Logs**: Logs completos da execução
- **Notificações**: Alertas em caso de falha

## 🔧 Estrutura Técnica

### API Endpoint
```
POST /api/episodes/update-titles
```

**Payload:**
```json
{
  "animeId": "opcional-id-do-anime",
  "forceUpdate": false
}
```

**Headers para GitHub Actions:**
```
Authorization: Bearer github-action
X-API-Key: sua-chave-cron
```

### Processo de Atualização

1. **Identificação**: Busca episódios com títulos genéricos
2. **Consulta**: Verifica títulos no MyAnimeList (API a ser implementada)
3. **Atualização**: Atualiza títulos no banco de dados
4. **Log**: Registra todas as alterações
5. **Relatório**: Gera resumo da execução

## 🚀 Próximos Passos

### Fase 1: Implementação Base ✅
- [x] API de atualização de títulos
- [x] GitHub Action com cron job
- [x] Interface de admin
- [x] Sistema de logs e relatórios

### Fase 2: Integração MyAnimeList (Próximo)
- [ ] Implementar busca real no MyAnimeList
- [ ] Cache de consultas para evitar rate limiting
- [ ] Fallback para outras fontes de dados

### Fase 3: Sistema de Tradução (Futuro)
- [ ] Detecção automática de idioma
- [ ] Tradução automática para português
- [ ] Interface para edições manuais
- [ ] Sistema de contribuição da comunidade

## 🛡️ Segurança

### Autenticação
- **Manual**: Requer login de admin
- **Automática**: Autenticada via API key secreta

### Rate Limiting
- **Lotes**: Processa episódios em lotes de 10
- **Pausas**: Pausa de 1 segundo entre lotes
- **Timeout**: Evita execuções muito longas

### Logs de Auditoria
- Todas as atualizações são logadas
- Timestamps de todas as operações
- Rastreamento de origem (manual vs automática)

## 🐛 Troubleshooting

### Problema: GitHub Action falha
**Soluções:**
1. Verificar se `CRON_API_KEY` está configurada
2. Confirmar se `NEXT_PUBLIC_APP_URL` está correto
3. Verificar logs da execução nos Actions

### Problema: API retorna erro 401
**Soluções:**
1. Verificar se a chave API está correta
2. Confirmar se as variáveis de ambiente estão configuradas
3. Verificar se o usuário tem permissões de admin

### Problema: Nenhum título é atualizado
**Soluções:**
1. Verificar se há episódios com títulos genéricos
2. Confirmar se a integração com MyAnimeList está funcionando
3. Verificar logs para erros específicos

## 📞 Monitoramento

### Métricas Importantes
- **Taxa de sucesso**: % de episódios atualizados com sucesso
- **Tempo de execução**: Duração de cada atualização
- **Erros**: Frequência e tipos de erros

### Alertas Recomendados
- Falhas consecutivas do cron job
- Taxa de erro acima de 10%
- Tempo de execução muito longo (>30 minutos)

---

**Nota**: Este sistema está preparado para integração futura com MyAnimeList e outros provedores de dados de anime.
