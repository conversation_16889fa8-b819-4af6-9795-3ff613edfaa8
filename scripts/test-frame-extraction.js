const { extractFrame } = require('../src/utils/video');
const path = require('path');

async function main() {
  try {
    const videoUrl = 'https://lightspeedst.net/s2/mp4/one-piece/sd/1.mp4';
    const outputPath = path.join(process.cwd(), 'public', 'frames', 'test-frame.jpg');
    
    console.log('Iniciando teste de extração de frame...');
    const result = await extractFrame(videoUrl, outputPath);
    console.log('Frame extraído com sucesso:', result);
  } catch (error) {
    console.error('Erro no teste:', error);
  }
}

main(); 