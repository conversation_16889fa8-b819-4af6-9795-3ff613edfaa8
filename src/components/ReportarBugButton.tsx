'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'

interface ReportarBugButtonProps {
  animeTitle?: string
  animeSlug?: string
}

export default function ReportarBugButton({ animeTitle, animeSlug }: ReportarBugButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [descricao, setDescricao] = useState('')
  const [tipo, setTipo] = useState('Erro de Vídeo')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!descricao.trim()) {
      toast.error('Por favor, descreva o problema')
      return
    }
    
    setIsLoading(true)
    
    try {
      const response = await fetch('/api/reportar-bug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          descricao, 
          tipo, 
          animeTitle, 
          animeSlug 
        }),
      })
      
      if (response.ok) {
        toast.success('Problema reportado com sucesso!')
        setDescricao('')
        setTipo('Erro de Vídeo')
        setIsOpen(false)
      } else {
        const data = await response.json()
        toast.error(data.error || 'Erro ao enviar relatório')
      }
    } catch (error) {
      console.error('Erro ao reportar bug:', error)
      toast.error('Erro ao enviar relatório. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      {/* Botão para abrir o modal */}
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-1 px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded-md text-xs transition-colors"
        title="Reportar um problema"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-4 w-4" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
          />
        </svg>
        <span>Reportar Problema</span>
      </button>

      {/* Modal de reportar bug */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
          <div 
            className="bg-gray-900 border border-gray-700 rounded-lg shadow-xl max-w-md w-full p-6 animate-fade-in"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-white">Reportar Problema</h3>
              <button 
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  className="h-6 w-6" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M6 18L18 6M6 6l12 12" 
                  />
                </svg>
              </button>
            </div>
            
            <p className="text-gray-300 mb-4">
              Encontrou algum problema? Informe-nos para que possamos corrigir o mais rápido possível.
              {animeTitle && (
                <span className="block mt-1 text-sm text-purple-400">
                  Anime: {animeTitle}
                </span>
              )}
            </p>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="tipo" className="block text-sm font-medium text-gray-300 mb-1">
                  Tipo de Problema
                </label>
                <select
                  id="tipo"
                  value={tipo}
                  onChange={(e) => setTipo(e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  <option value="Erro de Vídeo">Erro de Vídeo</option>
                  <option value="Episódio Faltando">Episódio Faltando</option>
                  <option value="Áudio com Problema">Áudio com Problema</option>
                  <option value="Legendas Incorretas">Legendas Incorretas</option>
                  <option value="Link Quebrado">Link Quebrado</option>
                  <option value="Outro">Outro</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="descricao" className="block text-sm font-medium text-gray-300 mb-1">
                  Descrição do Problema
                </label>
                <textarea
                  id="descricao"
                  value={descricao}
                  onChange={(e) => setDescricao(e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500"
                  placeholder="Descreva o problema em detalhes..."
                  rows={4}
                  required
                ></textarea>
              </div>
              
              <div className="flex justify-end pt-2">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="mr-2 px-4 py-2 text-gray-300 hover:text-white"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="bg-red-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-red-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Enviando...' : 'Enviar Relatório'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  )
}
