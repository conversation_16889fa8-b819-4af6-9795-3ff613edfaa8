import { NextResponse } from 'next/server';
import { sendEmail } from '@/lib/brevo';
import { sendEmailSmtp } from '@/lib/brevo-smtp';

export async function POST(request: Request) {
  try {
    const { nome, tipo } = await request.json();

    if (!nome) {
      return NextResponse.json(
        { error: 'Nome do anime é obrigatório' },
        { status: 400 }
      );
    }

    // Preparar o conteúdo do email
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #6200ea;">Nova Sugestão de Anime</h2>
        <p><strong>Nome do Anime:</strong> ${nome}</p>
        <p><strong>Tipo:</strong> ${tipo || 'Não especificado'}</p>
        <p>Esta sugestão foi enviada através do formulário de sugestão do site AnimesZera.</p>
      </div>
    `;

    // Email de destino (administrador do site)
    const adminEmail = '<EMAIL>';

    try {
      console.log('Enviando sugestão de anime por email');

      // Tentar enviar via API REST do Brevo primeiro
      if (process.env.BREVO_API_KEY) {
        try {
          await sendEmail({
            to: [{ email: adminEmail, name: 'Admin AnimesZera' }],
            subject: 'Nova Sugestão de Anime - AnimesZera',
            htmlContent,
          });

          console.log('Sugestão enviada via API REST do Brevo');
          return NextResponse.json(
            { success: true, message: 'Sugestão enviada com sucesso!' },
            { status: 200 }
          );
        } catch (apiError) {
          console.error('Falha ao enviar via API REST do Brevo, tentando SMTP como fallback:', apiError);
        }
      }

      // Fallback para SMTP do Brevo
      await sendEmailSmtp({
        to: adminEmail,
        subject: 'Nova Sugestão de Anime - AnimesZera',
        html: htmlContent,
      });

      console.log('Sugestão enviada via SMTP do Brevo');
      return NextResponse.json(
        { success: true, message: 'Sugestão enviada com sucesso!' },
        { status: 200 }
      );
    } catch (emailError) {
      console.error('Erro ao enviar email de sugestão:', emailError);
      return NextResponse.json(
        { error: 'Erro ao enviar sugestão. Por favor, tente novamente mais tarde.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Erro ao processar sugestão de anime:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
