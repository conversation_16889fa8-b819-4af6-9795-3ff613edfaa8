import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { translateEpisodeTitle, detectLanguage } from '@/services/translationService'

interface TranslationSummary {
  totalProcessed: number
  translated: number
  skipped: number
  errors: number
  timestamp: string
  translationLog: Array<{
    animeTitle: string
    episodeNumber: number
    originalTitle: string
    translatedTitle: string
    sourceLanguage: string
    confidence: number
    status: 'translated' | 'skipped' | 'error'
  }>
}

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const authHeader = request.headers.get('authorization')
    const isManualCall = !authHeader?.includes('github-action')
    
    if (isManualCall) {
      const session = await getServerSession(authOptions)
      if (!session?.user) {
        return NextResponse.json(
          { error: 'Não autorizado' },
          { status: 401 }
        )
      }
    } else {
      // Verificar se é uma chamada do GitHub Action
      const apiKey = request.headers.get('x-api-key')
      if (apiKey !== process.env.CRON_API_KEY) {
        return NextResponse.json(
          { error: 'API key inválida' },
          { status: 401 }
        )
      }
    }

    let body
    try {
      body = await request.json()
    } catch (parseError) {
      console.error('Erro ao fazer parse do JSON:', parseError)
      return NextResponse.json(
        { error: 'JSON inválido na requisição' },
        { status: 400 }
      )
    }
    
    const { animeId, forceTranslate = false, targetLanguage = 'pt' } = body

    console.log('🌐 Iniciando tradução de títulos de episódios...')

    // Buscar episódios que precisam de tradução
    // Filtrar apenas animes em lançamento/andamento
    const whereClause = animeId 
      ? { 
          animeId,
          anime: {
            OR: [
              { status: 'Em lançamento' },
              { status: 'Em Andamento' }
            ]
          }
        } 
      : forceTranslate 
        ? {
            anime: {
              OR: [
                { status: 'Em lançamento' },
                { status: 'Em Andamento' }
              ]
            }
          }
        : {
            anime: {
              OR: [
                { status: 'Em lançamento' },
                { status: 'Em Andamento' }
              ]
            },
            OR: [
              { translatedTitle: null },
              { translatedTitle: '' }
            ]
          }

    const episodes = await prisma.episode.findMany({
      where: whereClause,
      include: {
        anime: {
          select: {
            id: true,
            title: true,
            status: true
          }
        }
      },
      orderBy: [
        { animeId: 'asc' },
        { number: 'asc' }
      ]
    })

    console.log(`📊 Encontrados ${episodes.length} episódios para traduzir`)

    let translatedCount = 0
    let skippedCount = 0
    let errorCount = 0
    const translationLog: TranslationSummary['translationLog'] = []

    // Processar episódios em lotes para evitar sobrecarga
    const batchSize = 5 // Menor que títulos para evitar rate limiting
    for (let i = 0; i < episodes.length; i += batchSize) {
      const batch = episodes.slice(i, i + batchSize)
      
      await Promise.all(batch.map(async (episode) => {
        try {
          // Verificar se já tem tradução e não é forçado
          if (!forceTranslate && episode.translatedTitle) {
            translationLog.push({
              animeTitle: episode.anime.title,
              episodeNumber: episode.number,
              originalTitle: episode.title,
              translatedTitle: episode.translatedTitle,
              sourceLanguage: episode.detectedLanguage || 'unknown',
              confidence: 0.8,
              status: 'skipped'
            })
            skippedCount++
            return
          }

          // Detectar idioma se não foi detectado antes
          let detectedLanguage = episode.detectedLanguage
          if (!detectedLanguage) {
            const detection = await detectLanguage(episode.title)
            detectedLanguage = detection.language
          }

          // Pular se já está no idioma alvo
          if (detectedLanguage === targetLanguage) {
            translationLog.push({
              animeTitle: episode.anime.title,
              episodeNumber: episode.number,
              originalTitle: episode.title,
              translatedTitle: episode.title,
              sourceLanguage: detectedLanguage,
              confidence: 1.0,
              status: 'skipped'
            })
            skippedCount++
            return
          }

          // Traduzir título
          const translation = await translateEpisodeTitle(episode.title, targetLanguage)
          
          // Atualizar episódio no banco
          await prisma.episode.update({
            where: { id: episode.id },
            data: {
              originalTitle: episode.title,
              translatedTitle: translation.translatedText,
              detectedLanguage: translation.sourceLanguage,
              lastTitleUpdate: new Date()
            }
          })

          translatedCount++
          translationLog.push({
            animeTitle: episode.anime.title,
            episodeNumber: episode.number,
            originalTitle: episode.title,
            translatedTitle: translation.translatedText,
            sourceLanguage: translation.sourceLanguage,
            confidence: translation.confidence,
            status: 'translated'
          })

          console.log(`✅ Traduzido: ${episode.anime.title} - Ep ${episode.number}: "${episode.title}" → "${translation.translatedText}"`)
        } catch (error) {
          errorCount++
          console.error(`❌ Erro ao traduzir episódio ${episode.number} de ${episode.anime.title}:`, error)
          
          translationLog.push({
            animeTitle: episode.anime.title,
            episodeNumber: episode.number,
            originalTitle: episode.title,
            translatedTitle: episode.title,
            sourceLanguage: 'unknown',
            confidence: 0,
            status: 'error'
          })
        }
      }))

      // Pausa entre lotes para evitar rate limiting
      if (i + batchSize < episodes.length) {
        await new Promise(resolve => setTimeout(resolve, 2000)) // 2 segundos
      }
    }

    const summary: TranslationSummary = {
      totalProcessed: episodes.length,
      translated: translatedCount,
      skipped: skippedCount,
      errors: errorCount,
      timestamp: new Date().toISOString(),
      translationLog: translationLog.slice(0, 50) // Limitar log
    }

    console.log('📈 Resumo da tradução:', summary)

    return NextResponse.json({
      success: true,
      message: `Tradução concluída: ${translatedCount} títulos traduzidos, ${errorCount} erros`,
      summary
    })

  } catch (error) {
    console.error('Erro na tradução de títulos:', error)
    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    )
  }
}
