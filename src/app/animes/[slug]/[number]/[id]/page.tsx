'use client'

import { useState, useEffect, useRef } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'

interface Anime {
  id: string
  title: string
  description: string
  image: string
  status: string
  totalEpisodes: number
  studio: string
  year: number
  genres: string[]
  episodes: {
    id: string
    number: number
    title: string
    airDate: string
    frame: string
    videoUrl: string
  }[]
}

export default function AnimeDetailsPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const [anime, setAnime] = useState<Anime | null>(null)
  const [selectedEpisode, setSelectedEpisode] = useState<Anime['episodes'][0] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const videoRef = useRef<HTMLVideoElement>(null)
  const [playerKey, setPlayerKey] = useState(0)
  const [showEpisodes, setShowEpisodes] = useState(false)
  const [episodeSearch, setEpisodeSearch] = useState('')
  const [sortOrder, setSortOrder] = useState<'date' | 'number'>('date')

  useEffect(() => {
    fetchAnime()
  }, [params.id])

  useEffect(() => {
    if (anime && anime.episodes) {
      const episodeNumber = searchParams.get('episode')
      if (episodeNumber) {
        const episode = anime.episodes.find(ep => ep.number === parseInt(episodeNumber))
        if (episode) {
          setSelectedEpisode(episode)
          setShowEpisodes(true)
        }
      } else if (anime.episodes.length > 0) {
        setSelectedEpisode(anime.episodes[0])
      }
    }
  }, [anime, searchParams])

  // Removido useEffect de log de episódio selecionado

  useEffect(() => {
    if (selectedEpisode) {
      // Força um novo carregamento do player
      setPlayerKey(prev => prev + 1)
    }
  }, [selectedEpisode])

  const fetchAnime = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/animes/${params.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch anime')
      }
      const data = await response.json()
      setAnime(data)
    } catch (error) {
      console.error('Error fetching anime:', error)
      alert('Erro ao carregar anime')
    } finally {
      setIsLoading(false)
    }
  }

  const handleNextEpisode = () => {
    if (selectedEpisode && anime) {
      const nextEpisode = anime.episodes.find(ep => ep.number === selectedEpisode.number + 1)
      if (nextEpisode) {
        setSelectedEpisode(nextEpisode)
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }
    }
  }

  const handlePrevEpisode = () => {
    if (selectedEpisode && anime) {
      const prevEpisode = anime.episodes.find(ep => ep.number === selectedEpisode.number - 1)
      if (prevEpisode) {
        setSelectedEpisode(prevEpisode)
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }
    }
  }

  const filteredEpisodes = anime?.episodes
    .filter(episode =>
      episodeSearch === '' ||
      episode.number.toString().includes(episodeSearch) ||
      episode.title.toLowerCase().includes(episodeSearch.toLowerCase())
    )
    .sort((a, b) => {
      if (sortOrder === 'date') {
        return new Date(b.airDate).getTime() - new Date(a.airDate).getTime()
      }
      return a.number - b.number
    })

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900 text-white">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (!anime) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900 text-white">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Anime não encontrado</h2>
          <Link href="/animes" className="text-blue-400 hover:text-blue-300">
            Voltar para lista de animes
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header com imagem de fundo */}
      <div className="relative h-[400px] w-full">
        <Image
          src={anime.image}
          alt={anime.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/80 to-transparent">
          <div className="container mx-auto px-4 h-full flex items-end pb-8">
            <div className="max-w-3xl">
              <Link href="/animes" className="text-blue-400 hover:text-blue-300 flex items-center mb-4">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Voltar para lista de animes
              </Link>
              <h1 className="text-4xl font-bold mb-4">{anime.title}</h1>
              <div className="flex flex-wrap gap-2 mb-4">
                {anime.genres.map((genre) => (
                  <span
                    key={genre}
                    className="px-3 py-1 bg-purple-600 text-white rounded-full text-sm"
                  >
                    {genre}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Informações do Anime */}
          <div className="lg:col-span-1 space-y-6">
            <div className="bg-gray-800 rounded-xl p-6">
              <h2 className="text-xl font-bold mb-4">Informações</h2>
              <div className="space-y-4">
                <div className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span>Status: {anime.status}</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <span>Total de Episódios: {anime.totalEpisodes}</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  <span>Estúdio: {anime.studio}</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>Ano: {anime.year}</span>
                </div>
              </div>
            </div>

            {/* Sinopse */}
            <div className="bg-gray-800 rounded-xl p-6">
              <h2 className="text-xl font-bold mb-4">Sinopse</h2>
              <p className="text-gray-300 leading-relaxed">
                {anime.description}
              </p>
            </div>
          </div>

          {/* Player e Lista de Episódios */}
          <div className="lg:col-span-3 space-y-8">
            {/* Player de Vídeo */}
            <div className="bg-gray-800 rounded-xl shadow-lg overflow-hidden">
              <div className="relative aspect-video">
                {selectedEpisode ? (
                  <div key={playerKey}>
                    <video
                      ref={videoRef}
                      className="w-full h-full"
                      controls
                      playsInline
                      preload="auto"
                      onError={(e) => {
                        console.error('Erro no player:', e)
                        alert('Erro ao carregar o vídeo. Tente novamente mais tarde.')
                      }}
                    >
                      <source
                        src={`/api/video?url=${encodeURIComponent(selectedEpisode.videoUrl)}&t=${Date.now()}`}
                        type="video/mp4"
                      />
                      Seu navegador não suporta o elemento de vídeo.
                    </video>
                  </div>
                ) : (
                  <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                    <p className="text-gray-400">Selecione um episódio</p>
                  </div>
                )}
              </div>

              {/* Controles de Navegação */}
              {selectedEpisode && (
                <div className="p-4 border-t border-gray-700 flex justify-between items-center">
                  <button
                    onClick={handlePrevEpisode}
                    disabled={!anime.episodes.find(ep => ep.number === selectedEpisode.number - 1)}
                    className="flex items-center px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    Episódio Anterior
                  </button>
                  <span className="text-lg font-medium">
                    Episódio {selectedEpisode.number}
                  </span>
                  <button
                    onClick={handleNextEpisode}
                    disabled={!anime.episodes.find(ep => ep.number === selectedEpisode.number + 1)}
                    className="flex items-center px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Próximo Episódio
                    <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              )}
            </div>

            {/* Lista de Episódios */}
            <div className="bg-gray-800 rounded-xl p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">Episódios</h2>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Buscar episódio..."
                      value={episodeSearch}
                      onChange={(e) => setEpisodeSearch(e.target.value)}
                      className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pl-10"
                    />
                    <svg
                      className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                  <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as 'date' | 'number')}
                    className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="date">Mais Recentes</option>
                    <option value="number">Número do Episódio</option>
                  </select>
                  <button
                    onClick={() => setShowEpisodes(!showEpisodes)}
                    className="flex items-center text-gray-400 hover:text-white"
                  >
                    {showEpisodes ? 'Ocultar' : 'Mostrar'} Episódios
                    <svg
                      className={`w-5 h-5 ml-2 transform transition-transform ${showEpisodes ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              </div>

              {showEpisodes && (
                <div className="relative">
                  <div className="max-h-[600px] overflow-y-auto pr-2 custom-scrollbar">
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                      {filteredEpisodes?.map((episode) => (
                        <button
                          key={episode.id}
                          onClick={() => {
                            setSelectedEpisode(episode)
                            window.scrollTo({ top: 0, behavior: 'smooth' })
                          }}
                          className={`relative group rounded-lg overflow-hidden transition-all ${
                            selectedEpisode?.id === episode.id
                              ? 'ring-2 ring-blue-500'
                              : 'hover:ring-2 hover:ring-blue-500/50'
                          }`}
                        >
                          <div className="aspect-video relative">
                            <Image
                              src={episode.frame}
                              alt={`Episódio ${episode.number}`}
                              fill
                              className="object-cover"
                            />
                            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                              <span className="text-white font-medium">Assistir</span>
                            </div>
                          </div>
                          <div className="p-2 bg-gray-700">
                            <div className="font-medium text-sm">Episódio {episode.number}</div>
                            <div className="text-xs text-gray-400 mt-1">
                              {new Date(episode.airDate).toLocaleDateString('pt-BR')}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                  {filteredEpisodes?.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      Nenhum episódio encontrado
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #1f2937;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #4b5563;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #6b7280;
        }
      `}</style>
    </div>
  )
}

function extractVideoId(url: string): string {
  if (!url) return ''

  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)

  if (!match || match[2].length !== 11) {
    console.warn('URL do vídeo inválida:', url)
    return ''
  }

  return match[2]
}