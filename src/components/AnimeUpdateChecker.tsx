'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'

interface UpdateResult {
  animeId: string
  animeTitle: string
  animeSlug: string
  currentEpisodes: number
  newEpisodes: number
  episodesAdded: number
  success: boolean
  error?: string
}

interface CheckUpdatesResponse {
  results: UpdateResult[]
  totalChecked: number
  totalUpdated: number
  totalEpisodesAdded: number
}

export default function AnimeUpdateChecker({ animeId, onUpdateComplete }: { animeId?: string, onUpdateComplete?: () => void }) {
  const [isChecking, setIsChecking] = useState(false)
  const [results, setResults] = useState<CheckUpdatesResponse | null>(null)
  const [showResults, setShowResults] = useState(false)

  const checkUpdates = async () => {
    setIsChecking(true)
    setResults(null)
    setShowResults(true)

    try {
      // Construir a URL da API
      const apiUrl = animeId
        ? `/api/animes/check-updates?animeId=${animeId}`
        : '/api/animes/check-updates'

      // Verificar se estamos no ambiente do navegador ou do servidor
      let fullApiUrl = apiUrl

      // No ambiente do navegador, usar URL absoluta
      if (typeof window !== 'undefined') {
        fullApiUrl = `${window.location.origin}${apiUrl}`
      }

      const response = await fetch(fullApiUrl, {
        method: 'GET',
        credentials: 'include', // Importante: inclui cookies de autenticação
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      })

      if (!response.ok) {
        throw new Error(`Erro ao verificar atualizações: ${response.status} ${response.statusText}`)
      }

      const data: CheckUpdatesResponse = await response.json()
      setResults(data)

      // Mostrar mensagem de sucesso
      if (data.totalEpisodesAdded > 0) {
        toast.success(`${data.totalEpisodesAdded} episódio(s) adicionado(s) para ${data.totalUpdated} anime(s)!`)
      } else {
        // Verificar se houve algum erro
        const hasErrors = data.results.some(result => !result.success);
        if (hasErrors) {
          toast.warning('Verificação concluída com alguns erros. Verifique os detalhes abaixo.');
        } else {
          toast.success('Verificação concluída. Nenhum episódio faltante encontrado.');
        }
      }

      // Chamar o callback se fornecido
      if (onUpdateComplete) {
        onUpdateComplete()
      }
    } catch (error) {
      console.error('Erro ao verificar atualizações:', error)
      toast.error('Erro ao verificar atualizações')
    } finally {
      setIsChecking(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-200">
            {animeId ? 'Verificar Episódios Faltantes' : 'Verificar Atualizações de Animes em Lançamento'}
          </h3>
          <p className="text-sm text-gray-400 mt-1">
            {animeId
              ? 'Verifica se há episódios faltantes para este anime e os adiciona automaticamente.'
              : 'Verifica todos os animes em lançamento e adiciona episódios faltantes automaticamente.'}
          </p>
        </div>
        <button
          onClick={checkUpdates}
          disabled={isChecking}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {isChecking ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Verificando...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Verificar Episódios
            </>
          )}
        </button>
      </div>

      {showResults && (
        <div className="mt-4">
          {isChecking ? (
            <div className="flex flex-col items-center justify-center p-6 bg-gray-800 rounded-lg">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mb-4"></div>
              <p className="text-gray-300">Verificando atualizações de animes...</p>
              <p className="text-gray-400 text-sm mt-2">Isso pode levar alguns minutos.</p>
            </div>
          ) : results ? (
            <div className="bg-gray-800 rounded-lg overflow-hidden">
              <div className="p-4 bg-gray-700">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-800 p-3 rounded-lg">
                    <p className="text-sm text-gray-400">Animes Verificados</p>
                    <p className="text-2xl font-bold text-white">{results.totalChecked}</p>
                  </div>
                  <div className="bg-gray-800 p-3 rounded-lg">
                    <p className="text-sm text-gray-400">Animes Atualizados</p>
                    <p className="text-2xl font-bold text-white">{results.totalUpdated}</p>
                  </div>
                  <div className="bg-gray-800 p-3 rounded-lg col-span-2">
                    <p className="text-sm text-gray-400">Novos Episódios Adicionados</p>
                    <p className="text-2xl font-bold text-white">{results.totalEpisodesAdded}</p>
                  </div>
                </div>
              </div>

              {results.results.length > 0 && (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-700">
                    <thead className="bg-gray-700">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                          Anime
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                          Episódios Atuais
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                          Novos Episódios
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-gray-800 divide-y divide-gray-700">
                      {results.results.map((result) => (
                        <tr key={result.animeId} className={!result.success ? 'bg-red-900/20' : result.episodesAdded > 0 ? 'bg-green-900/20' : ''}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {result.animeTitle}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {result.currentEpisodes}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {result.episodesAdded}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {!result.success ? (
                              <span className="px-2 py-1 text-xs rounded-full bg-red-900/50 text-red-200">
                                Erro: {result.error}
                              </span>
                            ) : result.episodesAdded > 0 ? (
                              <span className="px-2 py-1 text-xs rounded-full bg-green-900/50 text-green-200">
                                {result.episodesAdded} episódio(s) adicionado(s)
                              </span>
                            ) : (
                              <span className="px-2 py-1 text-xs rounded-full bg-gray-700 text-gray-300">
                                Sem atualizações
                              </span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          ) : (
            <div className="p-6 bg-gray-800 rounded-lg text-center">
              <p className="text-gray-300">Nenhum resultado disponível.</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
