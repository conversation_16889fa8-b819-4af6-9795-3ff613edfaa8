import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// Função para buscar títulos de episódios do MyAnimeList
async function getEpisodeTitlesFromMAL(animeTitle: string, episodeNumber: number): Promise<string | null> {
  try {
    // Simular busca no MyAnimeList (você pode implementar a lógica real aqui)
    // Por enquanto, vamos usar uma lógica simples para demonstração

    // Aqui você implementaria a chamada real para a API do MyAnimeList
    // const response = await fetch(`https://api.myanimelist.net/v2/anime/search?q=${encodeURIComponent(animeTitle)}`)

    // Por enquanto, retornamos null para indicar que não encontrou título
    return null
  } catch (error) {
    console.error('Erro ao buscar título no MyAnimeList:', error)
    return null
  }
}

// Função para verificar se um título é genérico
function isGenericTitle(title: string): boolean {
  const genericPatterns = [
    /^Episódio \d+$/i,
    /^Episode \d+$/i,
    /^Ep\. \d+$/i,
    /^Ep \d+$/i,
    /^\d+$/,
  ]

  return genericPatterns.some(pattern => pattern.test(title.trim()))
}

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação (para chamadas manuais)
    const authHeader = request.headers.get('authorization')
    const isManualCall = !authHeader?.includes('github-action')

    if (isManualCall) {
      const session = await getServerSession(authOptions)
      if (!session?.user) {
        return NextResponse.json(
          { error: 'Não autorizado' },
          { status: 401 }
        )
      }
    } else {
      // Verificar se é uma chamada do GitHub Action
      const apiKey = request.headers.get('x-api-key')
      if (apiKey !== process.env.CRON_API_KEY) {
        return NextResponse.json(
          { error: 'API key inválida' },
          { status: 401 }
        )
      }
    }

    let body
    try {
      body = await request.json()
    } catch (parseError) {
      console.error('Erro ao fazer parse do JSON:', parseError)
      return NextResponse.json(
        { error: 'JSON inválido na requisição' },
        { status: 400 }
      )
    }

    const { animeId, forceUpdate = false } = body

    console.log('🔄 Iniciando atualização de títulos de episódios...')

    // Buscar episódios que precisam de atualização
    // Filtrar apenas animes em lançamento
    const whereClause = animeId
      ? {
          animeId,
          anime: {
            OR: [
              { status: 'Em lançamento' },
              { status: 'Em Andamento' }
            ]
          }
        }
      : forceUpdate
        ? {
            anime: {
              OR: [
                { status: 'Em lançamento' },
                { status: 'Em Andamento' }
              ]
            }
          }
        : {
            anime: {
              OR: [
                { status: 'Em lançamento' },
                { status: 'Em Andamento' }
              ]
            },
            OR: [
              { title: { contains: 'Episódio' } },
              { title: { contains: 'Episode' } },
              { title: { contains: 'Ep.' } },
              { title: { contains: 'Ep ' } },
            ]
          }

    const episodes = await prisma.episode.findMany({
      where: whereClause,
      include: {
        anime: {
          select: {
            id: true,
            title: true,
            malId: true
          }
        }
      },
      orderBy: [
        { animeId: 'asc' },
        { number: 'asc' }
      ]
    })

    console.log(`📊 Encontrados ${episodes.length} episódios para verificar`)

    let updatedCount = 0
    let errorCount = 0
    const updateLog: Array<{
      animeTitle: string
      episodeNumber: number
      oldTitle: string
      newTitle: string
      status: 'updated' | 'error' | 'skipped'
    }> = []

    // Processar episódios em lotes para evitar sobrecarga
    const batchSize = 10
    for (let i = 0; i < episodes.length; i += batchSize) {
      const batch = episodes.slice(i, i + batchSize)

      await Promise.all(batch.map(async (episode) => {
        try {
          // Verificar se o título é genérico ou se é atualização forçada
          if (!forceUpdate && !isGenericTitle(episode.title)) {
            updateLog.push({
              animeTitle: episode.anime.title,
              episodeNumber: episode.number,
              oldTitle: episode.title,
              newTitle: episode.title,
              status: 'skipped'
            })
            return
          }

          // Buscar título atualizado no MyAnimeList
          const newTitle = await getEpisodeTitlesFromMAL(episode.anime.title, episode.number)

          if (newTitle && newTitle !== episode.title) {
            // Atualizar título no banco de dados
            await prisma.episode.update({
              where: { id: episode.id },
              data: {
                title: newTitle,
                updatedAt: new Date()
              }
            })

            updatedCount++
            updateLog.push({
              animeTitle: episode.anime.title,
              episodeNumber: episode.number,
              oldTitle: episode.title,
              newTitle: newTitle,
              status: 'updated'
            })

            console.log(`✅ Atualizado: ${episode.anime.title} - Ep ${episode.number}: "${episode.title}" → "${newTitle}"`)
          } else {
            updateLog.push({
              animeTitle: episode.anime.title,
              episodeNumber: episode.number,
              oldTitle: episode.title,
              newTitle: episode.title,
              status: 'skipped'
            })
          }
        } catch (error) {
          errorCount++
          console.error(`❌ Erro ao atualizar episódio ${episode.number} de ${episode.anime.title}:`, error)

          updateLog.push({
            animeTitle: episode.anime.title,
            episodeNumber: episode.number,
            oldTitle: episode.title,
            newTitle: episode.title,
            status: 'error'
          })
        }
      }))

      // Pequena pausa entre lotes
      if (i + batchSize < episodes.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    const summary = {
      totalChecked: episodes.length,
      updated: updatedCount,
      errors: errorCount,
      skipped: episodes.length - updatedCount - errorCount,
      timestamp: new Date().toISOString(),
      updateLog: updateLog.slice(0, 50) // Limitar log para não sobrecarregar resposta
    }

    console.log('📈 Resumo da atualização:', summary)

    return NextResponse.json({
      success: true,
      message: `Atualização concluída: ${updatedCount} títulos atualizados, ${errorCount} erros`,
      summary
    })

  } catch (error) {
    console.error('Erro na atualização de títulos:', error)
    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    )
  }
}
