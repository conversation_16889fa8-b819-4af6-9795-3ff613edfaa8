import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import { generateAnimeUsername } from '@/lib/animeNames'

export default function AvatarUpload() {
  const { data: session, status, update } = useSession()
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState('')

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Verifica o tipo do arquivo
    if (!file.type.startsWith('image/')) {
      setError('Por favor, selecione apenas arquivos de imagem')
      return
    }

    // Verifica o tamanho do arquivo (máximo 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('O arquivo deve ter no máximo 5MB')
      return
    }

    try {
      setIsUpdating(true)
      setError('')

      // Converte a imagem para base64
      const reader = new FileReader()
      
      reader.onload = async () => {
        try {
          const base64Image = reader.result as string
          console.log('Imagem convertida para base64 com sucesso')

          if (!session?.user?.id) {
            throw new Error('Usuário não autenticado')
          }

          // Gera um nome de usuário aleatório se não existir
          const username = session.user.name || generateAnimeUsername()
          console.log('Usando nome de usuário:', username)

          // Atualiza o perfil do usuário
          console.log('Enviando requisição para atualizar avatar...')
          const response = await fetch('/api/user/profile', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              username,
              avatar: base64Image,
            }),
          })

          console.log('Resposta recebida:', response.status)
          const data = await response.json()
          console.log('Dados da resposta:', data)

          if (!response.ok) {
            throw new Error(data.error || 'Erro ao atualizar avatar')
          }

          // Atualiza a sessão
          console.log('Atualizando sessão...')
          const newSession = {
            ...session,
            user: {
              ...session.user,
              name: username,
              avatar: data.avatar,
            },
          }
          await update(newSession)
          console.log('Sessão atualizada com sucesso')
        } catch (error) {
          console.error('Erro durante o processamento do avatar:', error)
          setError(error instanceof Error ? error.message : 'Erro ao processar avatar')
        }
      }

      reader.onerror = () => {
        console.error('Erro ao ler o arquivo')
        setError('Erro ao ler o arquivo. Tente novamente.')
      }

      console.log('Iniciando leitura do arquivo...')
      reader.readAsDataURL(file)
    } catch (error) {
      console.error('Erro ao iniciar upload:', error)
      setError(error instanceof Error ? error.message : 'Erro ao iniciar upload')
    } finally {
      setIsUpdating(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="flex-shrink-0">
        <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-purple-500 mx-auto bg-white/10 animate-pulse"></div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="flex-shrink-0">
        <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-purple-500 mx-auto bg-white/10 flex items-center justify-center">
          <span className="text-6xl">👤</span>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-shrink-0">
      <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-purple-500 mx-auto bg-white/10 flex items-center justify-center relative">
        {session?.user?.avatar ? (
          <Image
            src={session.user.avatar}
            alt="Avatar"
            width={128}
            height={128}
            className="object-cover w-full h-full"
            unoptimized
            priority
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500"></div>
        )}
      </div>

      <div className="mt-4 text-center">
        <label className={`cursor-pointer inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors ${isUpdating ? 'opacity-50 cursor-not-allowed' : ''}`}>
          <span className="mr-2">{isUpdating ? 'Enviando...' : 'Alterar Avatar'}</span>
          <input
            type="file"
            accept="image/jpeg,image/png,image/gif,image/webp"
            onChange={handleFileChange}
            className="hidden"
            disabled={isUpdating}
          />
        </label>
        {error && (
          <p className="mt-2 text-red-500 text-sm">{error}</p>
        )}
        <div className="mt-2 space-y-1">
          <p className="text-sm text-gray-400">
            Formatos aceitos: JPG, PNG, GIF, WEBP
          </p>
          <p className="text-sm text-gray-400">
            Tamanho máximo: 5MB
          </p>
          <p className="text-sm text-gray-400">
            Aviso: Não use avatares com conteúdo impróprio. Sua conta pode ser banida.
          </p>
        </div>
      </div>
    </div>
  )
} 