import { NextResponse } from 'next/server';
import { sendEmail } from '@/lib/brevo';
import { sendEmailSmtp } from '@/lib/brevo-smtp';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(request: Request) {
  try {
    const { descricao, tipo, animeTitle, animeSlug } = await request.json();
    const session = await getServerSession(authOptions);

    if (!descricao) {
      return NextResponse.json(
        { error: 'Descrição do problema é obrigatória' },
        { status: 400 }
      );
    }

    // Obter informações do usuário se estiver logado
    const userEmail = session?.user?.email || 'Usuário não logado';
    const userName = session?.user?.name || 'Anônimo';

    // Preparar o conteúdo do email
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #e53e3e;">Relatório de Problema - AnimesZera</h2>
        
        <div style="background-color: #f8f8f8; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <p><strong>Tipo de Problema:</strong> ${tipo}</p>
          <p><strong>Anime:</strong> ${animeTitle || 'Não especificado'}</p>
          ${animeSlug ? `<p><strong>Link:</strong> <a href="https://animeszera.com.br/animes/${animeSlug}">https://animeszera.com.br/animes/${animeSlug}</a></p>` : ''}
          <p><strong>Reportado por:</strong> ${userName} (${userEmail})</p>
        </div>
        
        <h3 style="color: #4a5568;">Descrição do Problema:</h3>
        <div style="background-color: #f8f8f8; padding: 15px; border-radius: 5px; white-space: pre-wrap;">
          ${descricao}
        </div>
        
        <p style="margin-top: 20px; font-size: 12px; color: #718096;">
          Este relatório foi enviado através do formulário de reportar problemas do site AnimesZera.
        </p>
      </div>
    `;

    // Email de destino (administrador do site)
    const adminEmail = '<EMAIL>';

    try {
      console.log('Enviando relatório de problema por email');

      // Tentar enviar via API REST do Brevo primeiro
      if (process.env.BREVO_API_KEY) {
        try {
          await sendEmail({
            to: [{ email: adminEmail, name: 'Admin AnimesZera' }],
            subject: `[BUG] ${tipo} - ${animeTitle || 'Não especificado'}`,
            htmlContent,
          });

          console.log('Relatório enviado via API REST do Brevo');
          return NextResponse.json(
            { success: true, message: 'Problema reportado com sucesso!' },
            { status: 200 }
          );
        } catch (apiError) {
          console.error('Falha ao enviar via API REST do Brevo, tentando SMTP como fallback:', apiError);
        }
      }

      // Fallback para SMTP do Brevo
      await sendEmailSmtp({
        to: adminEmail,
        subject: `[BUG] ${tipo} - ${animeTitle || 'Não especificado'}`,
        html: htmlContent,
      });

      console.log('Relatório enviado via SMTP do Brevo');
      return NextResponse.json(
        { success: true, message: 'Problema reportado com sucesso!' },
        { status: 200 }
      );
    } catch (emailError) {
      console.error('Erro ao enviar email de relatório:', emailError);
      return NextResponse.json(
        { error: 'Erro ao enviar relatório. Por favor, tente novamente mais tarde.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Erro ao processar relatório de problema:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
