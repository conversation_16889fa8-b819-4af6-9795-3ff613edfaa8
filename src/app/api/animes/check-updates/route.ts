import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

/**
 * Verifica atualizações para todos os animes em lançamento
 * ou para um anime específico se o ID for fornecido
 */
export async function GET(request: Request) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions)
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    // Obter parâmetros da requisição
    const { searchParams } = new URL(request.url)
    const animeId = searchParams.get('animeId')

    // Interface para a resposta da API do AnimeFireAPI
    interface AnimeFireResponse {
      anime_slug: string
      anime_title: string
      anime_title1: string
      anime_image: string
      anime_info: string
      anime_synopsis: string
      anime_score: string
      anime_votes: string
      youtube_trailer: string | null
      episodes: {
        episode: number
        data: {
          url: string
          resolution: string
          status: string
        }[]
      }[]
      metadata: {
        op_start: string | null
        op_end: string | null
      }
      response: {
        status: string
        text: string
      }
    }

    // Interface para o resultado da verificação
    interface UpdateResult {
      animeId: string
      animeTitle: string
      animeSlug: string
      currentEpisodes: number
      newEpisodes: number
      episodesAdded: number
      success: boolean
      error?: string
    }

    // Função para verificar atualizações de um anime
    async function checkAnimeUpdates(
      animeId: string,
      animeTitle: string,
      animeSlug: string,
      currentEpisodeCount: number
    ): Promise<UpdateResult> {
      try {
        // Construir a URL do anime no AnimeFirePlus
        const animeFireUrl = `https://animefire.plus/animes/${animeSlug}-todos-os-episodios`;

        // Chave da API
        const apiKey = '0sYiHJHn/2uoVdlwgqcoTgzL3VqSIVw76XTeEVa7DfA=';

        // URL da API
        const apiUrl = `http://15.229.117.80/api?api_key=${encodeURIComponent(apiKey)}&anime_link=${encodeURIComponent(animeFireUrl)}`;

        console.log(`Verificando atualizações para ${animeTitle} (${animeSlug}) na URL: ${animeFireUrl}`);

        // Adicionar timeout e opções avançadas para o fetch
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 segundos de timeout

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          },
          signal: controller.signal,
          cache: 'no-store',
          next: { revalidate: 0 }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`API retornou status ${response.status}: ${response.statusText}`);
        }

        const data: AnimeFireResponse = await response.json();

        if (data.response.status !== '200') {
          throw new Error(`API retornou erro: ${data.response.text}`);
        }

        // Buscar os episódios existentes no banco de dados
        const existingEpisodes = await prisma.episode.findMany({
          where: { animeId },
          select: { id: true, number: true }
        });

        const existingEpisodeNumbers = existingEpisodes.map(ep => ep.number);

        console.log(`Anime ${animeTitle}: ${existingEpisodes.length} episódios existentes, números: [${existingEpisodeNumbers.join(', ')}]`);

        // Verificar quais episódios estão faltando
        const episodesToAdd = [];

        for (const ep of data.episodes) {
          // Se o episódio não existe no banco de dados, adicionar à lista
          if (!existingEpisodeNumbers.includes(ep.episode)) {
            episodesToAdd.push(ep);
          }
        }

        // Ordenar por número do episódio
        episodesToAdd.sort((a, b) => a.episode - b.episode);

        console.log(`Anime ${animeTitle}: ${existingEpisodes.length} episódios existentes, ${data.episodes.length} episódios encontrados, ${episodesToAdd.length} episódios para adicionar`);

        if (episodesToAdd.length === 0) {
          return {
            animeId,
            animeTitle,
            animeSlug,
            currentEpisodes: existingEpisodes.length,
            newEpisodes: 0,
            episodesAdded: 0,
            success: true
          };
        }

        // Adicionar os novos episódios
        let addedCount = 0;

        // Processar episódios em lotes para evitar sobrecarga
        const batchSize = 3;

        for (let i = 0; i < episodesToAdd.length; i += batchSize) {
          const batch = episodesToAdd.slice(i, i + batchSize);

          const episodePromises = batch.map(async (ep) => {
            try {
              // Escolher a melhor qualidade disponível (preferência: 720p)
              const hdVideo = ep.data.find(d => d.resolution === '720p' && d.status === 'ONLINE')
              const sdVideo = ep.data.find(d => d.resolution === '360p' && d.status === 'ONLINE')
              const fhdVideo = ep.data.find(d => d.resolution === '1080p' && d.status === 'ONLINE')

              const videoUrl = hdVideo?.url || sdVideo?.url || fhdVideo?.url || ''

              // Adicionar o episódio diretamente usando Prisma
              await prisma.episode.create({
                data: {
                  animeId: animeId,
                  number: ep.episode,
                  title: `Episódio ${ep.episode}`,
                  airDate: new Date(),
                  frame: data.anime_image || '',
                  videoUrl: videoUrl,
                  sourceType: 'direct_mp4'
                }
              });

              addedCount++;
              return true;
            } catch (error) {
              console.error(`Erro ao adicionar episódio ${ep.episode}:`, error);
              return false;
            }
          });

          await Promise.all(episodePromises);

          // Pequena pausa entre os lotes para evitar sobrecarga
          if (i + batchSize < episodesToAdd.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        return {
          animeId,
          animeTitle,
          animeSlug,
          currentEpisodes: existingEpisodes.length,
          newEpisodes: episodesToAdd.length,
          episodesAdded: addedCount,
          success: true
        };
      } catch (error) {
        console.error(`Erro ao verificar atualizações para ${animeTitle}:`, error);
        return {
          animeId,
          animeTitle,
          animeSlug,
          currentEpisodes: currentEpisodeCount,
          newEpisodes: 0,
          episodesAdded: 0,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }

    // Se um ID específico for fornecido, verificar apenas esse anime
    if (animeId) {
      const anime = await prisma.anime.findUnique({
        where: { id: animeId },
        include: { episodes: true }
      })

      if (!anime) {
        return NextResponse.json(
          { error: 'Anime não encontrado' },
          { status: 404 }
        )
      }

      // Verificar atualizações para este anime
      const result = await checkAnimeUpdates(
        anime.id,
        anime.title,
        anime.slug || '',
        anime.episodes.length
      )

      // Se novos episódios foram adicionados, atualizar o totalEpisodes do anime
      if (result.episodesAdded > 0) {
        // Buscar o número total de episódios após a atualização
        const updatedEpisodeCount = await prisma.episode.count({
          where: { animeId: anime.id }
        })

        await prisma.anime.update({
          where: { id: anime.id },
          data: {
            totalEpisodes: updatedEpisodeCount
          }
        })
      }

      return NextResponse.json({
        results: [result],
        totalChecked: 1,
        totalUpdated: result.episodesAdded > 0 ? 1 : 0,
        totalEpisodesAdded: result.episodesAdded
      })
    }

    // Caso contrário, verificar todos os animes em lançamento
    const animes = await prisma.anime.findMany({
      where: { status: 'Em Lançamento' },
      include: { episodes: true }
    })

    console.log(`Verificando atualizações para ${animes.length} animes em lançamento`)

    // Verificar atualizações para cada anime
    const results = []
    let totalUpdated = 0
    let totalEpisodesAdded = 0

    // Processar animes em lotes para evitar sobrecarga
    const batchSize = 3
    for (let i = 0; i < animes.length; i += batchSize) {
      const batch = animes.slice(i, i + batchSize)

      const batchPromises = batch.map(async (anime) => {
        const result = await checkAnimeUpdates(
          anime.id,
          anime.title,
          anime.slug || '',
          anime.episodes.length
        )

        // Se novos episódios foram adicionados, atualizar o totalEpisodes do anime
        if (result.episodesAdded > 0) {
          // Buscar o número total de episódios após a atualização
          const updatedEpisodeCount = await prisma.episode.count({
            where: { animeId: anime.id }
          })

          await prisma.anime.update({
            where: { id: anime.id },
            data: {
              totalEpisodes: updatedEpisodeCount
            }
          })

          totalUpdated++
          totalEpisodesAdded += result.episodesAdded
        }

        return result
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Pequena pausa entre os lotes para evitar sobrecarga
      if (i + batchSize < animes.length) {
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }

    return NextResponse.json({
      results,
      totalChecked: animes.length,
      totalUpdated,
      totalEpisodesAdded
    })
  } catch (error) {
    console.error('Erro ao verificar atualizações de animes:', error)
    return NextResponse.json(
      { error: 'Erro ao verificar atualizações: ' + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    )
  }
}
